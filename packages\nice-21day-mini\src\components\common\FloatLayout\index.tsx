import { ScrollView, Text, View } from '@tarojs/components';
import { CommonEvent } from '@tarojs/components/types/common';
import React from 'react';
// import './index.scss';

interface IFloatLayoutState {
  _isOpened: boolean;
}

interface IFloatLayoutProps {
  isOpened: boolean;
  title: string;
  scrollWithAnimation?: boolean;
  showClose?: boolean;
  onClose?: () => void;
  className?: string;
}

export default class FloatLayout extends React.Component<
  IFloatLayoutProps,
  IFloatLayoutState
> {
  public static defaultProps: IFloatLayoutProps;
  public constructor(props: IFloatLayoutProps) {
    super(props);

    const { isOpened } = props;
    this.state = {
      _isOpened: isOpened,
    };
  }

  public UNSAFE_componentWillReceiveProps(nextProps: IFloatLayoutProps): void {
    const { isOpened } = nextProps;

    if (isOpened !== this.state._isOpened) {
      this.setState({
        _isOpened: isOpened,
      });
    }
  }

  private handleClose = (): void => {
    this.props.onClose();
  };

  private close = (): void => {
    this.handleClose();
  };

  private handleTouchMove = (e: CommonEvent): void => {
    e.stopPropagation();
  };

  public render(): JSX.Element {
    const { _isOpened } = this.state;
    const { title, scrollWithAnimation, showClose } = this.props;

    return (
      <View
        className={`at-float-layout ${
          _isOpened ? 'at-float-layout--active' : ''
        }`}
        onTouchMove={this.handleTouchMove}
      >
        <View className="at-float-layout__overlay" />
        <View className="at-float-layout__container layout">
          {title ? (
            <View className="layout-header">
              <Text className="layout-header__title">{title}</Text>
              {showClose && (
                <View
                  className="layout-header__btn-close"
                  onClick={this.close}
                />
              )}
            </View>
          ) : null}
          <View className="layout-body">
            <ScrollView
              scrollWithAnimation={scrollWithAnimation}
              className="layout-body__content"
            >
              {this.props.children}
            </ScrollView>
          </View>
        </View>
      </View>
    );
  }
}

FloatLayout.defaultProps = {
  title: '',
  isOpened: false,
  scrollWithAnimation: false,
  showClose: true,
};
