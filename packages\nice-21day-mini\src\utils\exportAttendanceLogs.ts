import { IExportAttendanceLogsParams } from '@nice-people/nice-21day-shared';
import Taro from '@tarojs/taro';
import { getAuthorization } from '.';
import { getUrl } from './request';

const showError = () => {
  Taro.hideLoading();
  Taro.showModal({
    title: '提示',
    content: '打卡记录导出失败',
    showCancel: false,
    confirmText: '我知道了',
  });
};

/**
 * 导出用户打卡记录
 */
export const exportAttendanceLogs = (params: IExportAttendanceLogsParams) => {
  Taro.showModal({
    title: '提示',
    content: '确定导出所有的打卡记录？',
    success: (confirmRes) => {
      // 开始正式导出
      if (confirmRes.confirm) {
        Taro.showLoading({ title: '导出中....' });

        // 下载临时文件
        let url = getUrl(
          `/user-attendance-logs/export-files?user_id=${params.user_id}`,
        );
        if (params.training_id) {
          url += `&training_id=${params.training_id}`;
        }
        Taro.downloadFile({
          url,
          header: {
            ...getAuthorization(),
          },
          success: (downloadRes) => {
            const manage = Taro.getFileSystemManager();
            if (downloadRes.statusCode === 200) {
              // 保存临时文件
              manage.saveFile({
                tempFilePath: downloadRes.tempFilePath,
                success: (saveRes) => {
                  Taro.hideLoading();
                  // 打开文档
                  Taro.showModal({
                    title: '导出成功',
                    content: '是否打开文档？',
                    success: (res) => {
                      if (res.confirm) {
                        Taro.openDocument({
                          filePath: saveRes.savedFilePath,
                          showMenu: true,
                        });
                      }
                    },
                    fail: () => {
                      showError();
                    },
                  });
                },
              });
            } else {
              showError();
            }
          },
        });
      }
    },
  });
};
