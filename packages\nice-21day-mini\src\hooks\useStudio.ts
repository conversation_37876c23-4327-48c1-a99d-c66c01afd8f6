import { getStudioDetail } from '@/service';
import { IStudio } from '@nice-people/nice-21day-shared';
import { useCallback, useEffect, useState } from 'react';

/**
 * 索取音频创作详情内容
 * @param studioId 音频创作ID
 * @returns
 */
export const useStudioDetail = (studioId: string) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [studioDetail, setStudioDetail] = useState<IStudio>();

  const fetchStudioDetail = useCallback(
    (showLoading: boolean = true) => {
      if (showLoading) {
        setLoading(true);
      }
      getStudioDetail(studioId)
        .then(({ data }) => {
          setStudioDetail(data || ({} as IStudio));
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [studioId],
  );

  useEffect(() => {
    fetchStudioDetail();
  }, [fetchStudioDetail]);

  return {
    studioDetail,
    fetchStudioDetail,
    loading,
  };
};
