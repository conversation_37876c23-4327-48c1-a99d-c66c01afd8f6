import { AppContext } from '@/appContext';
import {
  getMyTrainingList,
  getTrainingDetail,
  IQueryMyTrainingParams,
  refreshMyTrainingScore,
} from '@/service';
import { ITraining } from '@nice-people/nice-21day-shared';
import { useCallback, useContext, useEffect, useState } from 'react';

/**
 * 获取训练营详情
 * @param trainingId 训练营ID
 * @returns
 */
export const useTraining = (trainingId: string) => {
  const [queryTrainingLoading, setQueryTrainingLoading] =
    useState<boolean>(true);
  const [trainingDetail, setTrainingDetail] = useState<ITraining>(
    {} as ITraining,
  );

  useEffect(() => {
    setQueryTrainingLoading(true);
    getTrainingDetail(trainingId)
      .then(({ data }) => {
        setTrainingDetail(data);
      })
      .finally(() => {
        setQueryTrainingLoading(false);
      });
  }, [trainingId]);

  return {
    trainingDetail,
    queryTrainingLoading,
  };
};

/**
 * 查询和我有关系的训练营
 */
export const useMyTrainingList = (params: IQueryMyTrainingParams) => {
  const { currentUser } = useContext(AppContext);
  const [loading, setLoading] = useState<boolean>(true);
  const [myTrainingList, setMyTrainingList] = useState<ITraining[]>([]);

  const fetchMyTrainingList = useCallback(
    (query: IQueryMyTrainingParams & { showLoading?: boolean }) => {
      const { showLoading = true, ...rest } = query;
      if (showLoading) {
        setLoading(true);
      }
      getMyTrainingList({
        page: 1,
        size: 100,
        ...rest,
      })
        .then(({ data }) => {
          setMyTrainingList(data.rows);
        })
        .catch(() => {
          setMyTrainingList([]);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [],
  );

  useEffect(() => {
    // 检测是否登录了
    if (!currentUser?.id) {
      setLoading(false);
      return;
    }
    fetchMyTrainingList(params);
  }, [JSON.stringify(params), currentUser]);

  return {
    myTrainingList,
    fetchMyTrainingList,
    loading,
  };
};

/**
 * 刷新我的积分
 */
export const useMyTrainingScore = (trainingId: string) => {
  const refreshMyScore = useCallback(() => {
    return refreshMyTrainingScore(trainingId);
  }, [trainingId]);

  return {
    refreshMyScore,
  };
};
