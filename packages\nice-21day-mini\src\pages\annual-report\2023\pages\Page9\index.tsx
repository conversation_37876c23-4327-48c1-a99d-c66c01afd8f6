import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useState } from 'react';
import { AtInput } from 'taro-ui';
import { REPORT_2023_KEYWORD_LOCAL_KEY } from '../../contants';
import { IPageProps } from '../../type';
import { wordList } from './contant';
import styles from './index.module.scss';
import RandomKeyword from './RandomKeyword';

export default ({ reportData, isActive }: IPageProps) => {
  const [keyword, setKeyword] = useState<string | undefined | null>(() =>
    Taro.getStorageSync(REPORT_2023_KEYWORD_LOCAL_KEY),
  );

  const updateKeyword = (val: string) => {
    setKeyword(val);
    Taro.setStorageSync(REPORT_2023_KEYWORD_LOCAL_KEY, val);
  };

  const handleRefreshKeyword = useCallback(() => {
    const restWordList = wordList.filter((el) => el !== keyword);
    const randomIndex = Math.floor(Math.random() * restWordList.length);
    updateKeyword(restWordList[randomIndex]);
  }, [keyword]);

  return (
    <View className={`${styles.container} flex flex-col justify-between`}>
      <View className="w-full flex flex-col items-center">
        <View className={styles.titleContainer}>
          <View className={styles.title}>
            <View>你眼中的自己是什么</View>
            <View>样子的呢？</View>
          </View>
          <View className={styles.subTitle}>为自己设置一个年度关键词吧！</View>
        </View>

        <View className={styles.inputContainer}>
          <AtInput
            className={styles.input}
            placeholder="请输入年度关键词"
            placeholderStyle="color: #fff"
            name="keyword"
            type="text"
            value={keyword}
            maxlength={10}
            border={false}
            onChange={updateKeyword}
          />
          <View
            className={`at-icon at-icon-reload ${styles.refresh}}`}
            onClick={handleRefreshKeyword}
          />
          <View className="text-[24px] mt-3">支持自定义输入，10个字以内</View>
        </View>
      </View>

      <RandomKeyword />
    </View>
  );
};
