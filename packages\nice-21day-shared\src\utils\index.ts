import {
  ADMIN_DOMAIN,
  ALI_OSS_DOMAIN,
  ALI_OSS_FILE_PREFIX,
  ATTENDANCE_LIMIT_MORNING_HOURS,
} from '../constants';
import {
  EAttendanceState,
  EBooleanString,
  IAttendanceLog,
  IAttendanceTask,
  ITraining,
} from '../types';

import dayjs, { Dayjs } from 'dayjs';

/**
 * 解析对象json
 * @param jsonString json字符串
 */
export const parseObjJson = <T>(jsonString: string) => {
  let res = <T>{};
  try {
    res = JSON.parse(jsonString);
  } catch (error) {}
  return res;
};

/**
 * 解析数组json
 * @param jsonString json字符串
 */
export const parseArrayJson = <T>(jsonString: string) => {
  let res = <T[]>[];
  try {
    res = JSON.parse(jsonString);
  } catch (error) {}
  return res;
};

/**
 * 获取对象的 keys 数组
 * @param obj 对象
 */
export const getObjectKeys = <T extends object>(obj: T) => {
  return Object.keys(obj) as Array<keyof T>;
};

// TODO: 之后全局替换该方法，换成 getFileUrl
/**
 * 获取图片真实的完整路径
 * @param path
 * @returns
 */
export const getFileUrl = (path?: string) => {
  if (!path) {
    return '';
  }
  if (path.startsWith('http')) {
    return path;
  }
  // 忽略 base64
  if (path.startsWith('data:image')) {
    return path;
  }

  // 如果是 alioss,
  if (path.includes(ALI_OSS_FILE_PREFIX)) {
    return ALI_OSS_DOMAIN + path;
  }

  return `${ADMIN_DOMAIN}${path}`;
};

/**
 * 根据文件后缀来区分是图片还是音频
 */
export const filterFiles = (files: string[]) => {
  const imageList: string[] = [];
  const audioList: string[] = [];
  // 现在音频的后缀是有限的
  const audioSuffix = ['mpeg', 'mp3'];

  files.forEach((filePath) => {
    const items = filePath.split('.');
    const fileSuffix = items[items.length - 1];
    if (audioSuffix.includes(fileSuffix)) {
      audioList.push(filePath);
    } else {
      imageList.push(filePath);
    }
  });

  return {
    imageList,
    audioList,
  };
};

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const uuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    let v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

/**
 *  数字格式化，添加千分位
 * @param num 数字
 * @returns
 */
export const formatNumber = (num: number) => {
  return num.toLocaleString();
};

/**
 * 计算用户在某个训练营下的总积分
 * @param attendanceLogs 用户在训练营下的所有打卡记录
 * @param training 训练营信息
 */
export const computedTrainingUserTotalScore = (
  attendanceLogs: IAttendanceLog[],
  training: ITraining,
) => {
  let score = 0;
  attendanceLogs.forEach((log) => {
    // 请假的不加分
    if (log.attendance_state === EAttendanceState.Attendance) {
      score += computedAttendanceLogScore(log);
    }
  });

  let reached = EBooleanString.YES;
  // 同时对比一下达标情况
  const { standard_score = 0 } = training;
  if (score >= standard_score) {
    reached = EBooleanString.YES;
  } else {
    reached = EBooleanString.NO;
  }

  return {
    score,
    reached,
  };
};

/**
 * 计算一次打卡记录的得分情况
 * @param attendanceLog 打卡记录
 */
export const computedAttendanceLogScore = (attendanceLog: IAttendanceLog) => {
  // 请假不加分
  if (attendanceLog.attendance_state === EAttendanceState.Leave) {
    return 0;
  }
  const attendanceTasks = parseArrayJson<IAttendanceTask>(
    attendanceLog.attendance_tasks,
  );

  const invalidItems = attendanceTasks
    // 周任务不参与计算积分
    .filter((task) => !isWeeklyTask(task?.name))
    .filter(
      (task) => !task.attendance_content && task.attendance_files?.length === 0,
    );
  // 如果存在部分子项未完成，也就是说部分打卡，那积分+(1 - Math.max(1, 未完成打卡数 * 0.5)
  // 部分打卡，扣分封顶为1分
  return 1 - Math.min(1, invalidItems.length * 0.5);
};

/**
 * 判断是否为周任务
 * @param taskName 任务名称
 */
export const isWeeklyTask = (taskName: string) => {
  return taskName?.includes('每周') || taskName?.includes('周任务');
};

const dateFormate = (date: Date | string | number | Dayjs) =>
  dayjs(date).format('YYYY-MM-DD');

/**
 * 是否是当天的打卡
 * @param attendanceDate 打卡日期
 * @param createAt 打卡提交日期
 */
export const isTodayAttendance = (
  attendanceDate: string,
  createAt: Date | string,
) => {
  const today = dayjs().format('YYYY-MM-DD');
  const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
  const createHours = +dayjs(createAt).format('H');

  // 打卡日期为今天
  if (attendanceDate === today) {
    return true;
  }
  // 打卡日期为昨天，但是提交时间在今天的凌晨 4 点之内
  if (
    attendanceDate === yesterday &&
    dateFormate(createAt) === today &&
    createHours < ATTENDANCE_LIMIT_MORNING_HOURS
  ) {
    return true;
  }
  return false;
};

/**
 * 计算训练营持续的总天数
 * @param startTime 训练营开始日期
 * @param endTime 训练营结束日期
 * @returns 训练营一共有x天
 */
export const trainingTotalDaysCount = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) {
    return 1;
  }
  return dayjs(endTime).diff(dayjs(startTime), 'day') + 1;
};

/**
 * 计算今天是开始打卡的第几天
 * @param startTime 训练营开始日期
 */
export const trainingStartAttendanceDaysCount = (startTime: string) => {
  if (!startTime) {
    return 0;
  }
  return dayjs().diff(dayjs(startTime), 'day') + 1;
};

/**
 * 计算训练营打卡进度
 * @param startTime 训练营开始日期
 * @param endTime 训练营结束日期
 * @returns 打卡总进度百分比
 */
export const trainingAttendanceProgress = (
  startTime: string,
  endTime: string,
) => {
  const totalDaysCount = trainingTotalDaysCount(startTime, endTime);
  // 今天是打卡的第几天
  const startDaysCount = trainingStartAttendanceDaysCount(startTime);
  // 对于超过100%的，最大只返回100%
  return Math.min(Math.trunc((startDaysCount / totalDaysCount) * 100), 100);
};

/**
 * 从 HTML 中提取纯文本
 * @param html 包含 HTML 标签的字符串
 * @returns 纯文本字符串
 */
export const extractText = (html?: string) => {
  if (!html) {
    return '';
  }
  // 移除所有 HTML 标签
  let text = html.replace(/<[^>]*>/g, '');

  // 处理特殊字符实体
  text = text
    .replace(/&nbsp;/g, ' ')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");

  // 压缩连续的空白字符
  text = text.replace(/\s+/g, ' ').trim();

  return text;
};
