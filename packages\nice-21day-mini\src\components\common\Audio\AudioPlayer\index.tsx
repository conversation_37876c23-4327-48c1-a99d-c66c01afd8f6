// 这里放音频播放相关的代码
import { Bubble, CircleProgress } from '@/components';
import { taroInnerAudioContextManager } from '@/utils';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import { ITouchEvent, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { formatAudioTime } from './help';
import './index.scss';

const IconFlowStaticPath = getFileUrl(
  '/alioss/2023-04-08/f61b0a30-c04a-410f-a7e6-2d5688f72916.png',
);

const IconFlowGifPath = getFileUrl(
  '/alioss/2023-04-11/16207ac7-bf83-48b5-9b84-8db897500159.gif',
);
interface IAudioPlayerProps {
  /** 音频 src */
  src?: string;
  /** 是否在录音中 */
  isRecording?: boolean;
  /** 是否展示为圆形播放 */
  circle?: boolean;
  /** 圆形大小 */
  circleSize?: number;
  /** 一一般的圆角还是全圆 */
  circleType?: 'full' | 'default';
  /** 背景图 */
  circleImage?: string;
  /** 音频总时间长，单位为秒 */
  totalDuration?: number;
  /** class 类名 */
  className?: string;
}

// 播放状态
export enum EPlayerStatus {
  PLAY,
  STOP,
  PAUSE,
  ERROR,
  ENDED,
}
const AudioPlayer: React.FC<IAudioPlayerProps> = ({
  src = '',
  isRecording = false,
  circle = false,
  circleSize = 50,
  circleType = 'default',
  circleImage,
  totalDuration = 0,
  className,
}) => {
  const timeoutRef = useRef<any>();
  /** 是否正在播放标志 */
  const [isPlay, setIsPlay] = useState(false);
  // 音频当前进度，秒
  const [curTime, setCurTime] = useState(0);

  const innerAudioContext =
    taroInnerAudioContextManager.getTaroInnerAudioContext();
  const [audioContextIns] = useState(() => innerAudioContext); // 音频实例持久化

  const [audioStatus, setAudioStatus] = useState<EPlayerStatus>(
    EPlayerStatus.STOP,
  );
  audioContextIns.onTimeUpdate(() => {
    setCurTime(audioContextIns.currentTime);
  });

  audioContextIns.onPlay(() => {
    taroInnerAudioContextManager.savePlaying(audioContextIns);
    setIsPlay(true);
    setAudioStatus(EPlayerStatus.PLAY);
  });
  const setToEnd = (type: EPlayerStatus, e?: any) => {
    if (e?.errMsg) {
      Taro.showToast({
        title: e.errMsg,
      });
      // 有待商榷
      setAudioStatus(EPlayerStatus.ENDED);
    } else {
      setAudioStatus(type);
    }
    console.log('设置停止');
    setIsPlay(false);
  };

  innerAudioContext.onStop(setToEnd.bind(null, EPlayerStatus.STOP));
  innerAudioContext.onPause(setToEnd.bind(null, EPlayerStatus.PAUSE));
  innerAudioContext.onError(setToEnd.bind(null, EPlayerStatus.STOP));
  innerAudioContext.onEnded(setToEnd.bind(null, EPlayerStatus.ENDED));

  useEffect(() => {
    if (!isRecording) {
      clearIntervalRef();
      return;
    }

    timeoutRef.current = setInterval(() => {
      setCurTime((prev) => (prev += 1));
    }, 1000);

    return () => {
      clearIntervalRef();
    };
  }, [isRecording]);

  const clearIntervalRef = useCallback(() => {
    if (timeoutRef.current) {
      clearInterval(timeoutRef.current);
    }
  }, []);

  const playAudio = useCallback(() => {
    if (src) {
      console.log('开始播放录音---');
      audioContextIns.src = getFileUrl(src);
      audioContextIns.play();
    }
  }, [src, audioContextIns]);

  const handleClickAudio = useCallback(
    (event: ITouchEvent) => {
      event.preventDefault();
      event.stopPropagation();

      // 正在录音中，不可以播放
      if (isRecording) {
        return;
      }

      if (isPlay) {
        stopAudio();
      } else {
        playAudio();
      }
    },
    [isPlay, isRecording],
  );

  const stopAudio = useCallback(() => {
    console.log('触发停止');
    audioContextIns.pause();
    audioContextIns.stop();
  }, [audioContextIns]);

  useEffect(() => {
    // 播放中，又开始录音，需要停止播放
    if (isRecording && audioStatus === EPlayerStatus.PLAY) {
      stopAudio();
    }
  }, [isRecording, audioStatus, stopAudio]);

  useEffect(() => {
    return () => {
      audioContextIns.destroy();
    };
  }, []);

  if (circle) {
    // 播放进度
    let percent = audioContextIns.duration
      ? audioContextIns.currentTime / audioContextIns.duration
      : 0;
    // 当数值比较小（时间少）的时候，误差非常大
    if (audioContextIns.currentTime && audioStatus === EPlayerStatus.ENDED) {
      percent = 1;
    }

    return (
      <View
        onClick={handleClickAudio}
        className="flex items-center justify-center relative overflow-hidden audio-player-circle"
        style={{
          backgroundImage: 'linear-gradient(to right, #10b981, #6b7280)',
          width: circleSize,
          height: circleSize,
          borderRadius: circleType === 'full' ? '100%' : '4px',
          // 解决 overflow-hidden 失效的问题
          transform: 'rotate(0deg)',
        }}
      >
        {/* bg */}
        {circleImage && (
          <View
            className={`absolute w-full h-full circling ${
              audioStatus === EPlayerStatus.PLAY ? 'running' : ''
            }`}
            style={{
              backgroundImage: `url(${getFileUrl(circleImage)})`,
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
            }}
          />
        )}
        <View
          className={`at-icon z-1 at-icon-${
            isPlay ? 'pause' : 'play ml-1'
          } text-light-50 ${circleSize >= 100 ? 'text-6xl' : 'text-2xl'}`}
        />
        <CircleProgress percent={percent} />
        {isPlay && <Bubble key={src} />}
      </View>
    );
  }

  return (
    <View
      onClick={handleClickAudio}
      className={`audio-player-pannel h-76rpx rounded-lg flex items-center justify-between gap-2 w-full box-border ${className}`}
    >
      {/* 左侧播放按钮 */}
      <View className="h-60rpx w-60rpx flex flex-shrink-0 items-center justify-center bg-white rounded-full">
        <View
          className={`at-icon at-icon-${
            isPlay ? 'pause' : 'play ml-0.5'
          } text-green-500 text-1xl`}
        />
      </View>

      {/* 中间播放波浪 */}
      <View
        className="flex-1 h-20rpx flex"
        style={{
          backgroundImage: `url(${
            isPlay || isRecording ? IconFlowGifPath : IconFlowStaticPath
          })`,
        }}
      />
      {/* 右侧播放进度时间 */}
      <View className="flex-shrink-0">
        <Text
          className={`ml-10rpx w-${totalDuration ? '60' : '30'}rpx text-white`}
        >
          {formatAudioTime(curTime)}{' '}
          {totalDuration ? `/ ${formatAudioTime(totalDuration)}` : ''}
        </Text>
      </View>
    </View>
  );
};

export default memo(AudioPlayer);
