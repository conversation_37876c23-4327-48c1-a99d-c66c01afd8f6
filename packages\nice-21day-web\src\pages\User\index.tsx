import { UserInfo } from '@/components/UserInfo';
import { PRO_TABLE_DEFAULT_CONFIG } from '@/constants';
import {
  changeUserStatus,
  queryUsers,
  updateUserAccessToken,
} from '@/services';
import { generateProTableValueEnum } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  EState,
  IUser,
  STATE_MAPPING,
  USER_GENDER_MAPPING,
} from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import { Button, message, Popconfirm, Space } from 'antd';
import { copy } from 'clipboard';
import React, { useRef } from 'react';

const User: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const changeType = (id: string, state: EState) => {
    changeUserStatus(id, state).then(() => {
      tableRef.current?.reload();
    });
  };
  const renderUserState = (text: string, id: string, state: EState) => (
    <Popconfirm
      key={`popconfirm_${text}`}
      title={`确认${text}吗?`}
      okText="是"
      cancelText="否"
      onConfirm={() => {
        changeType(id, state);
      }}
    >
      <Button type="link">{text}</Button>
    </Popconfirm>
  );
  const columns: ProColumns<IUser>[] = [
    {
      title: '用户',
      dataIndex: 'nick_name',
      ellipsis: true,
      renderText: (text, record) => {
        return <UserInfo {...record} />;
      },
      fixed: 'left',
      width: 200,
    },
    {
      title: '生日',
      dataIndex: 'birthday',
      width: 120,
      valueType: 'date',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      valueEnum: generateProTableValueEnum(USER_GENDER_MAPPING),
    },
    {
      title: '公司',
      dataIndex: 'company',
      ellipsis: true,
    },
    // {
    //   title: '提供内推',
    //   dataIndex: 'internal_referral_state',
    //   valueEnum: {
    //     [EBooleanString.YES]: {
    //       text: '是',
    //       status: 'Success',
    //     },
    //     [EBooleanString.NO]: {
    //       text: '否',
    //       status: 'error',
    //     },
    //   },
    // },
    // {
    //   title: '内推公司',
    //   dataIndex: 'internal_referral_companies',
    //   ellipsis: true,
    // },
    // {
    //   title: '恋爱状况',
    //   dataIndex: 'love_state',
    //   valueEnum: generateProTableValueEnum(USER_LOVE_STATE_MAPPING),
    // },
    {
      title: 'access token',
      dataIndex: 'access_token',
      render: (_, record) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                copy(record.access_token);
                message.success('复制成功');
              }}
            >
              复制
            </Button>
            <Popconfirm
              title="确认要重置吗？"
              okText="是"
              okButtonProps={{ danger: true }}
              cancelText="否"
              onConfirm={() => {
                updateUserAccessToken(record.id).then(() => {
                  tableRef.current?.reload();
                  message.success('操作成功');
                });
              }}
            >
              <Button type="link">重置</Button>
            </Popconfirm>
          </Space>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueEnum: generateProTableValueEnum(STATE_MAPPING),
      width: 80,
    },
    // {
    //   title: '创建时间',
    //   dataIndex: 'created_at',
    //   valueType: 'dateTime',
    //   search: false,
    //   width: 180,
    // },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      search: false,
      width: 100,
      render: (_, record) => {
        let state =
          record.state === EState.Enable
            ? renderUserState('禁用', record.id, EState.Disable)
            : renderUserState('启用', record.id, EState.Enable);
        return (
          <Space>
            {state}

            <Button
              key="profile"
              type="link"
              onClick={() => history.push(`/admin/user/${record.id}`)}
            >
              详情
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable<IUser>
      headerTitle="注册用户列表"
      rowKey="id"
      actionRef={tableRef}
      columns={columns}
      {...PRO_TABLE_DEFAULT_CONFIG}
      search={{
        span: 6,
        labelWidth: 70,
      }}
      request={async ({ pageSize, current, ...rest }) => {
        const res = await queryUsers({
          ...rest,
          size: pageSize!,
          page: current!,
        });
        return {
          data: res.rows,
          total: res.total,
          success: true,
        };
      }}
    />
  );
};

export default User;
