.container {
  height: 100%;
  width: 100%;
  background-color: #27d1ff;

  .cover {
    background-image: url('https://oss.yayujs.com/alioss/2023-12-23/bd851921-1525-4e60-80ce-4db59e4fef6a.png');
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    position: relative;
    top: 0;
    left: 0;
    height: 1334px;
    width: 750px;
    visibility: visible;

    & > * {
      visibility: visible;
    }
  }
}

.keyword {
  color: #0f59a1;
  font-feature-settings: 'clig' off, 'liga' off;
  font-size: 50px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;

  position: absolute;
  width: 100%;
  text-align: center;
}

.content {
  position: absolute;
  width: 574px;

  color: #3770a7;
  font-feature-settings: 'clig' off, 'liga' off;
  font-size: 44px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  padding-left: 100px;
}

.actions {
  position: absolute;
  bottom: 40px;
  display: flex;
  width: 80%;
  font-size: 32px;
  justify-content: space-between;
  z-index: 20;

  .btn {
    width: 48%;
    text-align: center;
    height: 100px;
    line-height: 100px;
    border-radius: 100px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  .viewAgain {
    border: 1px solid #fff;
    background: rgb(255, 255, 255);
    color: #0f59a1;
  }

  .share {
    border: 1px solid #fff;

    background: linear-gradient(
      90deg,
      rgb(69, 179, 255) 0.08%,
      rgb(182, 243, 255) 99.91%
    );
  }
}

.contentText {
  font-weight: normal;
  font-family: Glow Sans SC;
}
.contentNumber {
  display: inline-block;
  color: #0f59a1;
  font-size: 60px;
  font-family: DINPro-Medium;
}
