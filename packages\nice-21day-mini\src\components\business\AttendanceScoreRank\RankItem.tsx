import {
  ETrainingProgress,
  ITraining,
  ITrainingMember,
  getFileUrl,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { AtListItem } from 'taro-ui';
import { AtListProps } from 'taro-ui/types/list';
import './index.scss';

const ScoreRankItem = ({
  trainingUser,
  training,
  hasBorder = true,
  isMySelf = false,
}: {
  trainingUser: ITrainingMember;
  training: ITraining;
  isMySelf?: boolean;
} & AtListProps) => {
  // 是否已达标
  const isStorard = +trainingUser.score >= +training.standard_score;
  // 是否提交了总结
  const isSummary = trainingUser.summary;

  return (
    <View className="score-ranking-item">
      {isStorard && (
        <View className="storard-flag">
          <View className="triangle" />
          <View className="at-icon at-icon-check" />
        </View>
      )}
      {isSummary && <View className="summary-flag">已总结</View>}
      <AtListItem
        className={`${isStorard ? 'storard' : 'not-storard'}`}
        key={trainingUser.id}
        thumb={getFileUrl(trainingUser.user?.avatar_url)}
        title={`${trainingUser.user?.nick_name} ${isMySelf ? '[我自己]' : ''}`}
        note={`获赞${trainingUser.like_count || 0}次，打卡${
          trainingUser.total_attendance_count
        }天，连续打卡${trainingUser.max_consecutive_attendance_count}天${
          trainingUser.score_updated_at
            ? `\n积分更新于 ${dayjs(trainingUser.score_updated_at).format(
                'YYYY-MM-DD HH:mm:ss',
              )}`
            : ''
        }`}
        extraText={`${trainingUser.score}`}
        arrow="right"
        hasBorder={hasBorder}
        onClick={() => {
          // 训练营茶话会阶段或已结束，跳转到总结页面
          if (
            training.progress === ETrainingProgress.Summary ||
            training.progress === ETrainingProgress.Finished
          ) {
            Taro.navigateTo({
              url: `/pages/user-training-summary/index?training_id=${training.id}&user_id=${trainingUser.user_id}`,
            });
            return;
          }
          Taro.navigateTo({
            url: `/pages/analytics/user-attendance-analysis/index?training_id=${training.id}&user_id=${trainingUser.user_id}&from=ranking`,
          });
        }}
      />
    </View>
  );
};

export default ScoreRankItem;
