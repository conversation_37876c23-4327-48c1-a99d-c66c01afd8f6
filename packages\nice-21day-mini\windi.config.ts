// @ts-check - enable TS check for js file

import { defineConfig } from 'windicss/helpers';
import lineClamp from 'windicss/plugin/line-clamp';

export default defineConfig({
  preflight: false, // 禁止引入 reset 样式
  extract: {
    // 忽略部分文件夹
    exclude: ['node_modules', '.git', 'dist'],
  },
  theme: {
    extend: {
      colors: {
        red: {
          DEFAULT: '#ED1B26',
          dark: '#94171D',
        },
        blue: '#276EF1',
        brown: '#99644C',
        green: {
          DEFAULT: '#219653',
          dark: '#21531C',
        },
        orange: '#FB6939',
        purple: '#7356BF',
        yellow: '#EEAB27',
        black: {
          DEFAULT: '#161616',
          light: '#363636',
          pure: '#000000',
        },
        gray: {
          DEFAULT: '#222222',
          pressed: '#2b2b2b',
          subtitle: '#757575',
          description: '#AFAFAF',
          skeleton: '#2c2c2c',
          indicator: '#353535',
        },
        white: '#FFFFFF',
        background: '#282828',
        border: 'rgba(117, 117, 117, 0.1)',
      },
      spacing: {
        half: '50%',
        '7.5': '1.875rem',
        '22': '5.375rem',
      },
      borderRadius: {
        none: '0',
        sm: '0.125rem',
        DEFAULT: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        full: '9999px',
        large: '12px',
      },
    },
  },
  corePlugins: {
    // 禁用掉在小程序环境中不可能用到的 plugins
    container: false,
  },
  plugins: [lineClamp],
});
