import { IRichTextRef, RichText } from '@/components/RichText';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { getwxacode, updateMyTrainingSummary } from '@/services';
import { WarningOutlined } from '@ant-design/icons';
import { ETrainingProgress, uuid } from '@nice-people/nice-21day-shared';
import { useOutletContext } from '@umijs/max';
import { useRequest } from 'ahooks';
import {
  Alert,
  Button,
  Card,
  message,
  Modal,
  notification,
  Result,
} from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FloatButtonWrapper } from '../../components/FloatButtonWrapper';
import { IUserLayoutContext } from '../UserLayout';
import './index.less';

const EmptySummary = () => (
  <Result
    status="info"
    subTitle={
      <div className="text-center">
        <div>这位朋友有点懒惰</div>
        <div>还没有提交训练营总结</div>
      </div>
    }
  />
);
export default () => {
  const editorRef = useRef<IRichTextRef | null>(null);
  const { trainingUser, refreshTrainingUserDetail } =
    useOutletContext<IUserLayoutContext>();

  const [submitting, setSubmitting] = useState(false);
  // 编辑态
  const [editing, setEditing] = useState(false);
  // 设置一个key，让编辑器重新刷新
  const [editorKey, setEditorKey] = useState<string>(() => uuid());

  const { currentUser } = useCurrentUser();

  const canEdit = useMemo(() => {
    return (
      currentUser.id === trainingUser.user_id &&
      trainingUser.training?.progress === ETrainingProgress.Summary
    );
  }, [currentUser, trainingUser]);

  const { training } = trainingUser || {};

  // 当 editing 变成 true时， 页面滚动定位到 Alert 组件
  const alertRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (editing) {
      alertRef.current?.scrollIntoView({
        behavior: 'smooth',
      });
    }
  }, [editing]);

  const { data: imgUrl, loading: qrCodeLoading } = useRequest(
    () =>
      getwxacode({
        path: `pages/user-training-summary/index?training_id=${training?.id}&user_id=${trainingUser.user_id}`,
      }),
    {
      ready: Boolean(training?.id),
      refreshDeps: [training?.id],
    },
  );

  useEffect(() => {
    console.log('summary changed');
    setEditorKey(uuid());
  }, [trainingUser?.summary]);

  const handleSave = useCallback(async () => {
    const summary = editorRef.current?.editor?.getHtml() || '';
    // 判断文本长度
    if (summary?.length < 200 || summary?.length > 10000) {
      notification.warning({
        message: '保存失败',
        description: '训练营总结内容长度在 [200,10000] 之间',
      });
      return;
    }

    Modal.confirm({
      title: '确认保存吗？',
      onOk: () => {
        setSubmitting(true);
        updateMyTrainingSummary(training!.id!, summary)
          .then(() => {
            // 刷新数据
            refreshTrainingUserDetail();

            message.success('保存成功');
            setEditing(false);

            // 弹窗提示分享
            Modal.success({
              title: '保存成功',
              content: (
                <div className="flex flex-col gap-2 items-center">
                  <img width={160} src={imgUrl} />
                  <div>快去分享给好友吧！</div>
                </div>
              ),
              onOk() {},
            });
          })
          .catch(() => {
            message.error('保存失败');
          })
          .finally(() => {
            setSubmitting(false);
          });
      },
    });
  }, [training?.id, imgUrl, qrCodeLoading]);

  if (!training?.id) {
    return <Result status="error" subTitle="训练营成员不存在" />;
  }

  // 训练营阶段
  if (training.progress === ETrainingProgress.Summary) {
    if (editing) {
      return (
        <>
          <div ref={alertRef}>
            <Alert
              type="warning"
              showIcon
              className="mb-3"
              icon={<WarningOutlined className="text-amber-700" />}
              message={<div className="text-amber-700">温馨提示</div>}
              description={
                <div className="text-sm text-amber-700 space-y-1">
                  <p>• 当前页面没有自动保存功能，请注意及时保存内容</p>
                  <p>
                    • 建议先在备忘录或文档应用中编辑好内容，再粘贴到此处发布
                  </p>
                  <p>• 避免长时间编辑导致意外丢失文字内容</p>
                </div>
              }
            />
          </div>

          <div className="pb-[100px]">
            <RichText
              key={`${editorKey}--edit`}
              ref={editorRef}
              className="min-h-[400px] h-[600px]"
              initValue={trainingUser?.summary || ''}
              placeholder="分享一下你在这21天训练营中的收获和感悟...<br/><br/>💡 小贴士：可以从以下几个方面来写：<br/>• 训练过程中遇到的挑战和解决方法<br/>• 坚持下来的心得体会<br/>• 对习惯养成的新认识<br/>• 对未来的规划和期待"
            />
          </div>
          <FloatButtonWrapper>
            <div className="flex gap-2">
              <div className="w-[60%]">
                <Button
                  block
                  key="save-btn"
                  size="large"
                  className="!h-[50px] flex-1 bg-gradient-to-r !from-blue-500 !to-indigo-600 rounded-xl !hover:from-blue-600 !hover:to-indigo-700 transition-all"
                  type="primary"
                  loading={submitting}
                  onClick={handleSave}
                >
                  保存总结
                </Button>
              </div>
              <div className="flex-1">
                <Button
                  block
                  size="large"
                  className="!h-[50px]"
                  loading={submitting}
                  onClick={() => {
                    Modal.confirm({
                      title: '确认取消吗？',
                      content: '取消后编辑过的内容将会丢失',
                      onOk: () => {
                        setEditing(false);
                      },
                    });
                  }}
                >
                  取消
                </Button>
              </div>
            </div>
          </FloatButtonWrapper>
        </>
      );
    }

    // 没有填写过，显示空状态
    if (!trainingUser?.summary) {
      return (
        <>
          <EmptySummary />
          {canEdit && (
            <div className="flex items-center justify-center">
              <Button
                size="large"
                className="w-[200px] bg-gradient-to-r !h-[50px] !from-blue-500 !to-indigo-600 rounded-xl !hover:from-blue-600 !hover:to-indigo-700 transition-all"
                type="primary"
                loading={submitting}
                onClick={() => setEditing(true)}
              >
                编辑
              </Button>
            </div>
          )}
        </>
      );
    }

    // 填写过，显示为预览
    return (
      <>
        {canEdit && (
          <FloatButtonWrapper>
            <Button
              size="large"
              className="bg-gradient-to-r !h-[50px] !from-blue-500 !to-indigo-600 rounded-xl !hover:from-blue-600 !hover:to-indigo-700 transition-all"
              block
              type="primary"
              loading={submitting}
              onClick={() => setEditing(true)}
            >
              编辑
            </Button>
          </FloatButtonWrapper>
        )}
        <div className="pb-[100px]">
          <RichText
            key={`${editorKey}--preview`}
            readOnly
            initValue={trainingUser?.summary || ''}
          />
        </div>
      </>
    );
  }

  // 已经结束了，显示只读状态
  if (training.progress === ETrainingProgress.Finished) {
    if (!trainingUser?.summary) {
      return <EmptySummary />;
    }
    return (
      <>
        <RichText readOnly initValue={trainingUser?.summary || ''} />
      </>
    );
  }

  return (
    <Card>
      <Result status="info" subTitle="还没有到总结阶段" />
    </Card>
  );
};
