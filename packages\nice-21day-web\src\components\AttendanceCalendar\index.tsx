import Calendar from '@/components/Calendar';
import AttendanceLogProfile from '@/pages/AttendanceLog/components/AttendanceLogProfile';
import {
  CheckCircleTwoTone,
  FrownTwoTone,
  MinusCircleTwoTone,
} from '@ant-design/icons';
import {
  EAttendanceState,
  IAttendanceCalendar,
  IAttendanceLog,
} from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import { Modal, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useMemo } from 'react';

interface IUserAttendanceCalendarProps {
  calendarData: IAttendanceCalendar;
  onSelect?: (date: Dayjs) => void;
}
const UserAttendanceCalendar: React.FC<IUserAttendanceCalendarProps> = ({
  calendarData,
  onSelect,
}) => {
  const attendanceLogMap: Record<string, IAttendanceLog> = useMemo(() => {
    return (calendarData?.attendanceLogs || []).reduce(
      (prev, item) => ({
        ...prev,
        [item.attendance_date]: item,
      }),
      {},
    );
  }, calendarData?.attendanceLogs);

  const displayAttendance = (log: IAttendanceLog) => {
    Modal.confirm({
      title: '打卡详情',
      width: '80%',
      okText: '关闭',
      cancelText: '编辑打卡',
      cancelButtonProps: {
        onClick: () => {
          Modal.destroyAll();
          history.push(`/admin/attendance-log/${log.id}/update`);
        },
      },
      content: <AttendanceLogProfile attendanceLog={log} />,
    });
  };

  const handleDateCellRender = useCallback(
    (value: Dayjs) => {
      const date = dayjs(value).format('YYYY-MM-DD');
      const attendanceLog = attendanceLogMap[date];
      if (!attendanceLog) {
        return (
          <Tooltip title="点击去打卡">
            <MinusCircleTwoTone
              style={{ fontSize: 20 }}
              twoToneColor="#ddd"
              onClick={() => onSelect?.(value)}
            />
          </Tooltip>
        );
      }
      if (attendanceLog.attendance_state === EAttendanceState.Attendance) {
        return (
          <Tooltip title="点击查看打卡详情">
            <CheckCircleTwoTone
              style={{ fontSize: 20 }}
              twoToneColor="#52c41a"
              onClick={() => displayAttendance(attendanceLog)}
            />
          </Tooltip>
        );
      }
      if (attendanceLog.attendance_state === EAttendanceState.Leave) {
        return (
          <Tooltip title="点击查看请假详情">
            <FrownTwoTone
              style={{ fontSize: 20 }}
              twoToneColor="#eb2f96"
              onClick={() => displayAttendance(attendanceLog)}
            />
          </Tooltip>
        );
      }
    },
    [attendanceLogMap],
  );

  return (
    <>
      <Calendar
        style={{ width: '100%' }}
        fullscreen={false}
        disabledDate={(date) => {
          if (dayjs(date).isBefore(calendarData.training.start_time)) {
            return true;
          }
          if (dayjs(date).isAfter(calendarData.training.end_time)) {
            return true;
          }
          return false;
        }}
        headerRender={undefined}
        defaultValue={dayjs(calendarData.training.start_time)}
        dateCellRender={handleDateCellRender}
      />
    </>
  );
};

export default UserAttendanceCalendar;
