import { AppContext } from '@/appContext';
import {
  Avatar,
  Card,
  FabBox,
  Result,
  RichText,
  TrainingLayout,
} from '@/components';
import { useRouterParams, useTrainingUser, useUserCall } from '@/hooks';
import { updateMyTrainingSummary } from '@/service';
import {
  ETrainingProgress,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';
import { ScrollView, View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { AtButton, AtLoadMore, AtNoticebar, AtTextarea } from 'taro-ui';
import './index.scss';

const SUMMARY_MIN = 200;
const SUMMARY_MAX = 10000;

const getSummaryWebUrl = (trainingMember: ITrainingMember) => {
  return `https://nice.yayujs.com/training/${trainingMember.training_id}/user/${trainingMember.user_id}/summary`;
};

interface IGuideNoticeProps {
  trainingMember: ITrainingMember;
}

const GuideNotice = ({ trainingMember }: IGuideNoticeProps) => {
  return (
    <AtNoticebar
      single
      showMore
      moreText="查看Web"
      onGotoMore={() => {
        Taro.setClipboardData({
          data: getSummaryWebUrl(trainingMember),
        });
      }}
    >
      使用 web 网页编写总结可以获取完整的富文本编辑能力。
    </AtNoticebar>
  );
};

const UserTrainingSummary: React.FC = () => {
  const { training_id, user_id } = useRouterParams();
  const { currentUser } = useContext(AppContext);

  // 是否在编辑中
  const [newSummary, setNewSummary] = useState<string>('');
  const [editing, setEditing] = useState<boolean>(false);

  const {
    queryTrainingUser,
    trainingUserDetail,
    queryTrainingUserDetailLoading,
  } = useTrainingUser(training_id, user_id);

  useEffect(() => {
    setNewSummary(trainingUserDetail.summary);
  }, [trainingUserDetail.summary]);

  const { callPrefix } = useUserCall(currentUser, user_id);

  useShareAppMessage(() => {
    return {
      title: `${trainingUserDetail?.user?.nick_name}在${trainingUserDetail?.training?.name}的训练营总结`,
      path: `/pages/user-training-summary/index?training_id=${training_id}&user_id=${user_id}`,
    };
  });

  const menus = useMemo(() => {
    return [
      {
        title: `${callPrefix}报名`,
        url: `/pages/training-member-detail/index?member_id=${trainingUserDetail?.id}`,
      },
      {
        title: `${callPrefix}打卡`,
        url: `/pages/attendance-timeline-user/index?training_id=${training_id}&user_id=${user_id}`,
      },
      {
        title: `${callPrefix}统计`,
        url: `/pages/analytics/user-attendance-analysis/index?training_id=${training_id}&user_id=${user_id}`,
      },
    ];
  }, [callPrefix, trainingUserDetail?.id, training_id, user_id]);

  const saveSummary = useCallback(() => {
    if (newSummary.length < SUMMARY_MIN || newSummary.length > SUMMARY_MAX) {
      Taro.showModal({
        title: '提示',
        content: `训练营总结不能少于${SUMMARY_MIN}个字，不能超过${SUMMARY_MAX}个字`,
        showCancel: false,
        confirmText: '知道了',
      });
      return;
    }

    Taro.showModal({
      title: '确认保存训练营总结吗？',
      success: function (res) {
        if (res.confirm) {
          Taro.showLoading();
          updateMyTrainingSummary(training_id, newSummary)
            .then(() => {
              queryTrainingUser();
              setEditing(false);
              Taro.showToast({
                title: '保存成功',
                icon: 'success',
              });
            })
            .catch(() => {
              Taro.showToast({
                title: '保存失败',
                icon: 'error',
              });
            })
            .finally(() => {
              Taro.hideLoading();
            });
        }
      },
    });
  }, [training_id, newSummary, queryTrainingUser]);

  const renderActions = useCallback(() => {
    if (!trainingUserDetail || !currentUser) {
      return;
    }
    const { training, user } = trainingUserDetail;
    if (!training || !user) {
      return;
    }

    // 茶话会中，我自己可以编辑 + 分享
    if (
      training.progress === ETrainingProgress.Summary &&
      user_id === currentUser.id
    ) {
      if (editing) {
        return (
          <FabBox>
            <View className="flex flex-row gap-4">
              <AtButton
                circle
                type="primary"
                className="danger-button flex-1"
                onClick={() => {
                  setEditing(false);
                  setNewSummary(trainingUserDetail.summary);
                }}
              >
                <View className="flex items-center gap-1">
                  <View className="at-icon at-icon-close" />
                  取消
                </View>
              </AtButton>
              <AtButton
                type="primary"
                className="w-2/3"
                circle
                onClick={() => {
                  saveSummary();
                }}
              >
                <View className="flex items-center gap-1">
                  <View className="at-icon at-icon-upload" />
                  保存内容
                </View>
              </AtButton>
            </View>
          </FabBox>
        );
      }

      return (
        <FabBox>
          <View className="flex flex-row gap-4">
            <AtButton type="primary" className="w-half" circle openType="share">
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-share-2" />
                转发分享
              </View>
            </AtButton>

            <AtButton
              type="primary"
              className="w-half"
              circle
              onClick={() => {
                // 检查是否是富文本
                if (trainingUserDetail.summary.includes('<p>')) {
                  Taro.showModal({
                    title: '提示',
                    content:
                      '检测到已有总结内容存在HTML标签，请使用 Web 网页进行操作',
                    showCancel: false,
                    confirmText: '复制链接',
                    success: () => {
                      Taro.setClipboardData({
                        data: getSummaryWebUrl(trainingUserDetail),
                      });
                    },
                  });
                  return;
                }

                setEditing(true);
              }}
            >
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-edit" />
                编辑总结
              </View>
            </AtButton>
          </View>
        </FabBox>
      );
    }

    return (
      <FabBox>
        <AtButton type="primary" className="w-full" circle openType="share">
          <View className="flex items-center gap-1">
            <View className="at-icon at-icon-share-2" />
            转发分享
          </View>
        </AtButton>
      </FabBox>
    );
  }, [trainingUserDetail, currentUser, user_id, editing, saveSummary]);

  const renderSummaryContent = useCallback(() => {
    if (editing) {
      return (
        <View>
          <AtTextarea
            value={newSummary}
            onChange={(val) => {
              setNewSummary(val);
            }}
            maxLength={SUMMARY_MAX}
            height={600}
            placeholder={`请输入训练营总结，最少${SUMMARY_MIN}个字，最多${SUMMARY_MAX}个字`}
          />
        </View>
      );
    }

    // 为空
    if (!trainingUserDetail.summary) {
      return (
        <Result
          text={
            <View className="text-center">
              <View>这位朋友有点懒惰</View>
              <View>还没有提交训练营总结</View>
            </View>
          }
        />
      );
    }

    return (
      <ScrollView className="pb-160px">
        <Card>
          <RichText content={trainingUserDetail.summary} />
        </Card>
      </ScrollView>
    );
  }, [trainingUserDetail.summary, editing, newSummary]);

  if (!training_id || !user_id) {
    return <Result text="参数错误" />;
  }

  if (queryTrainingUserDetailLoading) {
    return <AtLoadMore status="loading" />;
  }

  if (!trainingUserDetail.training?.id) {
    return <Result text="训练营不存在或已被删除" />;
  }

  const { training, user } = trainingUserDetail;

  return (
    <View>
      <TrainingLayout
        training={training}
        stickyTitle
        title={
          <>
            <View className="flex gap-2 items-center mb-2.5 justify-center flex-col">
              <Avatar
                avatar={user?.avatar_url}
                userName={user?.nick_name}
                layout="horizontal"
                size="small"
                link
                onClick={() => {
                  const userId = user?.id;
                  if (userId) {
                    Taro.navigateTo({
                      url: `/pages/user-homepage/index?user_id=${userId}`,
                    });
                  }
                }}
              />
            </View>
            {/* 快捷按钮 */}
            <View className="flex gap-2">
              {menus.map((menu) => (
                <AtButton
                  className="flex-1"
                  key={menu.title}
                  size="small"
                  type="primary"
                  onClick={() => {
                    Taro.navigateTo({
                      url: menu.url,
                    });
                  }}
                >
                  <View className="flex items-center justify-center gap-0.5">
                    {menu.title}
                    <View className="at-icon at-icon-chevron-right" />
                  </View>
                </AtButton>
              ))}
            </View>
          </>
        }
      >
        {/* 训练营总结 */}
        <GuideNotice trainingMember={trainingUserDetail} />
        <View>{renderSummaryContent()}</View>
      </TrainingLayout>

      {renderActions()}
    </View>
  );
};

export default UserTrainingSummary;
