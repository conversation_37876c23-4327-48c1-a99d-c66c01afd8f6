import { ACCESS_TOKEN_LOCAL_KEY, LEETCODE_QUESTIONS_KEY } from '@/constant';
import { LeetcodeQuestionMap } from '@/pages/leetcode-weekly/typings';
import { IUploadFile, parseObjJson } from '@nice-people/nice-21day-shared';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { getUrl } from './request';

export { getFileUrl } from '@nice-people/nice-21day-shared';
export * from './taroInnerAudioContextManager';

/**
 * 获取当前页url
 */
export const getCurrentPageUrl = () => {
  let pages = Taro.getCurrentPages() || [];
  let currentPage = pages[pages.length - 1];
  // 不一定能取到
  let url = currentPage?.route;
  return url;
};

export const pageToLogin = () => {
  Taro.clearStorage();
};

//todo：跳转到登录之前的页面
export const pageToBeforeLogin = () => {
  Taro.redirectTo({
    url: '/pages/index/index',
  });
};

/** 返回当前日期（格式：YYYY-MM-DD) */
export const getNowDate = () => {
  return dayjs().format('YYYY-MM-DD');
};

/** 获取权限头部 */
export const getAuthorization = () => {
  return {
    Authorization: `Bearer ${Taro.getStorageSync(ACCESS_TOKEN_LOCAL_KEY)}`,
  };
};

/**
 * 获取本地缓存的力扣问题
 */
export const getLocalLeetcodeQuestion = () => {
  const data = Taro.getStorageSync(LEETCODE_QUESTIONS_KEY);
  if (data) {
    return parseObjJson<LeetcodeQuestionMap>(data);
  }
  return undefined;
};

/**
 * 将题目字符串解析成纯净的题目数组
 * @param questionIdText 题目合集，例如 [258][237][206][169][70]
 * @returns 题目ID数组 ['258', '237', '206', '169', '70']
 */
export const getLeetcodeFrontendQuestionIds = (
  questionIdText?: string,
): string[] => {
  if (!questionIdText) {
    return [];
  }
  // 解析
  // return questionIdText.match(/(?<=\[)(.+?)(?=\])/g) || [];
  return [];
};

/**
 * 获取某个日期所在的ISO周数
 * @param date 日期 YYYY-MM-DD
 * @returns
 */
export const getISOWeekNumber = (date: string) => dayjs(date).isoWeek();

/**
 * 获取某个日期所在ISO周的起止日期
 * @param {string} date YYYY-MM-DD
 * @returns 日期列表
 */
export const getWeekStartAndEnd = (date: string) => {
  // 当前周的星期一
  const startDate = dayjs(date).startOf('isoWeek').format('YYYY-MM-DD');

  const dateList: string[] = [];
  let index = 0;
  while (index < 7) {
    dateList.push(dayjs(startDate).add(index, 'day').format('YYYY-MM-DD'));
    index += 1;
  }

  return dateList;
};

/**
 * Taro 上传
 * @param tempFilePath 临时文件地址
 */
export const taroUpload = (tempFilePath: string): Promise<IUploadFile> => {
  return new Promise((resolve, reject) => {
    Taro.showLoading({
      title: `正在上传`,
    });
    Taro.uploadFile({
      url: `${getUrl('/upload/alioss')}`,
      filePath: tempFilePath,
      header: {
        ...getAuthorization(),
      },
      name: 'file',
      success: ({ data }) => {
        const file = parseObjJson<IUploadFile>(data);
        // 保护一下，避免意外的上传错误
        // 例如 nginx 转发错误
        if (Object.keys(file).length === 0 || !file?.path) {
          Taro.showToast({
            title: '上传失败，请稍后重试~',
            icon: 'none',
            duration: 2000,
          });
          return false;
        }

        resolve(file);
      },
      fail: () => {
        Taro.showToast({
          title: '上传失败，请稍后重试~',
          icon: 'none',
          duration: 2000,
        });
        reject();
      },
      complete: () => {
        Taro.showToast({
          title: '上传成功',
          icon: 'success',
          duration: 2000,
        });
        Taro.hideLoading();
      },
    });
  });
};

/**
 * 随机整数
 * @param min
 * @param max
 */
export function getRandomIntInclusive(min: number, max: number) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min; //含最大值，含最小值
}

/** 是否含有交集 */
export const isIntersection = (arr1: string[], arr2: string[]) =>
  arr1.length + arr2.length !== new Set([...arr1, ...arr2]).size;

/**
 * 获取星座
 * @param birthday 生日 YYYY-MM-DD
 * @see https://gist.github.com/solodxg/4324726
 */
export function getStarSign(birthday: string) {
  if (!birthday) {
    return;
  }
  const [_, month, day] = birthday.split('-').map((el) => +el);

  var s = '魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯';
  var d = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22];
  var i = month * 2 - (day < d[month - 1] ? 2 : 0);
  return s.substring(i, i + 2) + '座';
}

/**
 * 获取年龄标签
 * @param birthday 生日 YYYY-MM-DD
 */
export function getUserAgeText(birthday?: string) {
  let text = '';
  if (!birthday) {
    return text;
  }
  const [year] = birthday.split('-').map((el) => +el);

  if (year >= 2000) {
    text = '00 后';
  } else if (year >= 1990) {
    text = '90 后';
  } else if (year >= 1980) {
    text = '80 后';
  }

  text += ' ' + getStarSign(birthday);
  return text;
}

export const DEFAULT_AVATAR =
  'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
export const DEFAULT_NICK_NAME = '微信用户';

/**
 * 检查用户信息是否需要更新
 * @returns Boolean true-合法、false 不合法
 */
export const validateWechatInfo = (userInfo: {
  avatar: string;
  nickName: string;
}) => {
  const { avatar = '', nickName = '' } = userInfo;
  return (
    avatar.length > 0 &&
    avatar !== DEFAULT_AVATAR &&
    nickName.length > 0 &&
    nickName !== DEFAULT_NICK_NAME
  );
};
