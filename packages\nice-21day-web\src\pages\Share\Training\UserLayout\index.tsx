import { useCurrentUser } from '@/hooks/useCurrentUser';
import ScoreRankItem from '@/pages/Share/components/ScoreRankItem';
import { getTrainingMemberDetailByUserId } from '@/services';
import { PageContainer } from '@ant-design/pro-components';
import { ITrainingMember } from '@nice-people/nice-21day-shared';
import { history, Outlet, useLocation, useParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Card, List, Result, Spin } from 'antd';
import React, { useCallback, useState } from 'react';
import styles from './index.module.less';

export interface IUserLayoutContext {
  trainingUser: ITrainingMember;
  refreshTrainingUserDetail: () => Promise<void>;
}

const tabList = [
  {
    key: 'attendance-timeline',
    tab: '打卡记录',
  },
  {
    key: 'summary',
    tab: '训练营总结',
  },
];

const UserTrainingLayout: React.FC = () => {
  const { training_id, user_id } = useParams();
  const location = useLocation();
  const { currentUser } = useCurrentUser();

  const getTabKey = useCallback(() => {
    const url = `/training/${training_id}/user/${user_id}`;
    const tabKey = location.pathname.replace(`${url}/`, '');
    if (tabKey && tabKey !== '/') {
      return tabKey;
    }

    return '';
  }, [location, training_id]);

  const handleTabChange = (key: string) => {
    history.push(`/training/${training_id}/user/${user_id}/${key}`);
  };

  const [loading, setLoading] = useState(true);

  const { data: trainingUserDetail, refresh: refreshTrainingUserDetail } =
    useRequest(() => getTrainingMemberDetailByUserId(training_id!, user_id!), {
      ready: Boolean(training_id) && Boolean(user_id),
      refreshDeps: [training_id, user_id],
      onFinally: () => {
        setLoading(false);
      },
    });

  if (loading) {
    return (
      <div className="py-4 flex items-center justify-center">
        <Spin />
      </div>
    );
  }

  if (!trainingUserDetail?.id) {
    return (
      <Card>
        <Result status="warning" subTitle="当前用户没有参加这个训练营" />
      </Card>
    );
  }

  return (
    <>
      <PageContainer
        className={styles.layout}
        breadcrumb={undefined}
        extra={[]}
        tabList={tabList}
        tabActiveKey={getTabKey()}
        onTabChange={handleTabChange}
        childrenContentStyle={{
          padding: 0,
        }}
        content={
          <List
            itemLayout="horizontal"
            dataSource={[trainingUserDetail]}
            loading={loading}
            renderItem={(item) => (
              <ScoreRankItem
                trainingUser={item}
                training={item.training}
                isMySelf={item.user?.id === currentUser?.id}
                showActions={false}
                refresh={refreshTrainingUserDetail}
              />
            )}
          />
        }
      >
        <Outlet
          context={{
            trainingUser: trainingUserDetail,
            refreshTrainingUserDetail,
          }}
        />
      </PageContainer>
    </>
  );
};

export default UserTrainingLayout;
