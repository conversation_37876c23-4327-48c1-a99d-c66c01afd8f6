import { Result, TrainingLayout } from '@/components';
import AttendanceLikeRank from '@/components/business/AttendanceLikeRank';
import AttendanceScoreRank from '@/components/business/AttendanceScoreRank';
import { useRouterParams, useTraining } from '@/hooks';
import { View } from '@tarojs/components';
import { useShareAppMessage } from '@tarojs/taro';
import { useState } from 'react';
import { AtLoadMore, AtTabs, AtTabsPane } from 'taro-ui';
import './index.scss';

enum RankType {
  'ScoreRank',
  'LikeRank',
}

const LeaderBoard = () => {
  const { training_id, rank = RankType.ScoreRank } = useRouterParams();
  const [tabIndex, setTabIndex] = useState(Number(rank));

  const { trainingDetail, queryTrainingLoading } = useTraining(training_id);

  const rankList = [
    {
      title: '积分排行榜',
    },
    {
      title: '点赞排行榜（Top20）',
    },
  ];

  useShareAppMessage(() => {
    return {
      title: `${trainingDetail?.name}${
        rankList[tabIndex]?.title || '打卡排行榜'
      }`,
      path: `/pages/attendance-leader-board/index?training_id=${training_id}&rank=${tabIndex}`,
    };
  });

  const handleChangeRankType = (val: number) => {
    setTabIndex(val);
  };

  if (queryTrainingLoading) {
    return <AtLoadMore status="loading" />;
  }

  if (!trainingDetail?.id) {
    return <Result text="训练营不存在或已被删除" />;
  }

  return (
    <View className="ranking-page">
      <TrainingLayout training={trainingDetail} title="">
        <View className="-mt-8">
          <AtTabs
            current={tabIndex}
            tabList={rankList}
            onClick={handleChangeRankType}
          >
            <AtTabsPane current={tabIndex} index={RankType.ScoreRank}>
              <AttendanceScoreRank training={trainingDetail} />
            </AtTabsPane>
            <AtTabsPane current={tabIndex} index={RankType.LikeRank}>
              <AttendanceLikeRank training={trainingDetail} />
            </AtTabsPane>
          </AtTabs>
        </View>
      </TrainingLayout>
    </View>
  );
};

export default LeaderBoard;
