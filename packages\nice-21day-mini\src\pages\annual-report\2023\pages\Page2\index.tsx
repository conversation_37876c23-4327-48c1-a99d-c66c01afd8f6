import { Image, View } from '@tarojs/components';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import HighlightWrapper from '../../components/HighlightWrapper';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

export default ({ user, reportData, isActive }: IPageProps) => {
  const { regDays } = useMemo(() => {
    if (!reportData.user) {
      return {
        regDate: '',
        regDays: 0,
      };
    }
    const _regDate = dayjs(reportData.user.created_at).format('YYYY-MM-DD');
    const _regDays = dayjs().diff(dayjs(reportData.user.created_at), 'days');

    return {
      regDate: _regDate,
      regDays: _regDays,
    };
  }, [reportData.user?.created_at]);

  return (
    <View className={`${styles.container} flex justify-center items-center`}>
      <View className="absolute z-1 top-40px right-[-150px]">
        <Image
          src={require('./assets/planet.svg')}
          className="w-[676px] h-[554px]"
        />
      </View>
      <View className="absolute z-1 left-24px bottom-136px">
        <Image
          src={require('./assets/circle.svg')}
          className="w-[334px] h-[334px]"
        />
      </View>

      {reportData.user ? (
        <>
          <View className="report-2023__content absolute z-2 top-[380px] text-[#fff]">
            <View>你好啊</View>
            <View>
              <View className=" inline-block text-[52px]">
                <HighlightWrapper
                  active={isActive}
                  data={`${user.nick_name}`}
                />
              </View>
            </View>
            <View>还记得吗</View>
            <View>
              <HighlightWrapper
                active={isActive}
                data={dayjs(reportData.user.created_at).format('YYYY')}
              />{' '}
              年{' '}
              <HighlightWrapper
                active={isActive}
                data={dayjs(reportData.user.created_at).format('MM')}
              />{' '}
              月
              <HighlightWrapper
                active={isActive}
                data={dayjs(reportData.user.created_at).format('DD')}
              />{' '}
              日
            </View>
            <View>你第一次打开了好青年小程序</View>
            <View>不知不觉</View>
            <View>
              我们已经一起走过了{' '}
              <HighlightWrapper active={isActive} data={regDays} /> 天
            </View>
          </View>
          <View className="report-2023__content--desc absolute bottom-[168rpx] right-[92rpx] text-[#fff]">
            <View>有你一路相伴</View>
            <View>我感到无比幸运</View>
          </View>
        </>
      ) : (
        <>
          <View className="report-2023__content z-2 text-[#fff]">
            <View>
              <View className="report-2023__highlight--number inline-block">
                2023
              </View>{' '}
              年我们之间没有故事
            </View>
            <View>
              让我们一起相约{' '}
              <View className="report-2023__highlight--number inline-block">
                2024
              </View>{' '}
              年
            </View>
          </View>
        </>
      )}
    </View>
  );
};
