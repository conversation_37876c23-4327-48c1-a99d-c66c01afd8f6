import getTokenGuideImage from '@/assets/get-token-guide.png';
import wxappLoginPageImage from '@/assets/wxapp-login-page.png';
import Feedback from '@/components/Feedback';
import { ACCESS_TOKEN_LOCAL_KEY, LOGO_URL, SLOGAN } from '@/constants';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { userLogin } from '@/services';
import {
  ArrowRightOutlined,
  QuestionCircleOutlined,
  SafetyCertificateOutlined,
  UserAddOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import { Avatar, Image, message, Spin } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

const Login: React.FC = () => {
  // 从 url 中获取 query 参数
  const query = new URLSearchParams(window.location.search);
  const tab = query.get('tab');

  const [activeTab, setActiveTab] = useState<
    'tokenLogin' | 'wechatLogin' | 'register'
  >(() => (tab === 'register' ? 'register' : 'tokenLogin'));

  const [loading, setLoading] = useState(false);

  const [token, setToken] = useState('');

  const { currentUser } = useCurrentUser();

  const [getTokenGuideImageVisible, setGetTokenGuideImageVisible] =
    useState(false);

  useEffect(() => {
    if (currentUser.role === 'user') {
      message.success('当前已登录');
      history.replace('/user');
    }
  }, [currentUser]);

  const handleLogin = useCallback(() => {
    if (!token) {
      message.error('请输入授权码');
      return;
    }

    setLoading(true);
    userLogin({ access_token: token })
      .then(({ access_token }) => {
        // 记录token
        localStorage.setItem(ACCESS_TOKEN_LOCAL_KEY, access_token);
        // 强制重刷一次
        window.location.href = '/user';
      })
      .catch(({ response }) => {
        message.error(response?.data?.message ?? '登录错误');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [token]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-3">
      <div className="w-full max-w-md">
        {/* 头部 */}
        <div className="text-center mb-8">
          <Avatar className="w-20 h-20 mb-4" src={LOGO_URL} />
          <h1 className="text-2xl font-bold text-gray-800 mb-2">21天训练营</h1>
          <p className="text-gray-600">{SLOGAN}</p>
        </div>

        {/* 登录卡片 */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          {/* 登录方式切换 */}
          <div className="flex bg-gray-100 rounded-xl p-1 mb-6">
            <button
              type="button"
              onClick={() => setActiveTab('tokenLogin')}
              className={`flex-1 flex items-center justify-center py-3 px-3 rounded-lg transition-all ${
                activeTab === 'tokenLogin'
                  ? 'bg-white shadow-sm text-blue-600'
                  : 'text-gray-600'
              }`}
            >
              <SafetyCertificateOutlined className="text-lg mr-2" />
              授权码登录
            </button>
            {/* <button
              type="button"
              onClick={() => setActiveTab('wechat')}
              className={`flex-1 flex items-center justify-center py-3 px-4 rounded-lg transition-all ${
                activeTab === 'wechat'
                  ? 'bg-white shadow-sm text-blue-600'
                  : 'text-gray-600'
              }`}
            >
              <QrcodeOutlined className="text-lg mr-2" />
              小程序登录
            </button> */}
            <button
              type="button"
              onClick={() => setActiveTab('register')}
              className={`flex-1 flex items-center justify-center py-3 px-3 rounded-lg transition-all ${
                activeTab === 'register'
                  ? 'bg-white shadow-sm text-blue-600'
                  : 'text-gray-600'
              }`}
            >
              <UserAddOutlined className="w-4 h-4 mr-1" />
              微信注册
            </button>
          </div>

          {/* 登录表单 */}
          {activeTab === 'tokenLogin' && (
            <div className="space-y-4">
              <input
                type="password"
                placeholder="请输入授权码"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
              />

              {/* 如何获取授权码 */}
              <div
                className="flex gap-1 items-center text-sm text-gray-400 cursor-pointer"
                onClick={() => setGetTokenGuideImageVisible(true)}
              >
                <QuestionCircleOutlined />
                如何获取授权码
              </div>
            </div>
          )}

          {activeTab === 'wechatLogin' && (
            <div className="text-center pb-4">
              <div className="w-48 h-48 bg-gray-100 text-gray-400 rounded-xl mx-auto mb-4 flex items-center justify-center">
                {/* <QrCode className="w-20 h-20 text-gray-400" /> */}
                功能建设中
              </div>
              <p className="text-gray-600">请使用21天小程序扫描二维码登录</p>
            </div>
          )}

          {activeTab === 'register' && (
            <div className="text-center pb-4">
              <div className="w-48 h-48 bg-gray-100 text-gray-400 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <img src={wxappLoginPageImage} alt="微信扫码注册" />
              </div>
              <p className="text-gray-600">请使用微信扫描二维码进行注册</p>
            </div>
          )}

          {/* 登录按钮 */}
          {activeTab === 'tokenLogin' && (
            <Spin spinning={loading}>
              <button
                type="button"
                onClick={handleLogin}
                className="w-full mt-6 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-4 rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all flex items-center justify-center"
              >
                <span>登录</span>
                <ArrowRightOutlined className="w-4 h-4 ml-2" />
              </button>
            </Spin>
          )}
        </div>
      </div>

      <Image
        style={{ display: 'none' }}
        src={getTokenGuideImage}
        preview={{
          visible: getTokenGuideImageVisible,
          src: getTokenGuideImage,
          onVisibleChange: (value) => {
            setGetTokenGuideImageVisible(value);
          },
        }}
      />

      <Feedback />
    </div>
  );
};

export default Login;
