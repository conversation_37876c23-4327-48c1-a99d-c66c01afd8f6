import { AppContext } from '@/appContext';
import {
  Avatar,
  CustomListItem,
  FabBox,
  Login,
  Result,
  Title,
} from '@/components';
import { usePageShowAgain, useRouterParams, useUserProfile } from '@/hooks';
import {
  EBooleanString,
  USER_GENDER_MAPPING,
  USER_LOVE_STATE_MAPPING,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import dayjs from 'dayjs';
import { useContext, useEffect, useMemo } from 'react';
import { AtButton, AtList, AtLoadMore } from 'taro-ui';

const UserDetail = () => {
  const { currentUser } = useContext(AppContext);
  const { user_id } = useRouterParams();

  const { userProfile, fetchUserProfile, loading } = useUserProfile(user_id);

  usePageShowAgain(() => fetchUserProfile(false));

  const isMyself = useMemo(() => {
    return currentUser?.id === user_id;
  }, [user_id, currentUser?.id]);

  const titlePrefix = useMemo(() => {
    return isMyself ? '我的' : 'TA 的';
  }, [isMyself]);

  useEffect(() => {
    if (isMyself) {
      Taro.showShareMenu({});
    } else {
      Taro.hideShareMenu();
    }
  }, [isMyself]);

  useShareAppMessage(() => {
    return {
      title: `${userProfile?.nick_name}的资料`,
      path: `/pages/user-detail/index?user_id=${user_id}`,
    };
  });

  if (!currentUser?.id) {
    return <Login />;
  }

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  if (!userProfile?.id) {
    return <Result text="未找到人员信息" />;
  }

  return (
    <View className={`${isMyself ? 'pb-16' : ''}`}>
      <Avatar
        avatar={userProfile.avatar_url}
        userName={userProfile.nick_name}
      />

      {/* 个人信息 */}
      <Title title={`${titlePrefix}资料`} />
      <AtList>
        <CustomListItem
          title="性别"
          extraText={USER_GENDER_MAPPING[userProfile.gender]}
        />
        <CustomListItem title="居住地" extraText={userProfile.location} />
        <CustomListItem
          title="生日"
          extraText={
            userProfile.birthday
              ? dayjs(userProfile.birthday).format('MM-DD')
              : ''
          }
        />
        <CustomListItem
          title="个人爱好"
          layout="vertical"
          extraText={userProfile.hobby}
        />
        <CustomListItem
          layout="vertical"
          title="个人介绍"
          extraText={userProfile.about_me}
        />
      </AtList>
      {/* TOOD 个人照片 */}

      {/* 公司信息 */}
      <Title title={`${titlePrefix}公司`} />
      <AtList>
        <CustomListItem title="公司" extraText={userProfile.company} />
        <CustomListItem title="职称" extraText={userProfile.job_title} />
        {/* <CustomListItem title="技能标签" extraText={userProfile.skill_tags} /> */}
      </AtList>

      <Title title={`${titlePrefix}内推`} />
      <AtList>
        {/* 内推信息 */}
        <CustomListItem
          title="提供内推"
          extraText={
            userProfile.internal_referral_state === EBooleanString.YES
              ? '是'
              : '否'
          }
        />
        {/* <CustomListItem
          layout="vertical"
          title="内推公司"
          extraText={userProfile.internal_referral_companies}
        /> */}
        {userProfile.internal_referral_state === EBooleanString.YES && (
          <CustomListItem
            layout="vertical"
            title="内推说明"
            extraText={userProfile.internal_referral_description}
          />
        )}
      </AtList>

      <Title title={`${titlePrefix}社交`} />
      <AtList>
        {/* 社交网网站 */}
        <CustomListItem
          title="个人网站"
          arrow={userProfile.websites ? 'right' : undefined}
          extraText={userProfile.websites}
          onClick={() =>
            Taro.setClipboardData({
              data: userProfile.websites,
            })
          }
        />
        <CustomListItem
          title="微信公众号"
          extraText={userProfile.mp_weixin_name}
        />
        <CustomListItem
          title="Github"
          arrow={userProfile.github_address ? 'right' : undefined}
          extraText={userProfile.github_address}
          onClick={() =>
            Taro.setClipboardData({
              data: userProfile.github_address,
            })
          }
        />
        <CustomListItem
          title="掘金"
          arrow={userProfile.juejin_address ? 'right' : undefined}
          extraText={userProfile.juejin_address}
          onClick={() =>
            Taro.setClipboardData({
              data: userProfile.juejin_address,
            })
          }
        />
        <CustomListItem
          title="知乎"
          arrow={userProfile.zhihu_address ? 'right' : undefined}
          extraText={userProfile.zhihu_address}
          onClick={() =>
            Taro.setClipboardData({
              data: userProfile.zhihu_address,
            })
          }
        />
        <CustomListItem
          title="思否"
          arrow={userProfile.segmentfault_address ? 'right' : undefined}
          extraText={userProfile.segmentfault_address}
          onClick={() =>
            Taro.setClipboardData({
              data: userProfile.segmentfault_address,
            })
          }
        />
      </AtList>

      {/* 恋爱信息 */}
      <Title title={`${titlePrefix}爱情`} />
      <AtList>
        <CustomListItem
          title="恋爱状况"
          extraText={USER_LOVE_STATE_MAPPING[userProfile.love_state]}
        />
        <CustomListItem
          layout="vertical"
          title="恋爱宣言"
          extraText={userProfile.love_expectation}
        />
      </AtList>

      {isMyself && (
        <FabBox>
          <View className="flex flex-row gap-4">
            <AtButton type="primary" className="w-half" circle openType="share">
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-share-2" />
                转发分享
              </View>
            </AtButton>
            <AtButton
              type="primary"
              className="w-half"
              circle
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/my-profile/index`,
                });
              }}
            >
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-edit" />
                修改资料
              </View>
            </AtButton>
          </View>
        </FabBox>
      )}
    </View>
  );
};

export default UserDetail;
