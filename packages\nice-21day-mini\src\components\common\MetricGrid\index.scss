@import '@/custom-theme.scss';

.metrics-wrapper {
  display: flex;
  flex-flow: wrap;
  row-gap: 20px;
  // padding: 30px 20px 20px 20px;
  // border-radius: $border-radius-lg;
  // background: #fff;
  // color: $color-black-1;
  // box-sizing: border-box;
  // box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);

  .metrics {
    text-align: center;
    view:first-child {
      font-size: 28px;
      color: $color-black-2;
    }
    view:last-child {
      font-size: 40px;
      margin-top: 4px;
    }
  }
}
