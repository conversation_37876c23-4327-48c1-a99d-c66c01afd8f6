import { Image, View } from '@tarojs/components';
import HighlightWrapper from '../../components/HighlightWrapper';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

// 小程序上线时间
const APP_LAUNCH_DATE = '2023-03-21';

export default ({ reportData, isActive }: IPageProps) => {
  return (
    <View className={`${styles.container} flex justify-center items-center`}>
      <View className="absolute z-1 right-0 top-40px">
        <Image
          src={require('./assets/speaker.svg')}
          className="w-[600px] h-[400px]"
        />
      </View>
      <View
        className="absolute bottom-200px right-[-100px] w-300px h-300px rounded-full"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.22)' }}
      />

      <View className="report-2023__content absolute top-400px">
        <View>
          小程序在 <HighlightWrapper data={APP_LAUNCH_DATE} />
        </View>
        <View>首次开启线上打卡模式</View>
        <View>截至目前</View>
        <View>
          我们一起度过{' '}
          <HighlightWrapper
            active={isActive}
            data={reportData.global_statistics.training_2023_count}
          />{' '}
          期训练营
        </View>
        <View>在这个过程中我们收获了</View>
        <View>
          <HighlightWrapper
            active={isActive}
            data={reportData.global_statistics.training_join_user_count}
          />{' '}
          人次报名参与
        </View>
        <View>
          <HighlightWrapper
            active={isActive}
            data={reportData.global_statistics.attendance_total_count}
          />{' '}
          人次打卡
        </View>
      </View>
      <View className="report-2023__content--desc absolute bottom-[186rpx] right-[100rpx]">
        <View>一个人走的很快</View>
        <View>一群人走的更远</View>
      </View>
    </View>
  );
};
