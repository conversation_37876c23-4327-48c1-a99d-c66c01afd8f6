import { queryAttendanceLogDetail } from '@/services';
import { useParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Card, Empty, Skeleton } from 'antd';
import { useRef } from 'react';
import { TrainingAttendanceTimeline } from '../../components/AttendanceTimeline';
import AttendanceComments, {
  IAttendanceCommentsRef,
} from '../../components/Comment/AttendanceComments';
import CreateAttendanceComment from '../../components/Comment/CreateAttendanceComment';

export default () => {
  const commentsListRef = useRef<IAttendanceCommentsRef>(null);
  const { attendance_id } = useParams();

  // 获取打卡详情
  const { loading: attendanceDetailLoading, data: attendance } = useRequest(
    async () => queryAttendanceLogDetail(attendance_id!),
    {
      ready: Boolean(attendance_id),
      refreshDeps: [attendance_id],
    },
  );

  if (attendanceDetailLoading) {
    return (
      <Card>
        <Skeleton avatar active />
      </Card>
    );
  }

  if (!attendance) {
    return <Empty />;
  }

  return (
    <>
      <TrainingAttendanceTimeline
        list={[attendance]}
        showDetailButton={false}
      />

      <div className="space-y-3">
        {/* 点赞列表 */}
        {/* <Card
          title="点赞"
          styles={{
            body: {
              padding: 12,
            },
          }}
        ></Card> */}

        {/* 评论列表 */}
        <Card
          title="评论"
          styles={{
            body: {
              padding: 12,
            },
          }}
        >
          {/* 评论列表 */}
          <AttendanceComments
            ref={commentsListRef}
            attendance_id={attendance.id}
          />
          {/* 提交评论 */}
          <CreateAttendanceComment
            attendance_id={attendance.id}
            onFinish={() => {
              commentsListRef.current?.refresh();
              commentsListRef.current?.scrollToTop();
            }}
          />
        </Card>
      </div>
    </>
  );
};
