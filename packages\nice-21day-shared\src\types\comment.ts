import { ICommonFields, IPageParams } from './global';
import { IUser } from './user';

export enum ECommentTargetType {
  /** 评论打卡记录 */
  Attendance = 'attendance',
}

export interface IComment extends ICommonFields {
  /** 评论人ID */
  author_id: string;
  author?: IUser;

  /** 评论内容 */
  content: string;
  /** 评论对象ID */
  target_id: string;
  /** 评论对象类型 */
  target_type: ECommentTargetType;

  reply_id?: string;
  reply?: IComment;
}

export interface IQueryCommentParams
  extends IPageParams,
    Partial<Pick<IComment, 'author_id' | 'target_id' | 'target_type'>> {}

export interface ICreateCommentParams
  extends Pick<IComment, 'content' | 'target_id' | 'reply_id'> {}
