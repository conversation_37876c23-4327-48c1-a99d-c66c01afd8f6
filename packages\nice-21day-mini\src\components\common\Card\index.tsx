import { View } from '@tarojs/components';
import { CSSProperties, ReactNode } from 'react';
import './index.scss';

interface ICardProps {
  key?: string;
  /** 标题 */
  title?: string;
  /** 标题左侧的插槽 */
  extra?: ReactNode;
  styles?: CSSProperties;
  children: ReactNode;
}

const Card: React.FC<ICardProps> = ({
  key,
  title,
  extra,
  styles,
  children,
}) => {
  return (
    <View className="card" key={key} style={styles}>
      {(title || extra) && (
        <View className="card-title">
          <View className="title">{title}</View>
          <View className="extra">{extra}</View>
        </View>
      )}
      <View className="card-content">{children}</View>
    </View>
  );
};

export default Card;
