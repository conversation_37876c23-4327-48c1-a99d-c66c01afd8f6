import { RecommendAttendance, Result, TrainingListItem } from '@/components';
import { useMyTrainingList, usePageShowAgain, useRouterParams } from '@/hooks';
import { ETrainingProgress } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { AtTabs } from 'taro-ui';
import styles from './index.module.scss';

const tabList = [{ title: '报名中' }, { title: '进行中' }, { title: '已结束' }];
const tabIndexMap: Record<string, ETrainingProgress> = {
  '0': ETrainingProgress.Registering,
  '1': ETrainingProgress.Processing,
  '2': ETrainingProgress.Finished,
};

const MyTraining = () => {
  const { tab } = useRouterParams();
  const [tabInex, seTabInex] = useState<number>(+tab || 0);

  const trainingProgress: ETrainingProgress = useMemo(
    () => tabIndexMap[String(tabInex)],
    [tabInex],
  );

  const queryParams = useMemo(() => {
    return {
      page: 1,
      size: 100,
      progress: trainingProgress,
      // 进行中的训练营包括总结阶段
      progresses:
        trainingProgress === ETrainingProgress.Processing
          ? ETrainingProgress.Summary
          : '',
      // 当天的日期，会携带这天的打卡记录
      attendance_date:
        trainingProgress === ETrainingProgress.Processing
          ? dayjs().format('YYYY-MM-DD')
          : null,
    };
  }, [trainingProgress]);

  const { loading, myTrainingList, fetchMyTrainingList } =
    useMyTrainingList(queryParams);

  usePageShowAgain(() =>
    fetchMyTrainingList({ ...queryParams, showLoading: false }),
  );

  const handleTableChange = (val: number) => {
    seTabInex(val);
  };

  return (
    <View>
      <AtTabs
        className={styles.tabs}
        current={tabInex}
        tabList={tabList}
        onClick={handleTableChange}
      />

      <View className={styles.training}>
        {!loading && myTrainingList.length === 0 && <Result />}
        {myTrainingList.map((training) => (
          <TrainingListItem
            key={training.id}
            training={training}
            extra={
              trainingProgress === ETrainingProgress.Processing ? (
                <RecommendAttendance trainingId={training.id} />
              ) : null
            }
          />
        ))}
      </View>
    </View>
  );
};

export default MyTraining;
