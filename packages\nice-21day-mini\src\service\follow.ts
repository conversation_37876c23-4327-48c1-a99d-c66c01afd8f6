import request from '@/utils/request';
import { IUser } from '@nice-people/nice-21day-shared';

/**
 * 判断是否关注关系
 * @param user_id 粉丝ID
 * @param followee_id 被关注人ID
 */
export const queryIsFollowed = async (user_id: string, followee_id: string) => {
  return await request.get<{ isFollowed: boolean }>(`/follow/isfollowed`, {
    user_id,
    followee_id,
  });
};

/**
 * 查看某个人的关注列表
 */
export const queryUserFollowing = async (user_id: string) => {
  return await request.get<IUser[]>(`/follow/followees`, {
    user_id,
  });
};

/**
 * 查看某个人的粉丝列表
 */
export const queryUserFollowers = async (user_id: string) => {
  return await request.get<IUser[]>(`/follow/followers`, {
    user_id,
  });
};

/**
 * 关注
 * @param user_id 粉丝ID
 * @param followee_id 被关注人ID
 * @returns
 */
export const createFollow = async (user_id: string, followee_id: string) => {
  return await request.post(`/follow/do`, {
    user_id,
    followee_id,
  });
};

/**
 * 取消关注
 * @param user_id 粉丝ID
 * @param followee_id 被关注人ID
 * @returns
 */
export const deleteFollow = async (user_id: string, followee_id: string) => {
  return await request.post(`/follow/undo`, {
    user_id,
    followee_id,
  });
};
