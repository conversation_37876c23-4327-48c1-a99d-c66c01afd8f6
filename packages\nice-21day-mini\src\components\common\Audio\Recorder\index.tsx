// 这里放录音相关代码
import { taroUpload } from '@/utils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import debounce from 'lodash.debounce';
import { useCallback, useEffect, useMemo, useState } from 'react';
import AudioPlayer from '../AudioPlayer';
import RecordButton from './RecordingButton';

/** 声音太短时提示文案 */
const SHORT_DURATION_WARNING = '还不到5秒哦，再多说一点吧';
/** 最短限制为5秒钟 */
const MIN_DURATION_MS = 5 * 1000;
/** 获取全局唯一的录音实例 */
const recorderIns = Taro.getRecorderManager();

interface OnRecorderStopCallbackResult {
  /** 录音总时长，单位：ms */
  duration: number;
  /** 录音文件大小，单位：Byte */
  fileSize: number;
  /** 录音文件的临时路径 */
  tempFilePath: string;
}

interface IRecorderProps {
  /** 录制完成，返回上传完成后的音频地址 */
  onFinish: (audioPath: string) => void;
  /** 确定按钮文案 */
  confirmText?: string;
  /** 取消录制 */
  onCancel?: () => void;
  /** 录制状态变化 */
  onRecordingChange?: (isRecording: boolean) => void;
}

const Recorder: React.FC<IRecorderProps> = ({
  onFinish,
  confirmText = '完成',
  onCancel,
  onRecordingChange,
}) => {
  // 录音标志
  const [recording, setRecording] = useState(false);
  // 是否初始化
  const [hasInitRecording, setHasInitRecording] = useState(false);
  // 临时录音地址
  const [tempFile, setTempFile] = useState<OnRecorderStopCallbackResult>(
    {} as OnRecorderStopCallbackResult,
  );
  // 是否已经上传完成，避免重复上传
  const [uploaded, setUploaded] = useState<boolean>(false);

  useEffect(() => {
    onRecordingChange?.(recording);
  }, [recording]);

  // 每次临时地址变化时都需要重新上传
  useEffect(() => {
    setUploaded(false);
  }, [tempFile]);

  const finishBtnDisabled = useMemo(() => {
    return recording || !tempFile.tempFilePath || uploaded;
  }, [recording, tempFile.tempFilePath, uploaded]);

  const hasRecordAuth = (res: any) => {
    return res.authSetting['scope.record'];
  };

  const getRecordAuth = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      Taro.getSetting({
        success: (res) => {
          if (!hasRecordAuth(res)) {
            console.log('未获取到录音授权');
            // 第一次未授权会走 success拉起授权; 如果用户明确授权过，则会直接reject
            Taro.authorize({
              scope: 'scope.record',
              success: () => resolve(),
              fail: () => reject(new Error('用户拒绝授权')),
            });
          } else {
            resolve();
          }
        },
        fail: () => reject(new Error('获取授权信息失败')),
      });
    });
  };

  /**
   * 拒绝后的申请授权
   */
  const guidedAuthorization = () => {
    Taro.showModal({
      title: '未获取录音授权',
      content: '请授权录音权限才可正常录音',
      confirmText: '去授权',
      success({ confirm }) {
        // 点击确认，打开录音授权弹窗
        if (confirm) {
          Taro.openSetting({
            success: (setRes) => {
              // 拿到了录音授权
              if (hasRecordAuth(setRes)) {
                // 创建监听
                createRecordListener();
              }
            },
          });
        }
      },
    });
  };

  // 初始化录音器
  const initRecorder = async () => {
    try {
      await getRecordAuth();
      console.log('获取权限成功 ');
    } catch (error) {
      console.log('error: ', error);
      // 无权限，申请
      guidedAuthorization();
      return;
    }

    //  创建监听
    createRecordListener();
  };

  /**
   * 创建监听
   */
  const createRecordListener = () => {
    console.log('创建监听--');

    recorderIns.onStart((err) => {
      setRecording(true);
      Taro.showToast({
        title: '录音开始',
        icon: 'success',
        duration: 500,
      });
    });

    // 获取录音文件路径并保存到 state 中
    recorderIns.onStop((res) => {
      setRecording(false);
      console.log('录音停止', res);
      // 触发录音完成后，设置临时地址
      setTempFile(res);
    });

    // 中断处理
    recorderIns.onInterruptionBegin((res) => {
      Taro.showToast({
        title: res.errMsg,
        icon: 'success',
        duration: 2000,
      });

      stopRecording();
    });

    // 错误处理
    recorderIns.onError((res) => {
      // TODO: 组件卸载后停止录音会报错，这里先屏蔽吧
      // Taro.showToast({
      //   title: res.errMsg,
      //   icon: 'error',
      //   duration: 2000,
      // });
    });

    setHasInitRecording(true);

    // 开始录音
    startRecording();
  };

  const startRecording = () => {
    console.log('开始录音---');

    recorderIns.start({
      // Millisecond unit
      duration: 5 * 60 * 1000,
      // 目前先选择 mp3，兼容、体积较好；
      // 后期如果兼容性ok可以考虑用aac
      format: 'mp3',
      sampleRate: 32000,
    });
  };

  const debouncedClickStartRecord = debounce(
    async () => {
      setTempFile((prev) => ({ ...prev, duration: 0 }));

      let _hasLoading = false;
      // 初始化
      if (!hasInitRecording) {
        _hasLoading = true;
        Taro.showLoading({
          title: '录音准备中...',
        });

        console.log('初始化开始---');
        await initRecorder();
        console.log('初始化完成---');
        return;
      }

      if (recording) {
        stopRecording();
        return;
      }

      if (_hasLoading) {
        Taro.hideLoading();
      }

      startRecording();
    },
    500,
    {
      trailing: true,
    },
  );

  /**
   * 暂停录音
   */
  const pauseRecording = () => {
    if (recording) {
      recorderIns.pause();
    }
  };

  /**
   * 继续录音
   */
  const resumeRecording = () => {
    recorderIns.resume();
  };

  /**
   * 停止录音
   */
  const stopRecording = () => {
    if (recording) {
      recorderIns.stop();
    }
  };

  const stopRecordingEffect = useCallback(() => {
    if (recording) {
      recorderIns.stop();
    }
  }, [recording]);

  useEffect(() => {
    // 卸载时停止录音，防止直接点击返回按钮退出页面
    return () => stopRecordingEffect();
  }, [stopRecordingEffect]);

  /** 重新录制 */
  const handleReload = () => {
    Taro.showModal({
      title: '重新录制',
      content: '重录后，当前录音会消失哦',
      cancelText: '取消',
      confirmText: '重新开始',
      success: function (res) {
        if (res.confirm) {
          stopRecording();
          onCancel?.();
        }
      },
    });
  };

  /**
   * 放弃录音
   */
  const handleCancel = () => {
    Taro.showModal({
      title: '取消录音',
      content: '取消后，当前录音会消失哦',
      success: function (res) {
        if (res.confirm) {
          stopRecording();
          onCancel?.();
        }
      },
    });
  };

  const handleFinish = useCallback(() => {
    if (!tempFile.tempFilePath) {
      return;
    }

    if (tempFile.duration < MIN_DURATION_MS) {
      Taro.showModal({
        title: '',
        content: SHORT_DURATION_WARNING,
        showCancel: false,
        confirmText: '我知道了',
      });
      return;
    }

    Taro.showModal({
      title: '完成录制',
      content: '确定完成录制吗？录制结束后声音无法被再次编辑。',
      cancelText: '取消',
      confirmText: '完成',
      success: function (res) {
        if (res.confirm) {
          taroUpload(tempFile.tempFilePath).then((file) => {
            onFinish(file.path);
            setUploaded(true);
          });
        }
      },
    });
  }, [tempFile.tempFilePath, tempFile.duration]);

  const playButtonText = useMemo(() => {
    if (recording) {
      return '录制中...';
    }
    if (tempFile.tempFilePath) {
      return '重新录音';
    }
    return '点击录音';
  }, [tempFile.tempFilePath, recording]);

  return (
    <View className="flex flex-col w-full text-gray-500">
      {/* 声音太短时提示 */}
      {/* <AtToast
        isOpened={
          !recording &&
          tempFile.tempFilePath &&
          tempFile.duration < MIN_DURATION_MS
        }
        text={SHORT_DURATION_WARNING}
      /> */}

      {/* 声音回放进度条 */}
      <View className="audit-replay flex-1 flex items-end">
        {(recording || tempFile.tempFilePath) && (
          <AudioPlayer
            src={tempFile.tempFilePath}
            totalDuration={Math.trunc(tempFile.duration / 1000)}
            isRecording={recording}
          />
        )}
      </View>
      <View className="flex items-center justify-around gap-4 h-30 flex-grow-0">
        {/* 显示取消按钮 */}
        <View
          className="flex flex-col w-10 gap-1 items-center"
          onClick={handleCancel}
        >
          <View className="at-icon at-icon-chevron-left text-2xl" />
          取消
        </View>
        {/* 刚打开，还没有录制时 */}
        <View className="flex flex-col justify-center items-center gap-1">
          <View>
            <RecordButton
              onClick={debouncedClickStartRecord}
              isRecording={recording}
            />
          </View>
          <View>{playButtonText}</View>
        </View>
        {/* 录制完成，显示完成按钮 */}
        <View
          className={`flex flex-col w-10 gap-1 items-center ${
            finishBtnDisabled ? 'text-neutral-300' : ''
          }`}
          onClick={() => {
            if (!finishBtnDisabled) {
              handleFinish();
            }
          }}
        >
          <View className="at-icon at-icon-check-circle text-2xl" />
          {confirmText}
        </View>
      </View>
    </View>
  );
};

export default Recorder;
