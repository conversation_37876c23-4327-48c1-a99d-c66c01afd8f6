// 运行时配置

import { ICurrentUser } from '@nice-people/nice-21day-shared';
import { history, RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { message, Modal } from 'antd';
import { ACCESS_TOKEN_LOCAL_KEY, DEFAULT_NAME, LOGO_URL } from './constants';
import { queryCurrentUser } from './services';

// 当前所在页面
const isAdminPage = () => {
  const pathname = history.location.pathname;
  return pathname.includes('/admin');
};

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://next.umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{
  currentUser: ICurrentUser;
  name: string;
}> {
  try {
    const currentUser = await queryCurrentUser();
    return { currentUser, name: currentUser.nick_name };
  } catch (error) {
    return { currentUser: {} as ICurrentUser, name: '' };
  }
}

export const layout: RunTimeLayoutConfig = () => {
  return {
    logo: LOGO_URL,
    title: DEFAULT_NAME,
    menu: {
      locale: false,
    },
    navTheme: 'light',
    theme: 'dark',
    layout: 'mix',
    onMenuHeaderClick: () => history.push('/admin'),
    // @ts-ignore
    logout: () => {
      Modal.confirm({
        title: '确定要退出登录吗？',
        onOk: () => {
          localStorage.removeItem(ACCESS_TOKEN_LOCAL_KEY);
          if (isAdminPage()) {
            history.push('/admin/login');
          }
        },
      });
    },
    disableContentMargin: false,
  };
};

export const request: RequestConfig = {
  timeout: 30 * 1000,
  baseURL: '/api/v1',
  // other axios options you want
  errorConfig: {
    errorHandler(error: any) {
      if (error.response.status === 401) {
        if (isAdminPage()) {
          history.push('/admin/login');
        }
        return;
      }
      console.error('api error', error);
      message.error(error?.response?.data?.message || '请求错误');
    },
    errorThrower() {},
  },
  requestInterceptors: [
    (config: any) => {
      const token = localStorage.getItem(ACCESS_TOKEN_LOCAL_KEY);
      // 没有登录请求，并且没有 token 的时候直接进入登录页面
      if (config.url.indexOf('/admin/login') === -1 && !token) {
        if (isAdminPage()) {
          history.push('/admin/login');
        }
      }

      // 禁止缓存
      const url = config.url.concat(`?t=${+new Date()}`);

      // 忽略为空字符串的参数，防止后端接口报错
      const params = config.params || {};
      Object.keys(params).forEach((key) => {
        const val = params[key];
        if (val === '' || val === undefined || val === null) {
          delete params[key];
        }
      });

      return {
        ...config,
        params,
        url,
        headers: {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        },
      };
    },
  ],
  responseInterceptors: [
    (response) => {
      return response;
    },
  ],
};
