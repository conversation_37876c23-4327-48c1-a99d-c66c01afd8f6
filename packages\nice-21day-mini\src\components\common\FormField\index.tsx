import { View } from '@tarojs/components';
import React, { ReactNode } from 'react';

interface IFormFieldProps {
  label: ReactNode;
  className?: string;
}
const FormField: React.FC<IFormFieldProps> = ({
  label,
  children,
  className,
}) => {
  return (
    <View className={`p-3 pb-0 ${className}`}>
      <View
        style={{
          color: '#333',
          height: '30px',
          lineHeight: '30px',
          marginBottom: 4,
        }}
      >
        {label}
      </View>
      <View>{children}</View>
    </View>
  );
};

export default FormField;
