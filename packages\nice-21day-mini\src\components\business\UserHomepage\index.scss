.homepage-page {
  background-color: #f6f8fb;
  min-height: 100vh;
}
.homepage__cover {
  height: 340px;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 10;
}
.homepage__content {
  padding: 30px;
  position: relative;
  top: 340px;
  min-height: calc(100vh - 340px);
}
.homepage__user-card {
  padding: 20px;
  padding-bottom: 0;
  background-color: #fff;
  margin-top: -50px;
  position: sticky;
  top: 100px;
  z-index: 30;

  & .at-tabs__item-underline {
    height: 4px;
  }

  & .at-avatar {
    width: 160px;
    height: 160px;
  }
}

.homepage__user-action {
  & .at-tag {
    font-size: 24px;
    padding: 0 24px;
    height: 50px;
    line-height: 48px;
  }
}

.homepage__tab {
  & .at-tabs__header {
    display: none;
  }
  & .at-tabs-pane {
    min-height: 400px;
  }
  & .at-tabs__underline {
    background-color: transparent;
  }
}
