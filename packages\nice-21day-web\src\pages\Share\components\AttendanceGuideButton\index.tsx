import { useCurrentUser } from '@/hooks/useCurrentUser';
import { ETrainingProgress, ITraining } from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';

interface IProps {
  training: ITraining;
}
export default ({ training }: IProps) => {
  const { currentUser } = useCurrentUser();

  if (
    !training.inlude_me ||
    training.progress !== ETrainingProgress.Processing ||
    !currentUser?.id
  ) {
    return null;
  }

  return (
    <button
      type="button"
      onClick={(e) => {
        e.stopPropagation();
        history.push(
          `/training/${training.id}/user/${currentUser?.id}/attendance-timeline`,
        );
      }}
      className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-2 px-4 rounded-xl transition-all transform flex items-center justify-center space-x-2 shadow-lg mt-2"
    >
      <span className="font-medium">去打卡</span>
    </button>
  );
};
