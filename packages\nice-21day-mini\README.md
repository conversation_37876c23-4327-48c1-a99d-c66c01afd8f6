# 训练营小程序

## Start

1. 开发者工具要登录
2. 启动 shared: `pnpm run dev:shared`
3. 启动小程序: `pnpm run dev:weapp`
4. 开发者工具选择 `packages/nice-21day-mini/dist`
5. 确认 appid 是 wx769bbfcf9c406144

# nice-21day-mini

基于 Taro 框架开发的微信小程序

## 开发指南

### 环境要求
- Node.js 22.16.0+
- pnpm

### 安装依赖
```bash
pnpm install
```

### 开发运行
```bash
pnpm dev:weapp
```

### 生产构建
```bash
pnpm build:weapp
```

## 配置说明

### 域名白名单配置

在微信小程序管理后台需要添加以下域名到 **服务器域名** 配置中：

**request 合法域名：**
- `https://nice.yayujs.com`
- `https://tcb-api.tencentcloudapi.com`

**downloadFile 合法域名：**
- `https://oss.yayujs.com`
- `https://nice.yayujs.com`
- `https://636e-cloud1-8gtkkcco3dc-1307530300.tcb.qcloud.la`

**uploadFile 合法域名：**
- `https://nice.yayujs.com`

### 配置路径
微信公众平台 → 开发 → 开发管理 → 开发设置 → 服务器域名

### 常见错误解决方案

1. **"url not in domain list" 错误**
   - 检查上述域名是否已正确添加到小程序的合法域名列表中
   - 特别注意证书功能需要 `https://oss.yayujs.com` 域名

2. **"downloadFile:fail Error: ENOENT" 错误**
   - 通常是网络连接问题或域名白名单未配置
   - 检查网络连接状态
   - 确认域名已正确添加到 downloadFile 合法域名列表

3. **Canvas 绘制失败**
   - 通常由图片下载失败引起
   - 检查图片资源是否可访问
   - 确认域名白名单配置正确

如果遇到 "url not in domain list" 错误，请检查上述域名是否已正确添加到小程序的合法域名列表中。
