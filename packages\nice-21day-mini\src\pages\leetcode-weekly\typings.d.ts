export interface ILeetcodeWeekly {
  records: {
    /** 力扣个人主页 */
    homepage: string;
    /** 新题的数量 */
    newQuestionsTotal: number;
    /** 排名 */
    ranking: number;
    /** 力扣ID */
    userId: string;
    /** 力扣昵称 */
    userName: string;
    /** 每天的打卡记录  */
    weekly: string[];
  }[];
  /** 周报的标题 */
  title: string;
  /** 周报的更新记录 */
  updatedAt: string;
  /** 当前周的日期列表 */
  weekly: string[];
}

export interface ILeetcodeQuesiton {
  question_id: number;
  question__title: string;
  question__title_slug: string;
  question__hide: boolean;
  total_acs: number;
  total_submitted: number;
  total_column_articles: number;
  frontend_question_id: string;
  is_new_question: boolean;
  level: 1 | 2 | 3;
}

export type LeetcodeQuestionMap = Record<string, ILeetcodeQuesiton>;
