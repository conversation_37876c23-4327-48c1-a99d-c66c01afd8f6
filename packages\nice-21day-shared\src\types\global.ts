/** 启用禁用状态 */
export enum EState {
  /** 启用 */
  Enable = 'enable',
  /** 禁用 */
  Disable = 'disable',
}

/** 启用禁用映射关系 */
export const STATE_MAPPING: Record<EState, string> = {
  [EState.Enable]: '启用',
  [EState.Disable]: '禁用',
};

/**
 * 字符串布尔值
 * 0 - false
 * 1 - true
 */
export enum EBooleanString {
  NO = '0',
  YES = '1',
}

/** 字符串布尔值映射关系 */
export const BOOLEAN_STRING_MAPPING: Record<EBooleanString, string> = {
  [EBooleanString.YES]: '是',
  [EBooleanString.NO]: '否',
};

/**
 * 分页查询参数
 */
export interface IPageParams {
  /** 从 1 开始 */
  page: number;
  size: number;
}

/** 分页封装器 */
export interface IPageFactory<T> extends IPageParams {
  rows: T[];
  total: number;
}

/** 当前登录人 */
export interface ICurrentUser {
  id: string;
  login_name: string;
  nick_name: string;
  /** 角色 */
  role: 'admin' | 'user';
  avatar?: string;
  wechat_openid?: string;
  /** 生日 */
  birthday?: string;
}

export interface ICommonFields {
  id: string;
  deleted?: boolean;
  created_at?: string;
  updated_at?: string;
  deleted_at?: boolean;
  /** 备注信息 */
  description?: string;
}

/** 上传的图片 */
export interface IUploadFile {
  /** 图片的原始名称 */
  originalname: string;
  /**
   * 上传后的路径
   * @example /public/upload/2022-07-25/26ad3094-4cb3-404f-be5b-240e60e7b468.png
   */
  path: string;
}
