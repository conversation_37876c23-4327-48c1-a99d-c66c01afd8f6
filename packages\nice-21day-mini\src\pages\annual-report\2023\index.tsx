import { AppContext } from '@/appContext';
import { useReport2023Data } from '@/hooks';
import { BackgroundMusic } from '@/components/common';
import { CoverView, Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { useContext, useState } from 'react';
import './index.scss';
import Page1 from './pages/Page1';
import Page2 from './pages/Page2';
import Page3 from './pages/Page3';
import Page4 from './pages/Page4';
import Page5 from './pages/Page5';
import Page6 from './pages/Page6';
import Page7 from './pages/Page7';
import Page8 from './pages/Page8';
import Page9 from './pages/Page9';
import Page9_1 from './pages/Page9_1';
import Page10 from './pages/Page10';

// 钢琴曲卡农
const canon = '/alioss/2023-12-10/c07f953d-b5eb-409e-bd4c-1c020425bb82.mp3';

const pageList = [
  Page1,
  Page2,
  Page3,
  Page4,
  Page5,
  Page6,
  Page7,
  Page8,
  Page9,
  Page9_1,
  Page10,
];

const slimPageList = [Page1, Page3, Page2, Page9, Page9_1, Page10];

const UserReport2023: React.FC = () => {
  const { currentUser } = useContext(AppContext);
  const [current, setCurrent] = useState(0);

  const handleSwitchSwiper = (pageIdx: number) => {
    setCurrent(pageIdx);
  };

  const handleSwiperChange = (e: any) => {
    setCurrent(e.detail.current);
  };

  const { report2023Data, queryReport2023Loading } = useReport2023Data(
    currentUser?.id,
  );

  if (queryReport2023Loading || !currentUser?.id || !report2023Data.id) {
    return null;
  }

  return (
    <View className="h-[100vh]">
      <Swiper
        className="h-full report-2023__wrapper"
        current={current}
        onChange={handleSwiperChange}
        vertical
      >
        {(report2023Data.user_id ? pageList : slimPageList).map((Comp, idx) => (
          <SwiperItem
            key={Comp.name}
            className={`report-2023__section ${
              current === idx ? 'report-2023__section--active' : ''
            }`}
          >
            <Comp
              user={currentUser}
              reportData={report2023Data}
              isActive={current === idx}
              currentIdx={current}
              switchSwiper={handleSwitchSwiper}
            />
          </SwiperItem>
        ))}
      </Swiper>

      {/* bgm */}
      <CoverView className="absolute top-20px right-50px">
        <BackgroundMusic bgmUrl={canon} autoplay />
      </CoverView>

      {current !== 0 && current !== pageList.length - 1 && (
        <View className="report-2023__scroll-btn">
          <Image
            src={require('./assets/scroll.svg')}
            className="w-[36px] h-[46px]"
          />
        </View>
      )}
    </View>
  );
};

export default UserReport2023;
