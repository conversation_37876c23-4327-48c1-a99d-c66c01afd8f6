import { AppContext } from '@/appContext';
import {
  Login,
  RecommendAttendance,
  Result,
  TrainingListItem,
} from '@/components';
import withPageWrapper from '@/components/common/CustomWrapper';
import { useMyTrainingList, usePageShowAgain } from '@/hooks';
import { ETrainingProgress } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import { useContext } from 'react';
import { AtLoadMore } from 'taro-ui';

const queryParams = {
  page: 1,
  size: 100,
  progresses: `${ETrainingProgress.Processing},${ETrainingProgress.Summary}`,
  attendance_date: dayjs().format('YYYY-MM-DD'),
};

// 查询我已经报名的，正在打卡中的训练营
const MyProcessingTraining = () => {
  const { currentUser } = useContext(AppContext);

  const { loading, myTrainingList, fetchMyTrainingList } =
    useMyTrainingList(queryParams);

  usePageShowAgain(() =>
    fetchMyTrainingList({ ...queryParams, showLoading: false }),
  );

  if (!currentUser?.id) {
    return <Login />;
  }

  if (loading) {
    return <AtLoadMore status="loading" />;
  }
  if (myTrainingList.length === 0) {
    return (
      <View>
        <Result text="没有找到需要打卡的训练营" />
      </View>
    );
  }

  return (
    <View>
      <View style={{ padding: '0 10px' }}>
        {myTrainingList.map((training) => (
          <TrainingListItem
            key={training.id}
            training={training}
            extra={<RecommendAttendance trainingId={training.id} />}
          />
        ))}
      </View>
    </View>
  );
};

export default withPageWrapper(MyProcessingTraining);
