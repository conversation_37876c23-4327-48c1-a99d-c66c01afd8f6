import { MenuDataItem } from '@ant-design/pro-components';

export default [
  {
    name: '管理员登录',
    path: '/admin/login',
    component: './Login',
    hideInMenu: true,
    layout: false,
  },
  {
    path: '/admin',
    redirect: '/admin/admin',
  },
  {
    path: '/',
    name: '管理员',
    flatMenu: true,
    component: '@/Layouts/AdminLayout',
    access: 'admin',
    routes: [
      {
        name: '管理员',
        path: '/admin/admin',
        icon: 'user',
        hideChildrenInMenu: true,
        routes: [
          {
            name: '管理员列表',
            path: '/admin/admin',
            component: './Admin',
          },
          {
            name: '新建管理员',
            path: '/admin/admin/create',
            component: './Admin/Create',
          },
          {
            name: '编辑管理员',
            path: '/admin/admin/:id/update',
            component: './Admin/Update',
          },
        ],
      },
      {
        name: '注册用户',
        path: '/admin/user',
        icon: 'team',
        hideChildrenInMenu: true,
        routes: [
          {
            name: '用户列表',
            path: '/admin/user',
            component: './User',
          },
          {
            name: '用户详情',
            path: '/admin/user/:id',
            component: './User/Profile',
          },
        ],
      },
      {
        name: '训练营',
        path: '/admin/training',
        icon: 'project',
        hideChildrenInMenu: true,
        routes: [
          {
            name: '训练营列表',
            path: '/admin/training',
            component: './Training',
          },
          {
            name: '新建训练营',
            path: '/admin/training/create',
            component: './Training/Create',
          },
          {
            name: '编辑训练营',
            path: '/admin/training/:id/update',
            component: './Training/Update',
          },
          {
            name: '统计',
            path: '/admin/training/:id',
            component: './Training/Layout',
            routes: [
              {
                path: '/admin/training/:id',
                redirect: '/admin/training/:id/registration-form',
              },
              {
                name: '报名表',
                path: '/admin/training/:id/registration-form',
                component: './Training/RegistrationForm',
              },
              {
                name: '报名成员',
                path: '/admin/training/:id/member',
                component: './Training/Member',
              },
              {
                name: '打卡率统计',
                path: '/admin/training/:id/attendance-rate',
                component: './Training/AttendanceRate',
              },
            ],
          },
        ],
      },
      {
        name: '训练营成员',
        path: '/admin/training-member',
        icon: 'solution',
        hideChildrenInMenu: true,
        routes: [
          {
            name: '训练营列表',
            path: '/admin/training-member',
            component: './TrainingMember',
          },
          {
            name: '新建训练营成员',
            path: '/admin/training-member/create',
            component: './TrainingMember/Create',
          },
          {
            name: '编辑训练营成员',
            path: '/admin/training-member/:id/update',
            component: './TrainingMember/Update',
          },
        ],
      },
      {
        name: '训练营成员对比',
        path: '/admin/training-member-diff',
        icon: 'diff',
        component: './Training/MemberDiff',
      },
      {
        name: '打卡记录',
        path: '/admin/attendance-log',
        icon: 'profile',
        hideChildrenInMenu: true,
        routes: [
          {
            name: '打卡记录列表',
            path: '/admin/attendance-log',
            component: './AttendanceLog',
          },
          {
            name: '新建打卡',
            path: '/admin/attendance-log/create',
            component: './AttendanceLog/Create',
          },
          {
            name: '编辑打卡',
            path: '/admin/attendance-log/:id/update',
            component: './AttendanceLog/Update',
          },
          {
            name: '打卡记录日历',
            path: '/admin/attendance-log/calendar',
            component: './AttendanceLog/Calendar',
          },
          {
            name: '打卡记录详情',
            path: '/admin/attendance-log/:id/profile',
            component: './AttendanceLog/Profile',
          },
        ],
      },
      {
        name: '积分变更记录',
        path: '/admin/integral-log',
        icon: 'payCircle',
        component: './IntegralLog',
      },
      {
        name: '证书生成器',
        path: '/admin/certificate',
        icon: 'idcard',
        component: './Certificate',
      },
      {
        name: '批量证书生成器',
        path: '/admin/batch-certificate',
        icon: 'idcard',
        component: './Certificate/Batch',
      },
    ],
  },
  {
    name: '登录',
    path: '/user/login',
    component: './Share/Login',
    hideInMenu: true,
    layout: false,
  },
  {
    name: '用户',
    path: '/',
    component: '@/Layouts/UserLayout',
    hideInMenu: true,
    layout: false,
    routes: [
      {
        path: '/',
        component: './Share/Index',
      },
      {
        name: '用户',
        component: './Share/User/Layout',
        path: '/user',
        routes: [
          {
            name: '用户详情',
            path: '/user/:user_id',
            component: './Share/User/Profile',
          },
          {
            name: '我的',
            path: '/user/',
            component: './Share/User/Profile',
          },
        ],
      },
      {
        name: '训练营详情',
        path: '/training/:training_id',
        component: './Share/Training/Layout',
        routes: [
          {
            path: '/training/:training_id',
            redirect: '/training/:training_id/attendance-board',
          },
          {
            name: '训练营详情',
            path: '/training/:training_id',
            component: './Share/Training/TabsLayout',
            routes: [
              {
                name: '训练营-积分排行榜',
                path: '/training/:training_id/attendance-board',
                component: './Share/Training/AttendanceBoard',
              },
              {
                name: '训练营-打卡记录',
                path: '/training/:training_id/attendance-timeline',
                component: './Share/Training/AttendanceTimeline',
              },
            ],
          },
          {
            name: '训练营详情',
            path: '/training/:training_id/user/:user_id',
            component: './Share/Training/UserLayout',
            routes: [
              {
                path: '/training/:training_id/user/:user_id',
                redirect: '/training/:training_id/user/:user_id/summary',
              },
              {
                name: '训练营-打卡记录',
                path: '/training/:training_id/user/:user_id/attendance-timeline',
                component: './Share/Training/UserAttendanceTimeline',
              },
              {
                name: '训练营-总结',
                path: '/training/:training_id/user/:user_id/summary',
                component: './Share/Training/UserSummary',
              },
            ],
          },
          {
            name: '训练营-打卡详情',
            path: '/training/:training_id/user/:user_id/attendance/:attendance_id',
            component: './Share/Training/AttendanceDetail',
          },
        ],
      },
    ],
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
] as MenuDataItem[];
