import Taro, { InnerAudioContext } from '@tarojs/taro';

class TaroInnerAudioContextManager {
  private _currentPlay: InnerAudioContext | null = null;

  getTaroInnerAudioContext = () => {
    return Taro.createInnerAudioContext();
  };

  savePlaying = (audioCtx: InnerAudioContext) => {
    if (this._currentPlay !== audioCtx) {
      // 暂停上一个
      this._currentPlay?.stop();
    }
    this._currentPlay = audioCtx;
  };
}

export const taroInnerAudioContextManager = new TaroInnerAudioContextManager();
