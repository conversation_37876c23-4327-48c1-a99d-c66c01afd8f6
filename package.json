{"name": "nice-21day", "private": true, "scripts": {"dev:shared": "pnpm --filter=@nice-people/nice-21day-shared dev", "build:shared": "pnpm --filter=@nice-people/nice-21day-shared build", "dev:web": "pnpm run dev:shared & pnpm --filter=@nice-people/nice-21day-web dev", "build:web": "pnpm --filter=@nice-people/nice-21day-web build", "dev:weapp": "pnpm run dev:shared & pnpm --filter=@nice-people/nice-21day-mini dev:weapp", "build:weapp": "pnpm --filter=@nice-people/nice-21day-mini build:weapp", "postinstall": "pnpm run build:shared & husky install", "publish:shared": "pnpm install && pnpm run build:shared && pnpm publish -r --no-git-checks --registry https://registry.npmjs.org/"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@changesets/cli": "2.26.1", "@types/node": "^18.0.0", "chalk": "^4.1.2", "eslint": "^8.12.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "stylelint": "^14.9.1", "typescript": "^4.1.2"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,less}": ["prettier --write"]}}