import { refreshMyTrainingScore } from '@/services/my';
import {
  CheckCircleFilled,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FireOutlined,
  FlagOutlined,
  FundProjectionScreenOutlined,
  LikeOutlined,
  ReloadOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import {
  ETrainingProgress,
  getFileUrl,
  ITraining,
  ITrainingMember,
  ITrainingTask,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { history, useNavigate } from '@umijs/max';
import { Avatar, List, message, Space, Tag } from 'antd';
import { noop } from 'lodash';
import { useCallback, useMemo } from 'react';

interface IScoreRankItemProps {
  trainingUser: ITrainingMember;
  training?: ITraining;
  isMySelf?: boolean;
  showActions?: boolean;
  refresh?: () => void;
}
const ScoreRankItem = ({
  trainingUser,
  training,
  isMySelf = false,
  showActions = true,
  refresh = noop,
}: IScoreRankItemProps) => {
  const navigator = useNavigate();

  const showSummaryFlag = useMemo(() => {
    return [
      ETrainingProgress.Summary,
      ETrainingProgress.Finished,
      // @ts-ignore
    ].includes(training?.progress);
  }, [training?.progress]);

  const tasks = useMemo(() => {
    return parseArrayJson<ITrainingTask>(trainingUser.tasks || '[]');
  }, [trainingUser.tasks]);

  const handleRefreshScore = useCallback(() => {
    if (trainingUser.training_id) {
      // 10分钟内只能刷新一次
      const score_updated_at = trainingUser.score_updated_at; // UTC 时间
      if (
        score_updated_at &&
        Date.now() - new Date(score_updated_at).getTime() < 10 * 60 * 1000
      ) {
        message.warning('10分钟内只能刷新一次，待会再来吧');
        return;
      }

      refreshMyTrainingScore(trainingUser.training_id).then(() => {
        refresh();
        message.success('刷新成功');
      });
    }
  }, [trainingUser]);

  return (
    <div
      className="bg-white rounded-2xl shadow-sm px-2 mb-3 cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => {
        if (showActions) {
          if (showSummaryFlag) {
            navigator(
              `/training/${trainingUser.training_id}/user/${trainingUser.user_id}/summary`,
            );
            return;
          }
          navigator(
            `/training/${trainingUser.training_id}/user/${trainingUser.user_id}/attendance-timeline`,
          );
        }
      }}
    >
      <List.Item>
        <List.Item.Meta
          title={
            <div className="flex gap-1 items-center pl-2">
              <Avatar
                className="cursor-pointer"
                onClick={(e) => {
                  e?.stopPropagation();
                  history.push(`/user/${trainingUser.user_id}`);
                }}
                size={32}
                src={getFileUrl(trainingUser.user?.avatar_url)}
              />
              <div
                className="cursor-pointer inline-block"
                onClick={(e) => {
                  e.stopPropagation();
                  history.push(`/user/${trainingUser.user_id}`);
                }}
              >
                <span className="font-medium text-base">
                  {trainingUser.user?.nick_name}{' '}
                </span>
              </div>
              {isMySelf ? (
                <Space size={4}>
                  <Tag className="cursor-default" color="blue-inverse">
                    我自己
                  </Tag>
                  <Tag
                    icon={<ReloadOutlined />}
                    color="green-inverse"
                    onClick={handleRefreshScore}
                  >
                    刷新积分
                  </Tag>
                </Space>
              ) : (
                ''
              )}
            </div>
          }
          description={
            <div className="p-2">
              <div className="p-3 bg-gray-50 rounded-md relative">
                {/* ===== 积分 ===== */}
                <div className="absolute top-[-46px] right-[-4px] w-[60px] h-[60px] sm:w-20 sm:h-20]">
                  {/* 积分主容器 - 使用金色渐变背景 */}
                  <div className="relative bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 rounded-2xl p-3 shadow-lg transform hover:scale-105 transition-transform">
                    {/* 内层光晕效果 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-300/30 to-transparent rounded-2xl"></div>

                    {/* 装饰性闪光点 */}
                    <div className="absolute top-1 right-1 w-1 h-1 bg-white/70 rounded-full"></div>
                    <div className="absolute bottom-2 left-2 w-0.5 h-0.5 bg-white/50 rounded-full"></div>

                    {/* 积分内容 */}
                    <div className="relative text-center w-full">
                      <div className="text-sm sm:text-lg font-black text-white drop-shadow-sm">
                        {trainingUser.score}
                      </div>
                      <div className="text-xs text-amber-100 font-medium">
                        积分
                      </div>
                    </div>

                    {/* 底部装饰条 */}
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"></div>
                  </div>

                  {/* 外层光晕 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-400/20 to-yellow-500/20 rounded-2xl blur-sm -z-10 scale-110"></div>
                </div>

                {/* ====== 任务清单 ======= */}
                <div className="mb-3">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircleOutlined className="text-lg text-green-600" />
                    <span className="font-medium text-gray-800">
                      任务清单（{tasks.length}个）
                    </span>
                  </div>

                  <div className="flex flex-col gap-2">
                    {tasks.map((task) => (
                      <div
                        key={task.id}
                        className={`flex items-center gap-1 rounded-lg bg-green-50 border border-green-200 p-2`}
                      >
                        <div className="w-6 h-6 rounded-xl bg-blue-100 flex items-center justify-center flex-shrink-0">
                          <FlagOutlined className="text-blue-500" />
                        </div>
                        <div className="text-gray-500">{task.name}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* ===== 统计信息 ===== */}
                <div className="">
                  <div className="flex items-center space-x-2 mb-3">
                    <FundProjectionScreenOutlined className="text-lg text-green-600" />
                    <span className="font-medium text-gray-800">打卡统计</span>
                  </div>
                  {/* 小屏幕下，竖着排列 */}
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                    <div className="flex items-center space-x-1">
                      <TrophyOutlined className="w-4 h-4 text-orange-500" />
                      <span className="text-gray-600">
                        积分 {trainingUser.score}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <FireOutlined className="w-4 h-4 text-orange-500" />
                      <span className="text-gray-600">
                        累计打卡 {trainingUser.total_attendance_count} 天
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <FireOutlined className="w-4 h-4 text-orange-500" />
                      <span className="text-gray-600">
                        最长连续打卡{' '}
                        {trainingUser.max_consecutive_attendance_count} 天
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <LikeOutlined className="w-4 h-4 text-orange-500" />
                      <span className="text-gray-600">
                        获赞 {trainingUser.like_count || 0} 次
                      </span>
                    </div>
                  </div>
                </div>

                {/* ===== 总结状态 ===== */}
                {showSummaryFlag && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    {trainingUser.summary ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircleFilled className="mr-1" />
                        <span className="text-sm">已提交训练营总结</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-orange-600">
                        <ExclamationCircleOutlined className="mr-1" />
                        <span className="text-sm">还没有提交训练营总结</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          }
        />
      </List.Item>
    </div>
  );
};

export default ScoreRankItem;
