import TrainingProfile from '@/components/TrainingProfile';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { ITraining } from '@nice-people/nice-21day-shared';
import { history, Outlet, useLocation, useModel, useParams } from '@umijs/max';
import { Result, Skeleton } from 'antd';
import React, { useCallback, useEffect } from 'react';

export interface ITrainingLayoutContext {
  training: ITraining;
}

const tabList = [
  {
    key: 'registration-form',
    tab: '报名表',
  },
  {
    key: 'member',
    tab: '报名成员',
  },
  {
    key: 'attendance-rate',
    tab: '打卡率统计',
  },
];

const TrainingLayout: React.FC = () => {
  const params = useParams();
  const location = useLocation();

  const { queryDetailLoading, queryTrainingDetail, training } =
    useModel('trainingModel');

  useEffect(() => {
    queryTrainingDetail(params.id);
  }, [params.id]);

  const getTabKey = useCallback(() => {
    const url = `/admin/training/${params.id}`;
    const tabKey = location.pathname.replace(`${url}/`, '');
    if (tabKey && tabKey !== '/') {
      return tabKey;
    }

    return 'member';
  }, [location, params.id]);

  if (queryDetailLoading) {
    return <Skeleton loading />;
  }

  if (!training?.id) {
    return <Result status="error" title="训练营不存在或已被删除" />;
  }

  const handleTabChange = (key: string) => {
    history.push(`/admin/training/${params.id}/${key}`);
  };

  return (
    <ProCard bodyStyle={{ padding: 0 }}>
      <PageContainer
        breadcrumb={undefined}
        title={<div style={{ paddingTop: 10 }}>{training.name}</div>}
        extra={[]}
        tabList={tabList}
        tabActiveKey={getTabKey()}
        onTabChange={handleTabChange}
        content={
          <>
            <TrainingProfile training={training} />
          </>
        }
      >
        <div style={{ marginTop: 10, padding: '0 20px 20px 20px' }}>
          <Outlet context={{ training }} />
        </div>
      </PageContainer>
    </ProCard>
  );
};

export default TrainingLayout;
