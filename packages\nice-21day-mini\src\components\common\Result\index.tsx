import { View } from '@tarojs/components';
import { ReactNode } from 'react';
import styles from './index.module.scss'; //

interface IEmptyProps {
  status?: '404' | '401' | '500';
  text?: ReactNode;
  className?: string;
}
const Result: React.FC<IEmptyProps> = ({ text = '暂无数据', className }) => {
  return (
    <View className={`${styles.result} ${className}`}>
      <View className="at-icon at-icon-alert-circle"></View>
      <View className={styles.result__text}>{text}</View>
    </View>
  );
};

export default Result;
