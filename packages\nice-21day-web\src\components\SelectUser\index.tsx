import { getFileUrl } from '@nice-people/nice-21day-shared';
import { useModel } from '@umijs/max';
import { Avatar, Select } from 'antd';
import React, { useEffect } from 'react';

interface ISearchUserProps {
  // 最重要的字段是 id、value、onChange
  [key: string]: any;
}
export const SelectUser: React.FC<ISearchUserProps> = ({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  defaultRender,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  options,
  ...rest
}) => {
  const { loading, allUsers, queryAllUsers } = useModel('userModel');

  useEffect(() => {
    queryAllUsers();
  }, []);

  return (
    <Select
      {...rest}
      showSearch
      loading={loading}
      allowClear
      virtual
      placeholder="请选择"
      filterOption={(input, option) => {
        // 三个数组
        // 【头像，空格，人名】
        return (option?.children?.[2] as unknown as string)
          .toLowerCase()
          .includes(input.toLowerCase());
      }}
    >
      {allUsers.map((user) => (
        <Select.Option key={user.id} value={user.id}>
          <Avatar size="small" src={getFileUrl(user.avatar_url)} />{' '}
          {user.nick_name}
        </Select.Option>
      ))}
    </Select>
  );
};
