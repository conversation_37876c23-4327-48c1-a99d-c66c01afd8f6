import { uploadFile } from '@/services';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import {
  Boot,
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
} from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import { Card, message } from 'antd';
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import styles from './index.module.less';

import markdownModule from '@wangeditor/plugin-md';

Boot.registerModule(markdownModule);

type InsertFnType = (url: string, alt: string, href: string) => void;

export interface IWangEditorProps {
  initValue?: string;
  /**
   * 是否只读
   * @default false
   */
  readOnly?: boolean;
  placeholder?: string;
  className?: string;
  style?: React.CSSProperties;
  /**
   * 最大输入长度
   * @default 10000
   */
  maxLength?: number;
  /**
   * 简单模式
   * @default false
   */
  simple?: boolean;

  onChange?: (editor: IDomEditor) => void;
}
export interface IWangEditorRef {
  editor: IDomEditor | null;
}
export const WangEditor = forwardRef<IWangEditorRef, IWangEditorProps>(
  (
    {
      initValue,
      readOnly = false,
      placeholder = '',
      maxLength = 10000,
      simple = false,
      className,
      style,
      onChange,
    },
    ref,
  ) => {
    const [editor, setEditor] = useState<IDomEditor | null>(null);

    // 编辑器内容
    const [html, setHtml] = useState(() => initValue || '');
    // 工具栏配置
    const toolbarConfig: Partial<IToolbarConfig> = {
      // 隐藏视频
      excludeKeys: ['group-video', 'insertImage'],
    };

    const showToolbar = useMemo(() => {
      if (readOnly) {
        return false;
      }
      if (simple) {
        return false;
      }
      return true;
    }, [readOnly, simple]);

    // 编辑器配置
    const editorConfig: Partial<IEditorConfig> = {
      placeholder: placeholder,
      maxLength,
      onMaxLength() {
        message.warning(`评论内容不能超过${maxLength}个字符`);
      },
      readOnly,
      onChange(editor) {
        onChange?.(editor);
      },
      MENU_CONF: {
        uploadImage: {
          async customUpload(file: File, insertFn: InsertFnType) {
            const formData = new FormData();
            formData.append('file', file);
            message.loading('上传中...');
            uploadFile(formData)
              .then((res) => {
                const imageUrl = getFileUrl(res.path);

                insertFn(imageUrl, res.originalname, imageUrl);
              })
              .catch(() => {
                message.error('上传失败');
              })
              .finally(() => {
                setTimeout(() => {
                  message.destroy();
                }, 1000);
              });
          },
        },
      },
    };

    // 及时销毁 editor ，重要！
    useEffect(() => {
      return () => {
        if (editor === null) return;
        editor.destroy();
        setEditor(null);
      };
    }, [editor]);

    useImperativeHandle(ref, () => ({
      editor,
    }));

    return (
      <div>
        <Card
          style={{ zIndex: 100 }}
          styles={{
            body: {
              padding: 4,
            },
          }}
        >
          {showToolbar && (
            <Toolbar
              editor={editor}
              defaultConfig={toolbarConfig}
              mode="default"
              style={{
                borderBottom: '1px solid #f0f0f0',
              }}
            />
          )}
          <Editor
            defaultConfig={editorConfig}
            value={html}
            onCreated={setEditor}
            className={`${styles.editor} ${className}`}
            style={style}
            onChange={(editor) => setHtml(editor.getHtml())}
            mode="default"
          />
        </Card>
      </div>
    );
  },
);
