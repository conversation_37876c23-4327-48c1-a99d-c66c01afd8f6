/**
 * 通用头部组件
 * 显示用户信息、支持登录引导和退出登录功能
 * 根据用户登录状态显示不同内容
 */
import { ACCESS_TOKEN_LOCAL_KEY, LOGO_URL } from '@/constants';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { logout } from '@/services';
import { LoginOutlined, UserAddOutlined } from '@ant-design/icons';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import { Avatar, message, Modal } from 'antd';
import React from 'react';

const UserHeader: React.FC = ({}) => {
  const { currentUser, refresh } = useCurrentUser();

  const handleLogout = () => {
    Modal.confirm({
      title: '确认退出登录吗？',
      onOk: async () => {
        await logout();

        window.localStorage.removeItem(ACCESS_TOKEN_LOCAL_KEY);
        // 刷新用户数据
        refresh();
        message.success('已退出登录');
        history.push('/');
      },
    });
  };

  const handleLogin = () => {
    history.push('/user/login');
  };

  const handleRegister = () => {
    history.push('/user/login?tab=register');
  };

  return (
    <div className="bg-white shadow-sm border-b border-gray-100">
      <div className="px-4 py-2 flex items-center justify-between">
        {/* 左侧标题区域 */}
        <div className="flex items-center">
          <div
            className="flex items-center space-x-2 cursor-pointer"
            onClick={() => history.push('/')}
          >
            <Avatar size={32} src={LOGO_URL} />
            <span className="text-lg font-bold text-gray-800">21天训练营</span>
          </div>
        </div>

        {/* 右侧用户区域 */}
        <div className="flex items-center">
          {currentUser.id ? (
            <div className="flex items-center space-x-3">
              {/* 用户信息 */}
              <div
                onClick={() => history.push('/user')}
                className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"
              >
                <img
                  src={getFileUrl(currentUser.avatar)}
                  alt={currentUser.nick_name}
                  className="w-8 h-8 rounded-full object-cover"
                />
                <span className="text-sm font-medium text-gray-700 hidden sm:block">
                  {currentUser.nick_name}
                </span>
              </div>

              {/* 退出登录按钮 */}
              <button
                type="button"
                onClick={handleLogout}
                className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="退出登录"
              >
                <LoginOutlined />
                <span className="text-sm hidden sm:block">退出</span>
              </button>
            </div>
          ) : (
            /* 未登录状态 */
            <div className="flex gap-1">
              <button
                type="button"
                onClick={handleLogin}
                className="flex items-center space-x-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all"
              >
                <LoginOutlined />
                <span className="text-sm">登录</span>
              </button>
              <button
                type="button"
                onClick={handleRegister}
                className="flex items-center space-x-1 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <UserAddOutlined />
                <span className="text-sm">注册</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
