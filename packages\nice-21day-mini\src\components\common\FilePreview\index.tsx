import { filterFiles } from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useMemo } from 'react';
import { AudioPlayer } from '../Audio';
import './index.scss';

interface IFilePreviewProps {
  files: string[];
}
const FilePreview: React.FC<IFilePreviewProps> = ({ files = [] }) => {
  const { imageList, audioList } = useMemo(() => filterFiles(files), [files]);

  const handlePreviewImgs = (imgUrl: string) => {
    Taro.previewImage({
      current: imgUrl, // 当前显示图片的http链接
      urls: imageList || [],
    });
  };

  if (files.length === 0) {
    return null;
  }

  return (
    <View className="file-preview__wrapper">
      {imageList.map((img) => (
        <View
          key={img}
          className="file-preview__item"
          onClick={(e) => {
            e.stopPropagation();
            handlePreviewImgs(img);
          }}
        >
          <Image className="image" src={img} />
        </View>
      ))}
      {audioList.map((audio) => (
        <AudioPlayer circle key={audio} src={audio} />
      ))}
    </View>
  );
};

export default FilePreview;
