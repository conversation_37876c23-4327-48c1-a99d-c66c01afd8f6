import {
  IComment,
  ICreateCommentParams,
  IPageFactory,
  IQueryCommentParams,
} from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';

/**
 * 获取打卡记录列表（分页）
 */
export const queryAttendanceComments = async (params: IQueryCommentParams) =>
  await request<IPageFactory<IComment>>('/comments/attendance-comments', {
    params,
  });

/**
 * 提交打卡记录的评论
 */
export const createAttendanceComment = async (params: ICreateCommentParams) =>
  await request<IComment>('/comments/attendance-comments', {
    method: 'POST',
    data: params,
  });
