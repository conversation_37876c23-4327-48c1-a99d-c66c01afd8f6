import { useCurrentUser } from '@/hooks/useCurrentUser';
import { uploadFile } from '@/services';
import { createMyAttendance, updateMyrAttendance } from '@/services/my';
import {
  CaretRightOutlined,
  EditFilled,
  FlagOutlined,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  ATTENDANCE_LIMIT_MORNING_HOURS,
  EAttendanceLogAuditState,
  EAttendanceState,
  ETrainingProgress,
  getFileUrl,
  IAttendanceLog,
  IAttendanceTask,
  ITrainingTask,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { useOutletContext } from '@umijs/max';
import {
  Alert,
  Button,
  Collapse,
  Drawer,
  Form,
  Input,
  message,
  Modal,
  notification,
  Space,
  Tag,
} from 'antd';
import { UploadChangeParam, UploadFile } from 'antd/es/upload';
import { Upload } from 'antd/lib';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { IUserLayoutContext } from '../../Training/UserLayout';

interface IProps {
  // 打卡记录
  attendanceLog?: IAttendanceLog;
  // 打卡日期
  attendanceDate: string;
  onFinish?: () => void;
}
const SubmitAttendance = ({
  // 默认是今天
  attendanceDate = dayjs().format('YYYY-MM-DD'),
  attendanceLog,
  onFinish,
}: IProps) => {
  // 如果是我自己，并且未打卡，可以打卡
  const { trainingUser } = useOutletContext<IUserLayoutContext>();
  const { currentUser } = useCurrentUser();

  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // 维护已上传的文件
  // {任务ID: [文件数组]}
  const [fileMapping, setFileMapping] = useState<Record<string, string[]>>({});

  const tasks = useMemo(() => {
    // 用户的任务
    const userTasks = parseArrayJson<IAttendanceTask>(trainingUser.tasks);
    // 已经打卡的任务
    const attendanceTasks = parseArrayJson<IAttendanceTask>(
      attendanceLog?.attendance_tasks || '',
    );

    // 把 attendance_content 和 attendance_files 都合并到 userTasks 中
    attendanceTasks.forEach((attendance) => {
      const task = userTasks.find((t) => t.id === attendance.id);
      if (task) {
        task.attendance_content = attendance.attendance_content;
        task.attendance_files = attendance.attendance_files;
      }
    });
    // 更新文件映射关系
    const mapping: Record<string, string[]> = {};
    attendanceTasks.forEach((attendance) => {
      mapping[attendance.id] = attendance.attendance_files || [];
    });
    setFileMapping(mapping);

    return userTasks;
  }, [trainingUser.tasks, attendanceLog?.attendance_tasks]);

  // 每次进来都重新设置一次
  useEffect(() => {
    form.setFieldValue('tasks', tasks);
  }, [tasks]);

  const handleFileChange = useCallback(
    (fileInfo: UploadChangeParam<UploadFile<any>>, task: ITrainingTask) => {
      const mapping = _.cloneDeep(fileMapping);

      // 删除
      if (fileInfo.file.status === 'removed') {
        mapping[task.id] = [...(mapping[task.id] || [])].filter(
          (uid) =>
            !fileInfo.file.url?.includes(uid) || uid !== fileInfo.file.uid,
        );
        setFileMapping(mapping);
        return;
      }

      // 上传新增
      const formData = new FormData();
      formData.append('file', fileInfo.file as any);
      message.loading('上传中...');
      setUploadLoading(true);
      uploadFile(formData)
        .then((file) => {
          mapping[task.id] = [...(mapping[task.id] || []), file.path];
          setFileMapping(mapping);
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          setUploadLoading(false);
          setTimeout(() => {
            message.destroy();
          }, 1000);
        });
    },
    [fileMapping],
  );

  const handleSubmit = useCallback(() => {
    form.validateFields().then((values) => {
      const { tasks = [], ...rest } = values;
      // 检查任务填写情况
      // 组装数据
      const _attendanceTasks: IAttendanceTask[] = [];
      tasks.forEach((task: IAttendanceTask) => {
        _attendanceTasks.push({
          ...task,
          attendance_content: task.attendance_content || '',
          attendance_files: fileMapping[task.id] ?? [],
        });
      });

      if (
        _attendanceTasks.every(
          (task) =>
            !task.attendance_content && task.attendance_files?.length === 0,
        )
      ) {
        notification.error({
          message: '打卡失败',
          description: '至少打卡一个任务',
        });
        return;
      }

      const postData: Omit<IAttendanceLog, 'id' | 'user_id'> = {
        ...rest,
        attendance_tasks: JSON.stringify(_attendanceTasks),
        attendance_date: attendanceDate,
        attendance_state: EAttendanceState.Attendance,
        audit_state: EAttendanceLogAuditState.Valid,
        audit_comment: '审核通过',
        description: '',
      };

      console.log(postData);

      Modal.confirm({
        title: '确定保存吗？',
        onOk: () => {
          setSubmitting(true);
          (attendanceLog?.id
            ? updateMyrAttendance(attendanceLog.id, postData)
            : createMyAttendance(postData.training_id, postData)
          )
            .then(() => {
              setVisible(false);

              // 提醒去小程序分享
              Modal.success({
                title: '打卡成功',
                content: '快去小程序分享给好友吧！',
              });
              onFinish?.();
            })
            .catch(() => {
              message.error('打卡失败');
            })
            .finally(() => {
              setSubmitting(false);
            });
        },
      });
    });
  }, [form, fileMapping, attendanceLog]);

  // 未登录
  if (!currentUser?.id) {
    return null;
  }

  // 不在打卡中
  if (trainingUser.training?.progress !== ETrainingProgress.Processing) {
    return null;
  }

  // 不是本人
  if (trainingUser.user_id !== currentUser?.id) {
    return null;
  }

  // 判断日期是否允许
  // attendanceDate === 今天的日期，允许打卡
  // 今天凌晨4点之前，允许打卡和编辑昨天的打卡
  // 今天凌晨4点之后，不允许打卡和编辑昨天的打卡
  if (
    !(
      // 今天
      (
        attendanceDate === dayjs().format('YYYY-MM-DD') ||
        // 昨天
        (attendanceDate === dayjs().subtract(1, 'day').format('YYYY-MM-DD') &&
          dayjs().hour() <= ATTENDANCE_LIMIT_MORNING_HOURS)
      )
    )
  ) {
    return null;
  }

  // 去打卡
  return (
    <div
      className="inline-block"
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <>
        <Tag
          color="blue-inverse"
          className="cursor-pointer"
          onClick={() => setVisible(true)}
          icon={<EditFilled />}
        >
          {attendanceLog ? '编辑打卡' : '去打卡'}
        </Tag>
      </>
      <Drawer
        open={visible}
        title={`${attendanceDate} 打卡`}
        width="100%"
        onClose={() => setVisible(false)}
        maskClosable={false}
        destroyOnHidden
        keyboard={false}
        extra={
          <Space>
            <Button
              onClick={() => {
                Modal.confirm({
                  title: '确定取消吗？',
                  onOk: () => {
                    setVisible(false);
                  },
                });
              }}
              loading={submitting}
            >
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              onClick={handleSubmit}
              loading={submitting}
            >
              提交
            </Button>
          </Space>
        }
      >
        <Alert
          className="mb-2"
          showIcon
          message="注意"
          description={`凌晨 ${ATTENDANCE_LIMIT_MORNING_HOURS} 点之前可正常提交、编辑昨天的打卡。如果你想在凌晨 ${ATTENDANCE_LIMIT_MORNING_HOURS} 点之后打卡当天日期，目前是不可以的哦，请你睡醒再来吧。`}
          type="warning"
        />
        {/* 垂直 */}
        <Form
          layout="vertical"
          form={form}
          initialValues={{
            id: attendanceLog?.id,
            training_id: trainingUser.training?.id,
            user_id: trainingUser.user_id,
            attendance_date: dayjs(attendanceDate),
            tasks,
          }}
        >
          <Form.Item label="id" name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            label="用户"
            name="user_id"
            hidden
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="训练营"
            name="training_id"
            hidden
            rules={[{ required: true, message: '请选择训练营' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="打卡日期"
            name="attendance_date"
            hidden
            rules={[{ required: true, message: '请选择训练营' }]}
          >
            <Input />
          </Form.Item>

          <Form.List name="tasks">
            {(fields) => (
              <Space direction="vertical" className="w-full select-none">
                {fields.map((field, idx) => {
                  const task = tasks[field.key];
                  console.log(task);

                  return (
                    <Collapse
                      key={task.id}
                      defaultActiveKey={['1']}
                      bordered={false}
                      expandIcon={({ isActive }) => (
                        <CaretRightOutlined rotate={isActive ? 90 : 0} />
                      )}
                      items={[
                        {
                          key: '1',
                          showArrow: false,
                          className:
                            'border-l-4 border-green-500 !rounded-2xl shadow-sm',
                          styles: {
                            header: {
                              paddingLeft: 12,
                            },
                            body: {
                              paddingTop: 8,
                              paddingLeft: 12,
                            },
                          },
                          label: (
                            <div className="flex gap-1">
                              <div className="w-6 h-6 rounded-xl bg-blue-100 flex items-center justify-center flex-shrink-0">
                                <FlagOutlined className="text-blue-500" />
                              </div>
                              {`任务${idx + 1}: ` + task.name}
                            </div>
                          ),
                          children: (
                            <div>
                              <Form.Item
                                labelCol={{ span: 4 }}
                                label="ID"
                                hidden
                                name={[field.name, 'id']}
                                rules={[{ required: true }]}
                              >
                                <Input placeholder="ID" />
                              </Form.Item>
                              <Form.Item
                                labelCol={{ span: 4 }}
                                label="任务名称"
                                name={[field.name, 'name']}
                                style={{ marginBottom: 4 }}
                                hidden
                              >
                                <Input
                                  variant="borderless"
                                  readOnly
                                  placeholder="name"
                                />
                              </Form.Item>
                              <Form.Item
                                label="打卡内容"
                                name={[field.name, 'attendance_content']}
                              >
                                <Input.TextArea
                                  maxLength={10000}
                                  rows={6}
                                  placeholder="分享今天的收获和感受"
                                  onPaste={async (e) => {
                                    // 判断是否包含图片，包含图片的话上传图片
                                    if (e.clipboardData?.files?.length) {
                                      e.preventDefault();

                                      // 获取粘贴板数据
                                      const clipboardData =
                                        // @ts-ignore
                                        e.clipboardData || window.clipboardData;

                                      if (!clipboardData) {
                                        return;
                                      }

                                      // 检查是否有图片文件
                                      const items = clipboardData.items;
                                      if (!items) {
                                        return;
                                      }

                                      const imageFiles = [];
                                      for (let i = 0; i < items.length; i++) {
                                        const item = items[i];
                                        console.log('item', item, i);
                                        // 检查是否为图片类型
                                        if (
                                          item.kind === 'file' &&
                                          item.type.indexOf('image') !== -1
                                        ) {
                                          const file = item.getAsFile();
                                          imageFiles.push(file);
                                        }
                                      }

                                      if (imageFiles.length === 0) {
                                        return;
                                      }

                                      try {
                                        message.loading('上传中...');
                                        setUploadLoading(true);
                                        // 批量上传
                                        const uploadResList = await Promise.all(
                                          imageFiles.map(async (file) => {
                                            const formData = new FormData();
                                            formData.append(
                                              'file',
                                              file as any,
                                            );
                                            const res = await uploadFile(
                                              formData,
                                            );
                                            return res;
                                          }),
                                        );

                                        // 设置到文件映射中
                                        setFileMapping((prev) => ({
                                          ...prev,
                                          [task.id]: [
                                            ...(prev[task.id] || []),
                                            ...uploadResList.map(
                                              (el) => el.path,
                                            ),
                                          ],
                                        }));
                                      } catch (error) {
                                        message.error('上传失败');
                                      } finally {
                                        setUploadLoading(false);
                                        message.destroy();
                                      }
                                    }
                                  }}
                                />
                              </Form.Item>
                              <Form.Item label="附件" noStyle>
                                <Upload
                                  listType="picture-card"
                                  accept="image/png,image/jpeg,image/gif,image/webp"
                                  maxCount={9}
                                  fileList={(
                                    fileMapping[task?.id as string] || []
                                  ).map((url) => ({
                                    url: getFileUrl(url),
                                    uid: url,
                                    status: 'done',
                                    name: url,
                                  }))}
                                  beforeUpload={() => false}
                                  onChange={(info) =>
                                    handleFileChange(info, task)
                                  }
                                >
                                  <div>
                                    {uploadLoading ? (
                                      <LoadingOutlined />
                                    ) : (
                                      <PlusOutlined />
                                    )}
                                    <div style={{ marginTop: 8 }}>上传图片</div>
                                  </div>
                                </Upload>
                              </Form.Item>
                            </div>
                          ),
                        },
                      ]}
                    />
                  );
                })}
              </Space>
            )}
          </Form.List>
        </Form>
      </Drawer>
    </div>
  );
};

export default SubmitAttendance;
