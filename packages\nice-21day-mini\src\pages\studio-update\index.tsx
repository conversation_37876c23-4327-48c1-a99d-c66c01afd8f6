import { Result } from '@/components';
import StudioForm from '@/components/business/StudioForm';
import { useRouterParams, useStudioDetail } from '@/hooks';
import { AtLoadMore } from 'taro-ui';

const StudioUpdate: React.FC = () => {
  const { studio_id } = useRouterParams();

  const { loading, studioDetail } = useStudioDetail(studio_id);
  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  if (!studioDetail?.id) {
    return <Result text="内容不存在或已被删除" />;
  }

  return <StudioForm studio={studioDetail} />;
};

export default StudioUpdate;
