import { useAttendanceLikeTopN } from '@/hooks/useAttendanceLike';
import { ITraining } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import { AtLoadMore } from 'taro-ui';
import AttendanceTimelineItem from '../AttendanceTimelineItem';
import { Result } from '@/components/common';

interface IAttendanceLikeRankProps {
  training: ITraining;
}
const AttendanceLikeRank = ({ training }: IAttendanceLikeRankProps) => {
  const { loading, hotAttendanceLogs } = useAttendanceLikeTopN({
    training_id: training.id,
    count: 20,
  });

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  if (hotAttendanceLogs.length === 0) {
    return <Result />;
  }

  return (
    <View>
      {hotAttendanceLogs.map((attendance) => (
        <AttendanceTimelineItem
          key={attendance.id}
          training_id={training.id}
          attendance={attendance}
          marginSize="small"
        />
      ))}
    </View>
  );
};

export default AttendanceLikeRank;
