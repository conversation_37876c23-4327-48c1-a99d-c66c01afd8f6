import { Card, WeeklyWarning } from '@/components';
import { getTrainingMemberDetail } from '@/service';
import { getFileUrl } from '@/utils';
import {
  ITrainingMember,
  ITrainingTask,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { AtAvatar, AtLoadMore } from 'taro-ui';
import './index.scss';

const renderTaskName = (taskName: string) => {
  if (taskName.startsWith('<21天')) {
    return taskName;
  }
  return `<21天：${taskName}>`;
};

const TrainingMemberDetail: React.FC = () => {
  const { member_id } = Taro.getCurrentInstance().router.params;

  const [loading, setLoading] = useState<boolean>(true);
  const [memberDetail, setMemberDetail] = useState<ITrainingMember>();

  useEffect(() => {
    setLoading(true);
    getTrainingMemberDetail(member_id)
      .then(({ data }) => {
        setMemberDetail(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [member_id]);

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  const { tasks = '' } = memberDetail;
  const tasksArr = parseArrayJson<ITrainingTask>(tasks);

  return (
    <View style={{ marginTop: 20 }}>
      <View
        style={{
          marginBottom: 20,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <AtAvatar image={getFileUrl(memberDetail.user.avatar_url)}></AtAvatar>
        <View>{memberDetail.user.nick_name}</View>
      </View>
      <View className="task-list">
        <WeeklyWarning />
        {tasksArr.length > 0 &&
          tasksArr.map((task, index) => {
            return (
              <Card key={task.id} title={`任务${index + 1}`}>
                {renderTaskName(task.name)}
              </Card>
            );
          })}
      </View>
      {/* <View>
        <AtButton
          size="small"
          type="primary"
          onClick={() =>
            Taro.navigateTo({
              url: `/pages/user-homepage/index?user_id=${memberDetail.user_id}`,
            })
          }
        >
          还可以跳转到用户详情看信息
        </AtButton>
      </View> */}
    </View>
  );
};

export default TrainingMemberDetail;
