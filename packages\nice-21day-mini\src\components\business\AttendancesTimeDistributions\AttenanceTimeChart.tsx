import F2Canvas from 'taro-f2-react';
import { Axis, Chart, Interval, Tooltip } from '@antv/f2';
import { IAttendancesTimeDistributions } from '@nice-people/nice-21day-shared';
import { StyleProps } from '@antv/f2/es/components/axis/types';

interface IAttenanceTimeChartProps {
  id: string;
  data: IAttendancesTimeDistributions[];
  config?: {
    axiosColor?: string;
    axisStyle?: StyleProps<void>;
  };
}

export default ({ id, data, config }: IAttenanceTimeChartProps) => {
  return (
    <F2Canvas id={id}>
      <Chart data={data}>
        <Tooltip />
        <Axis
          field="hours"
          alias="时间"
          formatter={(val: string) => `${val}h`}
          tickCount={9}
          style={{ ...config?.axisStyle }}
        />
        {/* TODO: 取整数 */}
        <Axis
          field="count"
          alias="次数"
          type="linear"
          color={config?.axiosColor}
          style={{ ...config?.axisStyle }}
        />
        <Interval x="hours" y="count" color={config?.axiosColor} />
      </Chart>
    </F2Canvas>
  );
};
