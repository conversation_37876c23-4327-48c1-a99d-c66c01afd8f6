page {
  background: #f8f8f8;
  --q-easy-color: #00af9b;
  --q-medium-color: #ffb800;
  --q-hard-color: #ff2d55;
  --q-other-color: #bfbfbf;

  --default-color: #5dc991;
  --border-color: #eeeeee;

  --progress-color: #ffe599;

  // 点赞的颜色，取的网易云的颜色
  --like-color: #d43c33;
}

.at-button.danger-button {
  color: #ff4949;
  border-color: #ffc7c3;
  background: #ffc7c3;
}

.processing-button {
  background: #6190e8 !important;
  border-color: #6190e8 !important;
}

.iphone-botton-padding-safe {
  padding-bottom: constant(safe-area-inset-bottom);
  /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom);
  /*兼容 IOS>11.2*/
}

.disabled {
  cursor: not-allowed !important;
  pointer-events: none !important;
  opacity: 0.5 !important;
}

.at-load-more {
  height: 120px !important;
  padding-bottom: constant(safe-area-inset-bottom);
  /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom);
  /*兼容 IOS>11.2*/

  & .at-button__text {
    font-size: 28px !important;
  }
}
