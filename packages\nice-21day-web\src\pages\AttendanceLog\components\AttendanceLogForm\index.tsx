import { SelectTraining } from '@/components/SelectTraining';
import { SelectUser } from '@/components/SelectUser';
import {
  createAttendanceLog,
  queryTrainingMemberDetailByTrainingId,
  uploadFile,
} from '@/services';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import {
  EAttendanceLogAuditState,
  EAttendanceState,
  getFileUrl,
  IAttendanceLog,
  IAttendanceTask,
  ITrainingMember,
  ITrainingTask,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  notification,
  Result,
  Row,
  Skeleton,
  Space,
  Upload,
} from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import { UploadChangeParam, UploadFile } from 'antd/lib/upload';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';

interface IAttendanceLogFormProps {
  /** 训练营ID */
  trainingId: string;
  /** 用户ID */
  userId: string;
  /** 给哪一天打卡 */
  attendanceDate?: string;
  attendanceLog?: IAttendanceLog;
}

const AttendanceLogForm: React.FC<IAttendanceLogFormProps> = ({
  trainingId,
  userId,
  attendanceDate,
}) => {
  const [submiting, setSubmiting] = useState(false);
  const [form] = Form.useForm();

  const [uploadLoading, setUploadLoading] = useState(false);

  // 查询用户报名信息
  const [trainingMember, setTrainingMember] = useState<ITrainingMember>();
  const [queryMemberLoading, setQueryMemberLoading] = useState(true);

  // 维护已上传的文件
  // {任务ID: [文件数组]}
  const [fileMapping, setFileMapping] = useState<Record<string, string[]>>({});

  const tasks = useMemo(() => {
    return parseArrayJson<ITrainingTask>(trainingMember?.tasks || '');
  }, [trainingMember]);

  const handleFileChange = useCallback(
    (fileInfo: UploadChangeParam<UploadFile<any>>, task: ITrainingTask) => {
      const mapping = _.cloneDeep(fileMapping);

      // 删除
      if (fileInfo.file.status === 'removed') {
        mapping[task.id] = [...(mapping[task.id] || [])].filter(
          (url) => url !== fileInfo.file.url,
        );
        setFileMapping(mapping);
        return;
      }

      // 上传新增
      const formData = new FormData();
      formData.append('file', fileInfo.file as any);
      message.loading('上传中...');
      setUploadLoading(true);
      uploadFile(formData)
        .then((file) => {
          mapping[task.id] = [...(mapping[task.id] || []), file.path];
          setFileMapping(mapping);
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          setUploadLoading(false);
          setTimeout(() => {
            message.destroy();
          }, 1000);
        });
    },
    [fileMapping],
  );

  const handleFinish = useCallback(
    (values: any) => {
      const { attendance_date, tasks_array = [], ...rest } = values;
      // 检查任务填写情况
      // 组装数据
      const attendanceTasks: IAttendanceTask[] = [];
      tasks_array.forEach((task: IAttendanceTask) => {
        attendanceTasks.push({
          ...task,
          attendance_content: task.attendance_content || '',
          attendance_files: fileMapping[task.id] ?? [],
        });
      });

      console.log(attendanceTasks);

      if (
        attendanceTasks.every(
          (task) =>
            !task.attendance_content && task.attendance_files?.length === 0,
        )
      ) {
        notification.error({
          message: '打卡失败',
          description: '至少打卡一个任务',
        });
        return;
      }

      const data: IAttendanceLog = {
        ...rest,
        attendance_date: dayjs(attendance_date).format('YYYY-MM-DD'),
        attendance_state: EAttendanceState.Attendance,
        attendance_tasks: JSON.stringify(attendanceTasks),
        audit_state: EAttendanceLogAuditState.Valid,
        audit_comment: '审核通过',
      };

      Modal.confirm({
        title: '确定保存吗？',
        onOk: () => {
          setSubmiting(true);
          createAttendanceLog(data)
            .then(() => {
              message.success('打卡成功');
              history.back();
            })
            .catch(() => {
              message.error('打卡失败');
            })
            .finally(() => {
              setSubmiting(false);
            });
        },
      });
    },
    [fileMapping],
  );

  useEffect(() => {
    setQueryMemberLoading(true);
    queryTrainingMemberDetailByTrainingId(trainingId, userId)
      .then((member) => {
        setTrainingMember(member);
      })
      .finally(() => {
        setQueryMemberLoading(false);
      });
  }, [trainingId, userId]);

  if (queryMemberLoading) {
    return <Skeleton loading />;
  }

  if (!trainingMember?.id) {
    return <Result status="error" title="训练营成员不存在或已被删除" />;
  }

  return (
    <ProCard>
      <Form
        name="basic"
        onFinish={handleFinish}
        autoComplete="off"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}
        form={form}
        initialValues={{
          training_id: trainingId,
          user_id: userId,
          attendance_date: dayjs(attendanceDate),
          tasks_array: tasks,
        }}
      >
        <Form.Item label="id" name="id" hidden>
          <Input />
        </Form.Item>

        <Form.Item
          label="用户"
          name="user_id"
          rules={[{ required: true, message: '请选择用户' }]}
        >
          <SelectUser bordered={false} disabled />
        </Form.Item>
        <Form.Item
          label="训练营"
          name="training_id"
          rules={[{ required: true, message: '请选择训练营' }]}
        >
          <SelectTraining bordered={false} disabled />
        </Form.Item>

        <Form.Item
          label="打卡日期"
          name="attendance_date"
          rules={[{ required: true, message: '请选择打卡日期' }]}
        >
          <DatePicker
            format="YYYY-MM-DD"
            allowClear={false}
            bordered={false}
            disabled
          />
        </Form.Item>

        <Form.Item label="任务" name="tasks_array" required>
          <Form.List name="tasks_array">
            {(fields) => (
              <>
                {fields.map((field) => {
                  const taskInfo = tasks[field.key];
                  return (
                    <Card key={taskInfo.id} style={{ marginBottom: 10 }}>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        label="ID"
                        hidden
                        name={[field.name, 'id']}
                        rules={[{ required: true }]}
                      >
                        <Input placeholder="ID" />
                      </Form.Item>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        label="任务名称"
                        name={[field.name, 'name']}
                        style={{ marginBottom: 4 }}
                      >
                        <Input bordered={false} readOnly placeholder="name" />
                      </Form.Item>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        hidden
                        label="任务说明"
                        name={[field.name, 'description']}
                        style={{ marginBottom: 4 }}
                      >
                        <Input
                          bordered={false}
                          readOnly
                          placeholder="description"
                        />
                      </Form.Item>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        label="打卡内容"
                        name={[field.name, 'attendance_content']}
                        rules={[{ required: false }]}
                      >
                        <Input.TextArea
                          maxLength={256}
                          placeholder="打卡内容"
                        />
                      </Form.Item>
                      <Row>
                        <Col offset={4}>
                          <Upload
                            listType="picture-card"
                            accept="image/png, image/jpeg"
                            maxCount={3}
                            fileList={(
                              fileMapping[taskInfo?.id as string] || []
                            ).map((url) => ({
                              url: getFileUrl(url),
                              uid: url,
                              status: 'done',
                              name: url,
                            }))}
                            beforeUpload={() => false}
                            onChange={(info) =>
                              handleFileChange(info, taskInfo)
                            }
                          >
                            <div>
                              {uploadLoading ? (
                                <LoadingOutlined />
                              ) : (
                                <PlusOutlined />
                              )}
                              <div style={{ marginTop: 8 }}>上传图片</div>
                            </div>
                          </Upload>
                        </Col>
                      </Row>
                    </Card>
                  );
                })}
              </>
            )}
          </Form.List>
        </Form.Item>
        <Form.Item label="备注信息" name="description">
          <TextArea rows={4} maxLength={256} placeholder="请填入" />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4 }}>
          <Space>
            <Button type="primary" htmlType="submit" loading={submiting}>
              打卡
            </Button>
            <Button loading={submiting} onClick={() => history.back()}>
              返回
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default AttendanceLogForm;
