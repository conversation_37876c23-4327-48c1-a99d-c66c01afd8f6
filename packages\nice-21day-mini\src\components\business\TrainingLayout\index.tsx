import { ITraining } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import { ReactNode } from 'react';
import TrainingInfo, { ITrainingInfoProps } from '../TrainingInfo';
import './index.scss';

interface ITrainingLayoutProps {
  training: ITraining;
  // 标题
  title: ReactNode;
  // 是否固定标题
  stickyTitle?: boolean;
  trainingProfileProps?: Omit<ITrainingInfoProps, 'training'>;
  children?: ReactNode;
}
const TrainingLayout: React.FC<ITrainingLayoutProps> = ({
  title,
  stickyTitle = false,
  training = {} as ITraining,
  trainingProfileProps = {},
  children,
}) => {
  return (
    <View className="training-layout-wrapper">
      <TrainingInfo training={training} {...trainingProfileProps} />
      {/* 这里可以固定起来： sticky top-0 */}
      <View
        className={`training-layout-title ${
          stickyTitle ? 'sticky top-0 z-10' : ''
        }`}
      >
        {title}
      </View>
      <View className="training-layout-content">{children}</View>
    </View>
  );
};

export default TrainingLayout;
