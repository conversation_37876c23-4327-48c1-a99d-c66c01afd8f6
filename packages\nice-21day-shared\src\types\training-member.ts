import { EBooleanString, EState, IPageParams, ICommonFields } from './global';
import { ITraining } from './training';
import { IUser } from './user';

/** 训练营成员 */
export interface ITrainingMember extends ICommonFields {
  /** 用户ID */
  user_id: string;
  /** 用户详情 */
  user?: IUser;
  /** 训练营ID */
  training_id: string;
  /** 训练营详情 */
  training?: ITraining;
  /** 押金支付状态 */
  payment_state: ETrainingPaymentState;
  /** 是否达标 */
  reached: EBooleanString;
  /** 用户当前积分 */
  score: number;
  /** 积分更新时间 */
  score_updated_at?: string;
  /** 启用状态 */
  state: EState;
  /** 用户个人目标JOSN */
  tasks: string;
  /** 训练营总结 */
  summary: string;

  // 额外的统计信息
  /** 连续打卡天数 */
  max_consecutive_attendance_count?: number;
  /** 总打卡次数 */
  total_attendance_count?: number;
  /** 收获的点赞次数 */
  like_count?: number;
}

/** 押金支付状态 */
export enum ETrainingPaymentState {
  /** 无需支付 */
  NotRequired = 'not_required',
  /** 未支付 */
  Unpaid = 'unpaid',
  /** 已支付 */
  Paid = 'paid',
  /** 已退款 */
  Refunded = 'refunded',
}

/** 押金支付状态映射关系 */
export const TRAINING_PAYMENT_STATE_MAPPING: Record<
  ETrainingPaymentState,
  string
> = {
  [ETrainingPaymentState.NotRequired]: '无需支付',
  [ETrainingPaymentState.Unpaid]: '未支付',
  [ETrainingPaymentState.Paid]: '已支付',
  [ETrainingPaymentState.Refunded]: '已退款',
};

/**
 * 个人任务
 */
export interface ITrainingTask {
  /** ID */
  id: string;
  /** 任务标题 */
  name: string;
  /** 任务详情 */
  description: string;
}

export interface IQueryTrainingMemberParams
  extends IPageParams,
    Partial<
      Pick<
        ITrainingMember,
        'training_id' | 'user_id' | 'payment_state' | 'reached' | 'state'
      >
    > {}

/**
 *  更新积分结果
 */
export interface IRefreshTrainingUserScoreResult {
  successCount: number;
  errorCount: number;
}
