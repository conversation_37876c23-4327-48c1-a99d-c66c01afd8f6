import {
  CloseOutlined,
  FilterOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  ETrainingProgress,
  ITraining,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';
import { Switch } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';

export const useFilter = ({
  list = [],
  training,
}: {
  list: ITrainingMember[];
  training: ITraining;
}) => {
  // 筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showOnlyWithSummary, setShowOnlyWithSummary] = useState(false);

  const [debouncedSearchKeyword, setDebouncedSearchKeyword] = useState('');

  // 防抖处理搜索关键词
  const debouncedSetSearchKeyword = useCallback(
    debounce((keyword: string) => {
      setDebouncedSearchKeyword(keyword);
    }, 300),
    [],
  );

  useEffect(() => {
    debouncedSetSearchKeyword(searchKeyword);
  }, [searchKeyword, debouncedSetSearchKeyword]);

  // 筛选和排序逻辑
  const filteredAndSortedMembers = useMemo(() => {
    let filtered = [...list];

    // 按昵称关键词筛选
    if (debouncedSearchKeyword.trim()) {
      filtered = filtered.filter((member) =>
        member.user?.nick_name
          .toLowerCase()
          .includes(debouncedSearchKeyword.toLowerCase()),
      );
    }

    // 按总结状态筛选
    if (showOnlyWithSummary) {
      filtered = filtered.filter((member) => member.summary);
    }

    return filtered;
  }, [list, debouncedSearchKeyword, showOnlyWithSummary]);

  // 清除所有筛选条件
  const clearAllFilters = () => {
    setSearchKeyword('');
    setShowOnlyWithSummary(false);
  };

  // 检查是否有激活的筛选条件
  const hasActiveFilters = searchKeyword.trim() || showOnlyWithSummary;

  const FilterComp = (
    <div className="bg-white rounded-2xl shadow-sm p-3 mb-3">
      <div className="space-y-4">
        {/* 搜索框 */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchOutlined className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="搜索成员昵称..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            className="block w-full pl-8 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
          />
          {searchKeyword && (
            <button
              type="button"
              onClick={() => setSearchKeyword('')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <CloseOutlined className="text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>

        {/* 筛选选项 */}
        {[ETrainingProgress.Summary, ETrainingProgress.Finished].includes(
          training.progress,
        ) && (
          <div className="flex flex-wrap items-center gap-2">
            <div className="flex items-center space-x-2">
              <FilterOutlined className="text-gray-500" />
              <span className="text-sm text-gray-600">只看提交总结的成员</span>
            </div>

            {/* 只看有总结的用户 */}
            <div className="flex items-center space-x-2 cursor-pointer">
              <Switch
                checked={showOnlyWithSummary}
                onChange={setShowOnlyWithSummary}
              />
            </div>
            {/* 清除筛选按钮 */}
            {hasActiveFilters && (
              <button
                type="button"
                onClick={clearAllFilters}
                className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <CloseOutlined className="w-3 h-3" />
                <span>清除筛选</span>
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );

  return {
    FilterComp,
    filterResult: filteredAndSortedMembers,
  };
};
