name: Build and Deploy to Server

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build'
        required: true
        default: 'master'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # Checkout code from specified branch
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          repository: nice-people-frontend-community/nice-21day
          ref: ${{ github.event.inputs.branch }}

      # Set up Node.js 18.x
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'

      # Setup pnpm
      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: latest

      # 获取 pnpm store 目录
      - name: Get pnpm store directory
        id: pnpm-cache
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      # Cache pnpm store
      - name: Cache pnpm store
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      # Install dependencies
      - name: Install dependencies
        run: |
          pnpm install

      # Build
      - name: Build
        run: |
          pnpm run build:shared
          pnpm run build:web

      # Package build output
      - name: Archive build output
        run: |
          cd packages/nice-21day-web/dist
          tar -zcvf nice-21day-web.tar.gz *
          ls -l
          cd -

      # Copy artifact to server via SSH
      - name: Copy artifact to server
        uses: appleboy/scp-action@v1
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          timeout: 120s
          source: 'packages/nice-21day-web/dist/nice-21day-web.tar.gz'
          target: '/webapp_deploy'
          port: 22
          capture_stdout: true
          debug: true
          strip_components: 3 # 去除三级目录，变成 webapp_deploy/nice-21day-web.tar.gz
          overwrite: true # 覆盖目标文件

      # Deploy on server via SSH
      - name: Deploy and restart service on server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            # backup
            rm -rf /opt/components/nginx/html/nice-21day-web-bak
            cp -r /opt/components/nginx/html/nice-21day-web /opt/components/nginx/html/nice-21day-web-bak

            # remove old file
            rm -rf /opt/components/nginx/html/nice-21day-web/*

            # unzip new file
            sudo tar -zxvf /webapp_deploy/nice-21day-web.tar.gz -C /opt/components/nginx/html/nice-21day-web

            # restart nginx in docker
            cd /opt/scripts
            docker-compose restart nginx

      - name: Done
        run: echo "✅ Deployment finished successfully!"
