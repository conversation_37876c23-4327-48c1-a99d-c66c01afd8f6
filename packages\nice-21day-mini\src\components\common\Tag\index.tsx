import { View } from '@tarojs/components';
import React from 'react';
import './index.scss';

export const COLOR_MAP = {
  orange: {
    bg: '#FDEBE0',
    iconBg: '#F8DBCB',
  },
  blue: {
    bg: '#E0F1FD',
    iconBg: '#CBD8F8',
  },
  yellow: {
    bg: '#FDF5E0',
    iconBg: '#FAE8AB',
  },
  purple: {
    bg: '#E0E5FD',
    iconBg: '#D9CBF8',
  },
  salmon: {
    bg: '#F6DED3',
    iconBg: '#FCC0A7',
  },
  lavender: {
    bg: '#FBF0FF',
    iconBg: '#EED9FF',
  },
  buff: {
    bg: '#FFFBEB',
    iconBg: '#FFEFC7',
  },
  torquoise: {
    bg: '#F3FEFF',
    iconBg: '#ABD6E3',
  },
};

export const tagColors = Object.keys(COLOR_MAP);

interface ITagPorps {
  color: keyof typeof COLOR_MAP;
  icon: string;
  title: string;
  description?: string;
}
const Tag: React.FC<ITagPorps> = ({ color, icon, title, description }) => {
  return (
    <View
      className="c_tag px-32px py-32px flex items-center gap-16px rounded-32px"
      style={{ background: COLOR_MAP[color].bg }}
    >
      <View
        className="c_tag__icon flex w-80px h-80px rounded-16px justify-center items-center text-32px"
        style={{ background: COLOR_MAP[color].iconBg }}
      >
        <View>{icon}</View>
      </View>
      <View className="c_tag__content flex flex-col gap-8px">
        <View className="c_tag__content-title text-[32px] font-bold">
          {title}
        </View>
        {description && (
          <View className="c_tag__content-desc text-[24px]">{description}</View>
        )}
      </View>
    </View>
  );
};

export default Tag;
