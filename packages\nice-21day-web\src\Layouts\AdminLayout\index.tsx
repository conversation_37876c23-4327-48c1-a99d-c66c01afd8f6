import { useCurrentUser } from '@/hooks/useCurrentUser';
import { PageContainer, ProBreadcrumb } from '@ant-design/pro-components';
import { history, Outlet, useLocation } from '@umijs/max';
import { Button, Result } from 'antd';
import styles from './index.less';

const AdminLayout = () => {
  const location = useLocation();
  const { currentUser } = useCurrentUser();

  if (
    !location.pathname.includes('/admin/login') &&
    currentUser &&
    currentUser?.role !== 'admin'
  ) {
    return (
      <Result
        status="403"
        title="没有权限查看该页面"
        extra={
          <Button onClick={() => history.push('/admin/login')} type="primary">
            管理员登录
          </Button>
        }
      />
    );
  }

  return (
    <PageContainer
      title={false}
      className={styles.layout}
      breadcrumbRender={() => <ProBreadcrumb />}
      // 水印
      // waterMarkProps={{ content: DEFAULT_NAME, gapY: 160 }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default AdminLayout;
