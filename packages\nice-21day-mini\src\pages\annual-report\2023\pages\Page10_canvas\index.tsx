import {
  drawSingleText,
  getImageInfo,
} from '@/components/business/Certificate/helper';
import { formatNumber } from '@nice-people/nice-21day-shared';
import { Can<PERSON>, ScrollView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';
import { EMPTY_KEYWORD, REPORT_2023_KEYWORD_LOCAL_KEY } from '../../contants';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

export const CANVAS_ID = 'report-2023-canvas';
const BACKGROUND_2X =
  'https://oss.yayujs.com/alioss/2023-12-23/bd851921-1525-4e60-80ce-4db59e4fef6a.png';
// 'https://oss.yayujs.com/alioss/2023-12-23/a4af2c71-7933-469c-af8e-d675046a3043.png';

const BACKGROUND_WIDTH = 750;
const BACKGROUND_HEIGHT = 1334;

export default ({ user, switchSwiper, reportData }: IPageProps) => {
  const { statistics } = reportData;

  const yearKeyword = Taro.getStorageSync(REPORT_2023_KEYWORD_LOCAL_KEY);

  const [bgInfo, setBgInfo] =
    useState<Taro.getImageInfo.SuccessCallbackResult>();

  const draw = useCallback(async () => {
    if (!bgInfo) {
      return;
    }
    const ctx = Taro.createCanvasContext(CANVAS_ID);
    const imgDisplayWidth = BACKGROUND_WIDTH / 2;
    const imgDisplayHeight = BACKGROUND_HEIGHT / 2;

    ctx.drawImage(bgInfo.path, 0, 0, imgDisplayWidth, imgDisplayHeight);

    ctx.save();
    // 恢复上下文
    ctx.restore();

    // 底部的角标
    drawSingleText({
      ctx,
      x: imgDisplayWidth - 160,
      y: imgDisplayHeight - 70,
      // y: imgDisplayHeight - 40,
      text: '21天微习惯训练营',
      fontSize: 14,
      color: '#4d759c',
      moveCenter: false,
    });

    // 年度关键词
    drawSingleText({
      ctx,
      x: 180,
      y: 190,
      text: yearKeyword || EMPTY_KEYWORD,
      fontSize: 24,
      color: '#0F59A1',
    });

    // 人名
    drawSingleText({
      ctx,
      x: 50,
      y: 330,
      text: `@${user.nick_name}`,
      fontSize: 24,
      color: '#0F59A1',
      moveCenter: false,
    });

    [
      `参加训练营：${statistics?.training_joined_count || 0} 个`,
      `制定任务：${statistics?.task_total_count || 0} 个`,
      `累计打卡：${statistics?.attendance_total_count || 0} 次`,
      `输出文字：${formatNumber(
        (statistics?.training_summary_words_total_count || 0) +
          (statistics?.attendance_words_count || 0),
      )} 字`,
    ].forEach((text, idx) => {
      drawSingleText({
        ctx,
        x: 60,
        // 每行高53px
        y: 330 + 53 * (idx + 1),
        text,
        fontSize: 22,
        color: '#0F59A1',
        moveCenter: false,
      });
    });

    ctx.draw();
  }, [yearKeyword, statistics, bgInfo]);

  useEffect(() => {
    getImageInfo(BACKGROUND_2X).then((val) => {
      setBgInfo(val);
    });
  }, []);

  useEffect(() => {
    draw();
  }, [draw]);

  return (
    <>
      <View className={styles.canvas}>
        <ScrollView scrollX scrollY style={{ height: BACKGROUND_HEIGHT / 2 }}>
          <Canvas
            canvas-id={CANVAS_ID}
            id={CANVAS_ID}
            style={{
              width: `${BACKGROUND_WIDTH / 2}px`,
              height: `${BACKGROUND_HEIGHT / 2}px`,
              margin: '0 auto',
            }}
          />
        </ScrollView>
      </View>
    </>
  );
};
