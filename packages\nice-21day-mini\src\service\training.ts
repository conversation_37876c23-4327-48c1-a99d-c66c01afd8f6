import request from '@/utils/request';
import {
  ETrainingPaymentState,
  ETrainingProgress,
  IAttendanceLog,
  IPageFactory,
  IPageParams,
  IQueryTrainingParams,
  ITraining,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';

export interface IQueryMyTrainingParams extends IPageParams {
  progress?: ETrainingProgress;
  /** 多个训练营进度 */
  progresses?: string;
  attendance_date?: string;
  user_id?: string;
}
/**
  报名训练营参数细腻
 */

interface IJoinInParam {
  description: string;
  training_id: string;
  tasks: string;
  score: number;
  reached: string;
  payment_state: ETrainingPaymentState;
}
/**
 * 获取报名阶段的训练营（分页）
 */
export const getTrainingList = async (params: IQueryTrainingParams) => {
  return await request.get<IPageFactory<ITraining>>('/trainings', params);
};

/**
 * 获取和我相关的训练营列表：已报名、注册中、已完成（分页）
 */
export const getMyTrainingList = async (params: IQueryMyTrainingParams) => {
  return await request.get<IPageFactory<ITraining>>('/my/trainings', params, {
    hideError: true,
  });
};

/**
 * 查询某个训练营详情
 */
export const getTrainingDetail = async (training_id: string) => {
  return await request.get<ITraining>(`/trainings/${training_id}`);
};

/**
 * 查询某个训练营下的报名信息
 */
export const getTrainingJoinInDetail = async (training_id: string) => {
  return await request.get<ITrainingMember>(
    `/my/trainings/${training_id}/join-in`,
  );
};

/**
 * 报名某个训练营
 */
export const createTrainingJoinIn = async (
  training_id: string,
  params: IJoinInParam,
) => {
  return await request.post(`/my/trainings/${training_id}/join-in`, params);
};

/**
 * 某个训练营报名详情
 */
export const updateTrainingJoinIn = async (
  training_id: string,
  join_id: string,
  params: IJoinInParam,
) => {
  return await request.put(
    `/my/trainings/${training_id}/join-in/${join_id}`,
    params,
  );
};

/**
 * 放弃报名某个训练营
 */
export const giveupTrainingJoinIn = async (
  training_id: string,
  join_id: string,
) => {
  return await request.delete(
    `/my/trainings/${training_id}/join-in/${join_id}`,
  );
};

// 训练营推荐列表
export const queryRecommends = async (params: {
  /**训练营id */
  training_id: string;
  /**打卡日期 */
  attendance_date: string;
  /**推荐个数 */
  limit?: number;
  /**排除用户ID */
  exclude_user_id?: string;
}) => {
  return await request.get<IAttendanceLog[]>(
    `/user-attendance-logs/recommends`,
    params,
  );
};
