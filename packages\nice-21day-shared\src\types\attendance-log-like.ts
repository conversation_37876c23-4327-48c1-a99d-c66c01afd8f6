import { ICommonFields } from './global';
import { IUser } from './user';

/** 打卡记录点赞列表 */
export interface IAttendanceLogLike extends ICommonFields {
  /** 点赞人用户ID */
  user_id: string;
  /** 点赞人详情 */
  user?: IUser;
  /** 打卡记录ID */
  attendance_log_id: string;
  /** 训练营ID */
  training_id: string;
  /** 打卡记录所属人 */
  training_user_id: string;
}

/** 新建点赞的 body 参数 */
export interface ICreateAttendanceLogLikeData
  extends Pick<IAttendanceLogLike, 'user_id' | 'attendance_log_id'> {}

/** 查询点赞参数 */
export interface IQueryAttendanceLogLikeParams
  extends Partial<
    Pick<IAttendanceLogLike, 'user_id' | 'attendance_log_id' | 'training_id'>
  > {}

/** 查询某个训练营下点赞次数最多的打卡记录 */
export interface IQueryAttendanceLogLikeTopParams
  extends Partial<Pick<IAttendanceLogLike, 'training_id'>> {
  count?: number;
  atteneance_date?: string;
}
