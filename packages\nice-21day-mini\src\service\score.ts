import request from '@/utils/request';
import { IIntegralLog, ITrainingMember } from '@nice-people/nice-21day-shared';

/**
 * 获取用户在某个用户下的积分变更记录
 */
export const getMyScoreLogs = async (trainingId: string) => {
  return await request.get<IIntegralLog[]>(
    `/my/trainings/${trainingId}/score-logs`,
  );
};

/**
 * 更新我在某个训练营下的积分
 */
export const refreshMyTrainingScore = async (trainingId: string) => {
  return await request.post<ITrainingMember>(
    `/my/trainings/${trainingId}/refresh-scores`,
    {},
  );
};
