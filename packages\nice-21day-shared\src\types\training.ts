import { IAttendanceLog } from './attendance-log';
import { EBooleanString, EState, ICommonFields, IPageParams } from './global';
import { ITrainingMember } from './training-member';
/**训练营列表 */
export interface ITrainingList {
  page: number;
  size: number;
  total: number;
  limit: number;
  offset: number;
  rows: ITraining[];
}
/** 训练营 */
export interface ITraining extends ICommonFields {
  id: string;
  /** 训练营名称 */
  name: string;
  /** 训练营类型 */
  type: ETrainingType;
  /** 训练营期数 */
  period: number;
  /** 开始日期 */
  start_time: string;
  /** 结束日期 */
  end_time: string;
  /** 押金费用 */
  fee: number;
  /** 达标积分 */
  standard_score: number;
  /** 训练营进度 */
  progress: ETrainingProgress;
  /** 开启状态 */
  state: EState;

  // 以下字段都是关联计算出来的
  // ------------
  /** 参加人数 */
  join_user_count?: number;
  /** 打卡人数 */
  attendance_user_count?: number;
  /** 当前登录人是否参加报名了 */
  inlude_me?: EBooleanString;
  /** 当前登录人的报名信息 */
  my_join_info?: ITrainingMember;
  /** 我在某一天的打卡记录 */
  my_attendance_info?: IAttendanceLog;
  /** 我的打卡天数 */
  my_attendance_count?: number;
}

/** 训练营类型 */
export enum ETrainingType {
  /** 21天综合训练营 */
  '21Day' = '21day',
  /** 英语 */
  English = 'english',
  /** 运动 */
  Sports = 'sports',
}

/** 训练营类型映射关系 */
export const TRAINING_TYPE_MAPPING: Record<ETrainingType, string> = {
  [ETrainingType['21Day']]: '21天训练营',
  [ETrainingType.English]: '英语打卡',
  [ETrainingType.Sports]: '运动打卡',
};

/** 训练营进度 */
export enum ETrainingProgress {
  /** 报名中 */
  Registering = 'registering',
  /** 打卡进行中 */
  Processing = 'processing',
  /** 茶话会 */
  Summary = 'summary',
  /** 已结束 */
  Finished = 'finished',
}

/** 训练营进度映射关系 */
export const TRAINING_PROGRESS_MAPPING: Record<ETrainingProgress, string> = {
  [ETrainingProgress.Registering]: '报名中',
  [ETrainingProgress.Processing]: '打卡中',
  [ETrainingProgress.Summary]: '茶话会',
  [ETrainingProgress.Finished]: '已结束',
};

/** 查询训练营参数 */
export interface IQueryTrainingParams
  extends IPageParams,
    Partial<Pick<ITraining, 'name' | 'type' | 'state' | 'progress'>> {
  /** 多个训练营进度 */
  progresses?: string;
}
