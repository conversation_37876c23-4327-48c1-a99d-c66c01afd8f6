import {
  ICurrentUser,
  ISystemSettingMap,
} from '@nice-people/nice-21day-shared';
import { createContext } from 'react';

interface IAppContext {
  // 当前登录人
  currentUser: ICurrentUser;
  refreshCurrentUser?: () => void;

  // 系统配置
  systemSettings: ISystemSettingMap;
  querySystemSettingsLoading: boolean;
}
export const AppContext = createContext<IAppContext>({
  currentUser: {} as ICurrentUser,
  systemSettings: {},
  querySystemSettingsLoading: true,
});
