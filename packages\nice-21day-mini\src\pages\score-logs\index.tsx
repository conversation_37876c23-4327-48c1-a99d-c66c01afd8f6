import { getMyScoreLogs } from '@/service';
import { IIntegralLog } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { AtTimeline } from 'taro-ui';
import { AtTimelineProps } from 'taro-ui/types/timeline';

const ScoreLogs = () => {
  const { training_id } = Taro.getCurrentInstance().router.params;

  const [scoreLogs, setScoreLogs] = useState<IIntegralLog[]>([]);

  useEffect(() => {
    getMyScoreLogs(training_id).then(({ data }) => {
      setScoreLogs(data);
    });
  }, [training_id]);

  const timelineData: AtTimelineProps['items'] = useMemo(() => {
    return scoreLogs.map((log) => ({
      icon: 'check-circle',
      color: 'green',
      title: dayjs(log.created_at).format('YYYY-MM-DD HH:mm:ss'),
      content: [
        `积分变更: ${log.previous_score} => ${log.score}`,
        `变更原因: ${log.trigger_type}`,
        `变更日志: ${log.description}`,
      ],
    }));
  }, [scoreLogs]);

  return (
    <View style={{ padding: 20 }}>
      <AtTimeline items={timelineData}></AtTimeline>
    </View>
  );
};

export default ScoreLogs;
