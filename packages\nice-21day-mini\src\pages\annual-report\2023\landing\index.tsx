import { AppContext } from '@/appContext';
import { View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import { useContext } from 'react';
import { Landing } from '../pages/Page1';

const UserReport2023Landing: React.FC = () => {
  const { currentUser } = useContext(AppContext);

  useShareAppMessage(() => {
    return {
      title: '2023年度打卡报告',
      imageUrl:
        'https://oss.yayujs.com/alioss/2023-12-24/e55d43b9-deb6-41f1-884e-90d9036bbfc3.png',
    };
  });

  return (
    <View className="h-[100vh]">
      <Landing
        user={currentUser || ({} as any)}
        onClick={() => {
          Taro.navigateTo({
            url: '/pages/annual-report/2023/index',
          });
        }}
      />
    </View>
  );
};

export default UserReport2023Landing;
