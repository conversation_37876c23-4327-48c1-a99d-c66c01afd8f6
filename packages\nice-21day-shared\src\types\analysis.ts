import { IAttendanceLog } from './attendance-log';
import { ITraining } from './training';
import { ITrainingMember } from './training-member';
import { IUser } from './user';

export interface IAttendancesTimeDistributions {
  /** 小时区间 */
  hours: string;
  /** 打卡天数 */
  count: number;
}

export interface IAttendanceCalendar {
  user: IUser;
  training: ITraining;
  trainingUser: ITrainingMember;
  attendanceLogs: IAttendanceLog[];
}

/** 打卡时间分布统计 */
export interface IAttendancesTimeDistributions {
  hours: string;
  count: number;
}

/**
 * 打卡日期分布统计
 */
export interface IAttendancesDateDistributions {
  date: string;
  count: number;
}

/** 训练营打卡率统计 */
export interface ITrainingAttendanceRate {
  /** 训练营详情 */
  training: ITraining;
  /** 参加人数 */
  join_user_count: number;

  /** 全部打卡记录数：正常打卡+请假*/
  total_attendance_logs_count: number;
  /** 总正常打卡数 */
  total_attendance_count: number;
  /** 总请假数 */
  total_level_count: number;
  /** 积分达标率 */
  total_storard_rate: number;
  /** 训练营打卡率，正常打卡+请假 */
  total_attendance_rate: number;

  /** 某一天的打卡日期 */
  attendance_date: string;
  /** 某一天的正常打卡数 */
  attendance_count: number;
  /** 某一天的请假数 */
  level_count: number;
  /** 某一天的全部打卡记录数：正常打卡+请假 */
  attendance_logs_count: number;
  /** 某一天的训练营打卡率，正常打卡+请假 */
  attendance_rate: number;
}
