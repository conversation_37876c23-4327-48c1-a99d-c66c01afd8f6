import { AppContext } from '@/appContext';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useContext } from 'react';
import styles from './index.module.scss';

const Report2023Entry = () => {
  const { systemSettings } = useContext(AppContext);

  if (systemSettings['2023_report_enable'] === 'true') {
    return (
      <View className="px-20px my-20px w-full box-border">
        <View
          className={styles.container}
          onClick={() => {
            Taro.navigateTo({
              url: '/pages/annual-report/2023/index',
            });
          }}
        >
          <View className={styles.btn}>开启报告</View>
        </View>
      </View>
    );
  }

  return null;
};

export default Report2023Entry;
