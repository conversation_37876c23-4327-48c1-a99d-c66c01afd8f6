import { queryAttendanceLogByTrainingId } from '@/services';
import { IAttendanceLog } from '@nice-people/nice-21day-shared';
import { useSearchParams } from '@umijs/max';
import { Result, Skeleton } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import AttendanceLogForm from '../components/AttendanceLogForm';

const CreateAttendanceLog: React.FC = () => {
  const [searchParams] = useSearchParams();

  const [loading, setLoading] = useState(true);
  const [attendanceLog, setAttendanceLog] = useState<IAttendanceLog>();

  const trainingId = useMemo(
    () => searchParams.get('training_id'),
    [searchParams],
  );
  const userId = useMemo(() => searchParams.get('user_id'), [searchParams]);
  const attendanceDate = useMemo(
    () => searchParams.get('attendance_date'),
    [searchParams],
  );

  // 检查是否已经存在打卡
  const queryAttendanceLog = useCallback(() => {
    if (!trainingId || !userId || !attendanceDate) {
      return;
    }
    setLoading(true);
    queryAttendanceLogByTrainingId(trainingId, userId, attendanceDate)
      .then((res) => {
        setAttendanceLog(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [searchParams]);

  useEffect(() => {
    queryAttendanceLog();
  }, [searchParams]);

  if (loading) {
    return <Skeleton loading />;
  }

  if (attendanceLog?.id) {
    return <Result status="warning" title="已打卡，无需再次打卡" />;
  }

  return (
    <AttendanceLogForm
      trainingId={trainingId!}
      userId={userId!}
      attendanceDate={attendanceDate!}
    />
  );
};

export default CreateAttendanceLog;
