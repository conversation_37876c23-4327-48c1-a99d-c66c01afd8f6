import { useCurrentUser } from '@/hooks/useCurrentUser';
import { queryAttendanceLogs } from '@/services';
import { EBooleanString } from '@nice-people/nice-21day-shared';
import { useOutletContext } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Alert, Card, Skeleton } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import AttendanceTimeline from '../../components/AttendanceTimeline';
import SubmitAttendance from '../../components/SubmitAttendance';
import { IUserLayoutContext } from '../UserLayout';

export default () => {
  const { trainingUser } = useOutletContext<IUserLayoutContext>();
  const { currentUser } = useCurrentUser();

  const { loading, data, refresh } = useRequest(() =>
    queryAttendanceLogs({
      training_id: trainingUser.training_id,
      user_id: trainingUser.user_id,
      with_user: EBooleanString.YES,
      page: 1,
      size: 50,
    }),
  );

  const isTodayAttendance = useMemo(() => {
    return data?.rows?.find(
      (item) => item.attendance_date === dayjs().format('YYYY-MM-DD'),
    );
  }, [data?.rows]);

  if (loading) {
    return (
      <>
        {Array(4)
          .fill(1)
          .map((_item, idx) => (
            <Card
              key={idx}
              className="mb-[10px]"
              styles={{
                body: {
                  padding: 12,
                },
              }}
            >
              <Skeleton avatar paragraph={{ rows: 4 }} />
            </Card>
          ))}
      </>
    );
  }

  return (
    <>
      {/* 判断今天是否已经打卡 */}
      {!loading &&
        currentUser?.id === trainingUser.user_id &&
        (isTodayAttendance ? (
          <Alert
            className="mb-2"
            showIcon
            message="今天已打卡"
            type="success"
          />
        ) : (
          <Alert
            className="mb-2"
            showIcon
            message="今天还没有打卡，快去打卡吧"
            type="warning"
            action={
              <SubmitAttendance
                attendanceDate={dayjs().format('YYYY-MM-DD')}
                onFinish={() => {
                  refresh?.();
                }}
              />
            }
          />
        ))}

      <AttendanceTimeline
        training={trainingUser.training!}
        scene="training-user-detail"
        loading={loading}
        list={data?.rows || []}
        onRefresh={refresh}
      />
    </>
  );
};
