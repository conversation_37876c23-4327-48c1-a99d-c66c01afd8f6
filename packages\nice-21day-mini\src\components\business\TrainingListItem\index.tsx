import { AppContext } from '@/appContext';
import { useUserCall } from '@/hooks';
import cover from '@/static/cover.png';
import {
  EAttendanceState,
  ETrainingProgress,
  formatNumber,
  ITraining,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Button, Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ReactNode, useContext, useMemo } from 'react';

import './index.scss';

interface ITrainingListItemProps {
  training: ITraining;
  extra?: ReactNode;
  /**
   * 匿名场景
   * 这时候需要动态计算证书按钮和打卡按钮的显示
   */
  anonymous?: boolean;
}
const TrainingListItem: React.FC<ITrainingListItemProps> = ({
  training: {
    id,
    name,
    progress,
    start_time,
    end_time,
    join_user_count,
    attendance_user_count,
    inlude_me,
    description,
    my_attendance_info,
    my_join_info,
  },
  anonymous = false,
  extra,
}) => {
  const { currentUser } = useContext(AppContext);
  const { isMyself, callPrefix } = useUserCall(
    currentUser,
    my_join_info?.user_id,
  );

  const userId = useMemo(() => {
    return my_join_info?.user_id || currentUser?.id;
  }, [my_join_info, currentUser]);

  const attendanceStateText = useMemo(() => {
    if (!my_attendance_info) {
      return '去打卡';
    }
    if (my_attendance_info.attendance_state === EAttendanceState.Leave) {
      return '已请假';
    }
    return '已打卡';
  }, [my_attendance_info]);

  // const countdown = useMemo(() => {
  //   const diffHours = dayjs(dayjs(end_time).endOf('days')).diff(
  //     dayjs(),
  //     'hours',
  //     true,
  //   );
  //   const days = Math.trunc(diffHours / 24);
  //   const hours = diffHours - days * 24;
  //   return {
  //     days,
  //     hours,
  //   };
  // }, [end_time]);

  return (
    <View className="card_wrapper">
      {/* 当前进度 */}
      <View className="progress-flag">
        {TRAINING_PROGRESS_MAPPING[progress]}
      </View>
      <View
        className="training_content"
        onClick={() => {
          // 打卡中
          if (progress === ETrainingProgress.Processing) {
            Taro.navigateTo({
              url: `/pages/analytics/training-attendance-analysis/index?training_id=${id}`,
            });
            return;
          }
          // 总结、结束
          Taro.navigateTo({
            url: `/pages/attendance-leader-board/index?training_id=${id}`,
          });
        }}
      >
        <View className="training_content_left">
          <Image className="cover" src={cover} />
        </View>
        <View className="training_content_right">
          <View className="training_date">
            <View>
              {start_time} ~ {end_time}
            </View>
            {progress !== ETrainingProgress.Registering && (
              <View
                className="text-sm"
                onClick={() =>
                  Taro.navigateTo({
                    url: `/pages/attendance-leader-board/index?training_id=${id}`,
                  })
                }
                style={{ fontSize: 14, color: '#999' }}
              >
                排行榜
                <View className="at-icon at-icon-chevron-right" />
              </View>
            )}
            {/* <View>
              <Text className="mr-1">还剩</Text>
              <AtCountdown
                isShowDay
                isCard
                isShowHour
                day={countdown.days}
                hours={countdown.hours}
              />
            </View> */}
          </View>
          <View className="training_name">{name}</View>
          <ScrollView className="training_descripton">
            <Text className="training_descripton_text">{description}</Text>
          </ScrollView>
        </View>
      </View>
      <View className="training_footer">
        <View className="training_extrainfo">
          <View
            className="at-icon at-icon-tags"
            style={{
              color: 'var(--default-color)',
              marginRight: 6,
              fontSize: 16,
            }}
          />
          {/* 报名中 */}
          {progress === ETrainingProgress.Registering && (
            <Text
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/training-member-list/index?training_id=${id}`,
                })
              }
            >
              已有 <Text className="user-count">{join_user_count}</Text> 人参加
            </Text>
          )}
          {/* 打卡中 */}
          {progress === ETrainingProgress.Processing && (
            <Text
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/attendance-timeline/index?training_id=${id}`,
                })
              }
            >
              共有{' '}
              <Text className="user-count">
                {formatNumber(attendance_user_count || 0)}
              </Text>{' '}
              人次打卡
            </Text>
          )}
          {/* 茶话会阶段 */}
          {progress === ETrainingProgress.Summary && (
            <Text
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/attendance-timeline/index?training_id=${id}`,
                })
              }
            >
              共有{' '}
              <Text className="user-count">
                {formatNumber(attendance_user_count || 0)}
              </Text>{' '}
              人次打卡
            </Text>
          )}
          {/* 已结束 */}
          {progress === ETrainingProgress.Finished && (
            <Text
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/attendance-leader-board/index?training_id=${id}`,
                })
              }
            >
              共有 <Text className="user-count">{join_user_count}</Text> 人参加
            </Text>
          )}
        </View>
        <View>
          {
            // 报名中
            progress === ETrainingProgress.Registering && currentUser.id && (
              <Button
                className={`!w-170px button ${
                  !inlude_me ? 'processing-button' : ''
                }`}
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/join-training/index?training_id=${id}`,
                  });
                }}
              >
                <View className="flex items-center justify-center gap-0.5">
                  {inlude_me ? '已报名' : '去报名'}
                  <View className="at-icon at-icon-chevron-right" />
                </View>
              </Button>
            )
          }
          {
            // 打卡中
            currentUser.id &&
              inlude_me &&
              progress === ETrainingProgress.Processing && (
                <View className="flex gap-1">
                  <Button
                    className="button !w-170px"
                    onClick={() => {
                      Taro.navigateTo({
                        url: `/pages/attendance-timeline-user/index?training_id=${id}&user_id=${userId}`,
                      });
                    }}
                  >
                    <View className="flex items-center justify-center gap-0.5">
                      {callPrefix}打卡
                      <View className="at-icon at-icon-chevron-right" />
                    </View>
                  </Button>
                  {isMyself && (
                    <Button
                      className={`!w-170px button ${
                        !inlude_me ? 'processing-button' : ''
                      }`}
                      onClick={() => {
                        Taro.navigateTo({
                          url: `/pages/attendance/index?training_id=${id}`,
                        });
                      }}
                    >
                      <View className="flex items-center justify-center gap-0.5">
                        {attendanceStateText}
                        <View className="at-icon at-icon-chevron-right" />
                      </View>
                    </Button>
                  )}
                </View>
              )
          }
          {
            // 打卡结束
            progress === ETrainingProgress.Summary &&
              (!anonymous || inlude_me) && (
                <View className="flex gap-1">
                  <Button
                    className="button !w-170px"
                    onClick={() => {
                      Taro.navigateTo({
                        url: `/pages/training-certificate/index?training_id=${id}&user_id=${userId}`,
                      });
                    }}
                  >
                    <View className="flex items-center justify-center gap-0.5">
                      {callPrefix}证书
                      <View className="at-icon at-icon-chevron-right" />
                    </View>
                  </Button>
                  <Button
                    className="button !w-170px"
                    onClick={() => {
                      Taro.navigateTo({
                        url: `/pages/user-training-summary/index?training_id=${id}&user_id=${userId}`,
                      });
                    }}
                  >
                    <View className="flex items-center justify-center gap-0.5">
                      {callPrefix}总结
                      <View className="at-icon at-icon-chevron-right" />
                    </View>
                  </Button>
                </View>
              )
          }
          {
            // 已结束
            progress === ETrainingProgress.Finished && (
              <View className="flex gap-1">
                {(!anonymous || inlude_me) && (
                  <Button
                    className="button"
                    style={{ width: 90 }}
                    onClick={() => {
                      Taro.navigateTo({
                        url: `/pages/training-certificate/index?training_id=${id}&user_id=${userId}`,
                      });
                    }}
                  >
                    <View className="flex items-center justify-center gap-0.5">
                      {callPrefix}证书
                      <View className="at-icon at-icon-chevron-right" />
                    </View>
                  </Button>
                )}

                <Button
                  className="button"
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/pages/attendance-leader-board/index?training_id=${id}`,
                    });
                  }}
                >
                  <View className="flex items-center justify-center gap-0.5">
                    已结束
                    <View className="at-icon at-icon-chevron-right" />
                  </View>
                </Button>
              </View>
            )
          }
        </View>
      </View>
      {extra && <View className="training_extra">{extra}</View>}
    </View>
  );
};

export default TrainingListItem;
