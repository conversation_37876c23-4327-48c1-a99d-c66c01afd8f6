import { AppContext } from '@/appContext';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ListWrapperHandler,
  Result,
  StudioItem,
} from '@/components';
import { usePageShowAgain } from '@/hooks';
import { getStudios } from '@/service';
import { IQueryStudioParams, IStudio } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useContext, useRef } from 'react';
import { AtButton } from 'taro-ui';

const StudioList: React.FC = () => {
  const ref = useRef<ListWrapperHandler>();
  const { currentUser, systemSettings } = useContext(AppContext);

  usePageShowAgain(() => {
    ref.current?.refresh();
  });

  // 兼容代码
  if (systemSettings['studio_enable'] === 'false') {
    return (
      <Result
        text={
          <View className="flex flex-col items-center gap-2">
            <View>个人版小程序暂不支持</View>
            <View>敬请期待</View>
          </View>
        }
      />
    );
  }

  return (
    <View className="px-2.5">
      <View
        className="relative flex justify-between items-center h-7.5 py-2.5 sticky top-0 z-10"
        style={{ background: '#f8f8f8' }}
      >
        <View>
          <AtButton
            size="small"
            circle
            type="secondary"
            className="!bg-transparent"
            onClick={() => {
              ref.current?.refresh();
            }}
          >
            <View className="flex items-center justify-center gap-0.5">
              刷新数据
              <View className="at-icon at-icon-reload" />
            </View>
          </AtButton>
        </View>
        <View>声音创作</View>
        <View>
          <AtButton
            size="small"
            circle
            type="secondary"
            className="!bg-transparent"
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/studio-create/index',
              });
            }}
          >
            <View className="flex items-center justify-center gap-0.5">
              录制声音
              <View className="at-icon at-icon-chevron-right" />
            </View>
          </AtButton>
        </View>
      </View>

      <ListWrapper<IStudio, IQueryStudioParams>
        ref={ref}
        height="100%"
        requestFn={getStudios}
        refresherEnabled={false}
        params={{}}
        emptyText="没有找到相关的声音创造数据"
        renderItem={(studio) => (
          <StudioItem
            key={studio.id}
            studio={studio}
            currentUser={currentUser}
            onAvatarClick={() => {
              Taro.navigateTo({
                url: `/pages/studio-list-user/index?user_id=${studio.user?.id}`,
              });
            }}
          />
        )}
      />
    </View>
  );
};

export default StudioList;
