import { View } from '@tarojs/components';

interface IRecordingBtnProps {
  /** 是否在录音中 */
  isRecording: boolean;
  onClick: () => void;
}

const RecordingButton: React.FC<IRecordingBtnProps> = ({
  isRecording = false,
  onClick,
}) => {
  return (
    <View
      onClick={onClick}
      className="bg-gray-200 w-100rpx h-100rpx flex justify-center items-center rounded-full recording-button"
    >
      <View
        className={`bg-red-500 ${
          isRecording ? 'w-1/3 h-1/3' : 'w-9/12 h-9/12'
        }`}
        style={{
          transition: 'all 0.3s ease-in-out',
          borderRadius: `${isRecording ? '0rpx' : '50%'}`,
        }}
      />
    </View>
  );
};

export default RecordingButton;
