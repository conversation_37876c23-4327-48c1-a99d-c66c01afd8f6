import React, { useCallback, useRef, useState } from 'react';
// 组件库
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Switch, Tooltip } from 'antd';
// 工具库
import {
  EBooleanString,
  EState,
  ITrainingMember,
  ITrainingTask,
  parseArray<PERSON>son,
  TRAINING_PAYMENT_STATE_MAPPING,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { history, useRequest, useSearchParams } from '@umijs/max';
// service
import {
  deleteTrainingMember,
  queryTrainingMembers,
  updateTrainingMemberState,
  updateTrainingAllUserScore,
  updateUserScore,
} from '@/services';
// 子组件
import { SelectTraining } from '@/components/SelectTraining';
import { SelectUser } from '@/components/SelectUser';
import TasksModal from '@/components/TaskModal';
import { UserInfo } from '@/components/UserInfo';
import { PRO_TABLE_DEFAULT_CONFIG } from '@/constants';
import { generateProTableValueEnum } from '@/utils';
import dayjs from 'dayjs';

interface IProps {
  training_id: string;
}

const TrainingMember: React.FC<IProps> = (props) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [tasksDatasource, setTasksDatasource] = useState<ITrainingTask[]>([]);
  const [searchParams] = useSearchParams();
  const training_id =
    searchParams.get('training_id') || props.training_id || undefined;

  // 启用、禁用
  const { run: updateTrainingMemberStateRun } = useRequest(
    updateTrainingMemberState,
    {
      manual: true,
      onSuccess: () => {
        actionRef?.current?.reload();
      },
    },
  );

  const { run: deleteTrainingMemberRun } = useRequest(deleteTrainingMember, {
    manual: true,
    onSuccess: () => {
      actionRef?.current?.reload();
    },
  });

  // 刷新一个人的积分
  const refreshUserScore = useCallback(
    (trainingUserId: string) => {
      updateUserScore(trainingUserId).then(() => {
        message.success('刷新成功');
        actionRef?.current?.reload();
      });
    },
    [training_id],
  );

  // 刷新训练营下所有人的积分
  const refreshAllUserScore = useCallback(() => {
    if (!training_id) {
      return;
    }
    updateTrainingAllUserScore(training_id).then(() => {
      message.success('刷新成功');
      actionRef?.current?.reload();
    });
  }, [training_id]);

  const columns: ProColumns<ITrainingMember>[] = [
    {
      title: '成员',
      dataIndex: 'nick_name',
      key: 'nick_name',
      search: false,
      width: 180,
      fixed: 'left',
      render: (_, record) => {
        return (
          <UserInfo
            nick_name={record.user?.nick_name || record.user_id}
            avatar_url={record?.user?.avatar_url}
          />
        );
      },
    },
    {
      title: '成员',
      dataIndex: 'user_id',
      hideInTable: true,
      valueType: 'select',
      renderFormItem: (_, props) => <SelectUser {...props} />,
    },
    {
      title: '训练营',
      dataIndex: 'training_id',
      valueType: 'select',
      fixed: 'left',
      width: 200,
      ellipsis: true,
      // hideInSearch: !!training_id,
      initialValue: training_id || undefined,
      render: (_, record) => {
        return record.training?.name || record.training_id;
      },
      renderFormItem: (_, props) => <SelectTraining {...props} />,
    },
    {
      title: '训练营进度',
      dataIndex: 'progress',
      width: 120,
      search: false,
      renderText: (_, record) => record.training?.progress,
      valueEnum: generateProTableValueEnum(TRAINING_PROGRESS_MAPPING),
    },
    {
      title: '目标',
      dataIndex: 'tasks',
      width: 120,
      search: false,
      ellipsis: true,
      render: (_, record) => {
        return (
          <a
            onClick={() => {
              setTasksDatasource(parseArrayJson<ITrainingTask>(record.tasks));
              setModalVisible(true);
            }}
          >
            共 {JSON.parse(record?.tasks)?.length} 项
          </a>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 120,
      search: false,
      hideInTable: true,
      render: (state, record) => {
        return (
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={state === EState.Enable}
            onChange={(checked: boolean) => {
              const state = checked ? EState.Enable : EState.Disable;
              updateTrainingMemberStateRun(record.id, state);
            }}
          />
        );
      },
    },
    {
      title: '积分',
      dataIndex: 'score',
      width: 100,
      search: false,
      ellipsis: true,
      render: (_, { score, score_updated_at }) => {
        if (!score_updated_at) {
          return score;
        }
        return (
          <Tooltip
            title={`最后更新于：${dayjs(score_updated_at).format(
              'YYYY-MM-DD HH:mm:ss',
            )}`}
          >
            {score}
          </Tooltip>
        );
      },
    },
    {
      title: '达标情况',
      dataIndex: 'reached',
      width: 100,
      valueEnum: {
        [EBooleanString.YES]: {
          text: '已达标',
          status: 'success',
        },
        [EBooleanString.NO]: {
          text: '未达标',
          status: 'error',
        },
      },
    },
    {
      title: '押金支付',
      dataIndex: 'payment_state',
      width: 100,
      valueEnum: generateProTableValueEnum(TRAINING_PAYMENT_STATE_MAPPING),
    },
    {
      title: '报名时间',
      dataIndex: 'created_at',
      valueType: 'dateTime',
      width: 220,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      valueType: 'option',
      width: 240,
      search: false,
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Popconfirm
            title={`确定要刷新[${record.user?.nick_name}]的积分吗？`}
            onConfirm={() => refreshUserScore(record.id)}
          >
            <Button type="link">刷新积分</Button>
          </Popconfirm>
          <Button
            type="link"
            onClick={() =>
              history.push(`/admin/training-member/${record.id}/update`)
            }
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗？"
            okText="确定"
            cancelText="取消"
            onConfirm={() => {
              deleteTrainingMemberRun(record.id);
            }}
          >
            <Button type="link">删除</Button>
          </Popconfirm>
          <Button
            type="link"
            onClick={() =>
              history.push(
                `/admin/attendance-log/calendar?user_id=${record.user_id}&training_id=${record.training_id}`,
              )
            }
          >
            打卡记录
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<ITrainingMember>
        headerTitle="训练营成员列表"
        columns={columns}
        actionRef={actionRef}
        rowKey="id"
        {...PRO_TABLE_DEFAULT_CONFIG}
        request={async ({ pageSize, current, ...rest }) => {
          const res = await queryTrainingMembers({
            training_id,
            ...rest,
            size: pageSize!,
            page: current!,
          });
          return {
            data: res?.rows,
            total: res?.total,
            success: true,
          };
        }}
        scroll={{ x: 1000 }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() =>
              history.push(
                `/admin/training-member/create${
                  training_id ? `?training_id=${training_id}` : ''
                }`,
              )
            }
          >
            新建
          </Button>,
          ...(training_id
            ? [
                <Popconfirm
                  key="refresh-score"
                  title={`确定要刷新所有成员的积分吗？`}
                  onConfirm={refreshAllUserScore}
                >
                  <Button icon={<ReloadOutlined />} type="primary">
                    刷新积分
                  </Button>
                </Popconfirm>,
              ]
            : []),
        ]}
      />
      <TasksModal
        visible={modalVisible}
        handleOk={() => setModalVisible(false)}
        tasks={tasksDatasource}
      />
    </>
  );
};

export default TrainingMember;
