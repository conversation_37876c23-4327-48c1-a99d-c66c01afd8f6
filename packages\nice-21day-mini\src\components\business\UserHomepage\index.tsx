import { AppContext } from '@/appContext';
import { Card, ListWrapper, Result, TrainingListItem } from '@/components';
import { ACCESS_TOKEN_LOCAL_KEY } from '@/constant';
import { usePageShowAgain, useUserProfile } from '@/hooks';
import { useIsfollowed } from '@/hooks/useFollow';
import {
  getMyTrainingList,
  IQueryMyTrainingParams,
  logout,
  updateMyAccessToken,
} from '@/service';
import { createFollow, deleteFollow } from '@/service/follow';
import { getUserAgeText } from '@/utils';
import { exportAttendanceLogs } from '@/utils/exportAttendanceLogs';
import { getFileUrl, ITraining } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import { useCallback, useContext, useMemo, useState } from 'react';
import {
  AtAvatar,
  AtButton,
  AtFab,
  AtList,
  AtListItem,
  AtLoadMore,
  AtTabs,
  AtTabsPane,
  AtTag,
} from 'taro-ui';
import './index.scss';

const DEFAULT_TAB = 0;

// 打卡提醒模板
const attendanceNoticeTemplateId =
  'yRR8o-Jcit1vpij245TmLyCAs7zHTK7P-V8CVdGClu8';
// 新评论通知模板
const newCommentNoticeTemplateId =
  'VfNlxqd-N7Thu06i0IUOqkTEP61scAB90q0jCYTPJUI';

// 随机生成背景图
// https://picsum.photos/750/340
const COVER_IMAGE = getFileUrl(
  '/alioss/2023-04-24/82aedaee-3bb2-4b46-afb5-d96511d263a9.jpeg',
);

const webPage = 'https://nice.yayujs.com/user/';

interface IUserHomepageProps {
  userId: string;
}
const UserHomepage: React.FC<IUserHomepageProps> = ({ userId }) => {
  const { currentUser, refreshCurrentUser, querySystemSettingsLoading } =
    useContext(AppContext);
  const [currentTab, setCurrentTab] = useState<number>(DEFAULT_TAB);

  const {
    loading: queryUserLoading,
    userProfile,
    fetchUserProfile,
  } = useUserProfile(userId);

  const isMyself = useMemo(
    () => currentUser?.id === userProfile?.id,
    [currentUser, userProfile],
  );

  const tabList = useMemo(() => {
    if (isMyself) {
      return [
        { title: '训练营' },
        { title: '授权登录' },
        { title: '帮助中心' },
      ];
    }

    return [{ title: '训练营' }];
  }, [isMyself]);

  // 查询当前登录人对这个人有没有关注
  const { isfollowed, fetchIsFollowed } = useIsfollowed(
    currentUser?.id,
    userId,
  );

  usePageShowAgain(() => fetchUserProfile(false));

  const userAgeText = useMemo(() => {
    return getUserAgeText(userProfile?.birthday);
  }, [userProfile?.birthday]);

  useShareAppMessage(() => {
    return {
      title: `${userProfile?.nick_name}的个人主页`,
      path: `/pages/user-homepage/index?user_id=${userId}`,
    };
  });

  const handleTabChange = (tabIndex: number) => {
    setCurrentTab(tabIndex);
  };

  const handleExportAttendanceFile = useCallback(() => {
    exportAttendanceLogs({
      user_id: currentUser.id,
    });
  }, [currentUser.id]);

  const handleFollow = useCallback(() => {
    const title = isfollowed ? '取消关注' : '关注';

    Taro.showModal({
      title: title,
      content: `确定${title}吗？`,
      success: function (res) {
        if (res.confirm) {
          (isfollowed ? deleteFollow : createFollow)(
            currentUser?.id,
            userId,
          ).then(() => {
            // 重新拉取关注情况
            fetchIsFollowed();
            fetchUserProfile(false);
          });
        }
      },
    });
  }, [isfollowed, currentUser, userId]);

  const handleRefreshAccessToken = useCallback(() => {
    Taro.showModal({
      title: '确定重置 Token 吗？',
      content: `重置Token后，web 原有登录态将失效`,
      success: function (res) {
        if (res.confirm) {
          updateMyAccessToken()
            .then(() => {
              fetchUserProfile(false);
              Taro.showToast({
                title: '操作成功',
                icon: 'success',
                duration: 2000,
              });
            })
            .catch(() => {
              Taro.showToast({
                title: '操作失败',
                icon: 'error',
                duration: 2000,
              });
            });
        }
      },
    });
  }, [fetchUserProfile]);

  // 关注情况
  const renderActionComp = useCallback(() => {
    if (isMyself) {
      return (
        <AtTag
          circle
          active
          onClick={() => {
            Taro.navigateTo({
              url: '/pages/my-profile/index',
            });
          }}
        >
          编辑资料
          <View className="at-icon at-icon-chevron-right" />
        </AtTag>
      );
    }

    if (isfollowed) {
      return (
        <AtTag type="primary" circle active onClick={handleFollow}>
          <View className="flex justify-center items-center gap-1">
            <View className="at-icon at-icon-heart-2" />
            已关注
          </View>
        </AtTag>
      );
    }
    return (
      <AtTag type="primary" circle active onClick={handleFollow}>
        <View className="flex justify-center items-center gap-1">
          <View className="at-icon at-icon-add" />
          关注
        </View>
      </AtTag>
    );
  }, [isfollowed, isMyself]);

  if (queryUserLoading || querySystemSettingsLoading) {
    return <AtLoadMore status="loading" />;
  }

  if (!userProfile?.id) {
    return <Result text="未找到人员信息" />;
  }

  return (
    <View className="homepage-page">
      <View
        className="homepage__cover"
        style={{
          backgroundImage: `url(${COVER_IMAGE})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
        }}
      />
      <View className="homepage__content">
        <View className="homepage__user-card rounded-xl">
          <View className="relative rounded-md flex flex-col items-center justify-center gap-1.5">
            {/* 头像 */}
            <AtAvatar
              circle
              size="large"
              image={getFileUrl(userProfile.avatar_url)}
              className="-mt-100px"
            />
            {/* 昵称 */}
            <View className="text-lg font-medium">{userProfile.nick_name}</View>
            {/* 关注信息 */}
            <View className="flex items-center gap-3 text-xs text-gray-400">
              <View
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/user-following/index?user_id=${userId}`,
                  });
                }}
              >
                {userProfile.followee_count} 关注
              </View>
              <View
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/user-followers/index?user_id=${userId}`,
                  });
                }}
              >
                {userProfile.follower_count} 粉丝
              </View>
            </View>
            {/* tag 标签 */}
            <View className="flex items-center gap-3">
              {userProfile.location && (
                <AtTag type="primary" size="small">
                  {userProfile.location}
                </AtTag>
              )}
              {userAgeText && (
                <AtTag type="primary" size="small">
                  {userAgeText}
                </AtTag>
              )}
              {(userProfile.location || userAgeText) && (
                <AtTag
                  type="primary"
                  size="small"
                  className="!flex justify-center items-center !px-1"
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/pages/user-detail/index?user_id=${userId}`,
                    });
                  }}
                >
                  <View className="at-icon at-icon-chevron-right" />
                </AtTag>
              )}
            </View>
            {/* 如果是我自己，显示编辑资料 */}
            <View className="homepage__user-action">{renderActionComp()}</View>
          </View>
          <AtTabs
            current={currentTab}
            tabList={tabList}
            onClick={handleTabChange}
          ></AtTabs>
        </View>
        <View className="homepage__tab">
          <AtTabs
            current={currentTab}
            tabList={tabList}
            onClick={handleTabChange}
          >
            {/* <AtTabsPane current={currentTab} index={0}>
              <Result text="正在施工中" />
            </AtTabsPane> */}
            <AtTabsPane current={currentTab} index={0}>
              <ListWrapper<ITraining, IQueryMyTrainingParams>
                height="100%"
                requestFn={getMyTrainingList}
                refresherEnabled={false}
                params={{
                  user_id: userId,
                  size: 20,
                }}
                emptyText="没有找到参加的训练营"
                renderItem={(record) => (
                  <TrainingListItem key={record.id} training={record} />
                )}
              />
            </AtTabsPane>
            {/* <AtTabsPane current={currentTab} index={1}>
              {
                // 兼容代码
                systemSettings['studio_enable'] === 'false' ? (
                  <Result
                    text={
                      <View className="flex flex-col items-center gap-2">
                        <View>个人版小程序暂不支持</View>
                        <View>敬请期待</View>
                      </View>
                    }
                  />
                ) : (
                  <ListWrapper<IStudio, IQueryStudioParams>
                    height="100%"
                    requestFn={getStudios}
                    refresherEnabled={false}
                    params={{
                      creator_id: userId,
                    }}
                    emptyText="没有找到相关的声音创造数据"
                    renderItem={(studio) => (
                      <StudioItem studio={studio} currentUser={currentUser} />
                    )}
                  />
                )
              }
            </AtTabsPane> */}
            {/* 授权登录 */}
            <AtTabsPane current={currentTab} index={1}>
              <Card styles={{ marginTop: 8 }}>
                <View className="mb-2 flex flex-col gap-1">
                  <View>
                    1. 可以使用 Token 在 web 端登录，也可以直接扫码进行登录
                  </View>
                  <View>
                    2. 重置 Token 后，原有登录态将会失效，需要重新登录
                  </View>

                  <View className="text-lg">{webPage}</View>
                </View>

                {userProfile.access_token ? (
                  <>
                    <View className="flex gap-2">
                      <AtButton
                        type="primary"
                        circle
                        className="w-half"
                        onClick={() => {
                          Taro.setClipboardData({
                            data: userProfile.access_token,
                          });
                        }}
                      >
                        <View className="flex items-center gap-1">
                          <View className="at-icon at-icon-money" />
                          复制 Token
                        </View>
                      </AtButton>
                      <AtButton
                        type="primary"
                        circle
                        className="danger-button w-half"
                        onClick={handleRefreshAccessToken}
                      >
                        <View className="flex items-center gap-1">
                          <View className="at-icon at-icon-repeat-play" />
                          重置 Token
                        </View>
                      </AtButton>
                    </View>
                  </>
                ) : (
                  <AtButton
                    type="primary"
                    circle
                    onClick={handleRefreshAccessToken}
                  >
                    <View className="flex items-center gap-1">
                      <View className="at-icon at-icon-repeat-play" />
                      生成 Token
                    </View>
                  </AtButton>
                )}
                <View className="mt-2">
                  <AtButton
                    type="primary"
                    circle
                    onClick={() => {
                      Taro.setClipboardData({
                        data: webPage,
                      });
                    }}
                  >
                    <View className="flex items-center gap-1">
                      <View className="at-icon at-icon-money" />
                      复制 Web 端地址
                    </View>
                  </AtButton>
                </View>
                <View className="mt-2">
                  <AtButton type="primary" circle>
                    <View className="flex items-center gap-1">
                      <View className="at-icon at-icon-camera" />
                      扫码登录
                    </View>
                  </AtButton>
                </View>
              </Card>
            </AtTabsPane>
            {/* 帮助中心 */}
            <AtTabsPane current={currentTab} index={2}>
              <Card styles={{ marginTop: 8 }}>
                <AtList hasBorder={false}>
                  <AtListItem
                    title="微信通知提醒"
                    arrow="right"
                    onClick={async () => {
                      Taro.requestSubscribeMessage({
                        tmplIds: [
                          attendanceNoticeTemplateId,
                          newCommentNoticeTemplateId,
                        ],
                        entityIds: [],
                        success: (res) => {
                          if (res.errMsg === 'requestSubscribeMessage:ok') {
                            Taro.showToast({
                              title: '授权成功',
                            });
                          }
                        },
                        fail: (result) => {
                          console.log(result);
                        },
                      });
                    }}
                  />
                  <AtListItem
                    title="问题反馈"
                    arrow="right"
                    onClick={() => {
                      Taro.showModal({
                        title: '问题反馈',
                        content: `小程序无法直接打开外部页面，请点击按钮复制反馈链接`,
                        confirmText: '复制链接',
                        success: function (res) {
                          if (res.confirm) {
                            Taro.setClipboardData({
                              data: 'https://www.yuque.com/forms/share/cba230d3-a7e4-4b76-b1f8-a5b1336add5a',
                            });
                          }
                        },
                      });
                    }}
                  />
                  <AtListItem
                    title="打卡积分计算说明"
                    arrow="right"
                    onClick={() => {
                      Taro.navigateTo({
                        url: '/pages/help/index',
                      });
                    }}
                  />

                  <AtListItem
                    title="退出登录"
                    arrow="right"
                    hasBorder={false}
                    onClick={() => {
                      Taro.showModal({
                        title: '退出登录',
                        content: '确定退出登录吗？',
                        success: async function (res) {
                          if (res.confirm) {
                            try {
                              await logout();
                              Taro.setStorageSync(ACCESS_TOKEN_LOCAL_KEY, '');
                              refreshCurrentUser?.();
                              Taro.reLaunch({
                                url: '/pages/index/index',
                              });
                            } catch (error) {
                              console.log(error);
                            }
                          }
                        },
                      });
                    }}
                  />
                </AtList>
              </Card>
            </AtTabsPane>
          </AtTabs>
        </View>
      </View>
      {isMyself && (
        <View className="fixed right-5 bottom-5">
          <AtFab onClick={handleExportAttendanceFile}>
            <text className="at-fab__icon at-icon at-icon-download-cloud"></text>
          </AtFab>
        </View>
      )}
    </View>
  );
};

export default UserHomepage;
