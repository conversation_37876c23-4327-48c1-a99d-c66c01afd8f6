import { history, Outlet } from '@umijs/max';
import { Tabs } from 'antd';
import { useCallback, useContext } from 'react';
import AttendanceGuideButton from '../../components/AttendanceGuideButton';
import { FloatButtonWrapper } from '../../components/FloatButtonWrapper';
import { TrainingLayoutContext } from '../Layout';

export default () => {
  const { training } = useContext(TrainingLayoutContext);

  const getTabKey = useCallback(() => {
    const url = `/training/${training?.id}`;
    const tabKey = location.pathname.replace(`${url}/`, '');
    if (tabKey && tabKey !== '/') {
      return tabKey;
    }

    return '';
  }, [location, training?.id]);

  return (
    <>
      <Tabs
        className="mt-4"
        activeKey={getTabKey()}
        items={[
          {
            key: 'attendance-board',
            label: '积分排行榜',
            children: <Outlet context={training} />,
          },
          {
            key: 'attendance-timeline',
            label: '打卡记录',
            children: <Outlet context={training} />,
          },
        ]}
        onChange={(key) => history.push(`/training/${training.id}/${key}`)}
      />

      {/* 浮动框，引导去打卡 */}
      <FloatButtonWrapper>
        <AttendanceGuideButton training={training} />
      </FloatButtonWrapper>
    </>
  );
};
