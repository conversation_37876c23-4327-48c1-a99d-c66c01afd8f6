import { AtListItem } from 'taro-ui';
import { AtListItemProps } from 'taro-ui/types/list';
import styles from './index.module.scss';

interface ICustomListItemProps extends AtListItemProps {
  layout?: /** 水平 */ 'horizontal' | /** 垂直 */ 'vertical';
}
const CustomListItem: React.FC<ICustomListItemProps> = ({
  layout = 'horizontal',
  ...restProps
}) => {
  return (
    <AtListItem
      className={`${styles.list__item} ${styles[`list__item--${layout}`]}`}
      {...restProps}
    />
  );
};

export default CustomListItem;
