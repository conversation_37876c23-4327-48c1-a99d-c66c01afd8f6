import Feedback from '@/components/Feedback';
import UserHeader from '@/components/UserHeader';
import { DEFAULT_NAME, LOGO_URL, SLOGAN } from '@/constants';
import { Helmet, Outlet } from '@umijs/max';
import { App, ConfigProvider, FloatButton } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import styles from './index.module.less';

const UserLayout = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <App>
        <Helmet>
          <title>{DEFAULT_NAME}</title>
          <meta name="description" content={SLOGAN} />
          <meta property="og:title" content={DEFAULT_NAME} />
          <meta property="og:description" content={SLOGAN} />
          <meta property="og:image" content={LOGO_URL} />
          <meta name="referrer" content="no-referrer" />
        </Helmet>

        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
          <UserHeader />
          <div className={styles.layout}>
            <Outlet />
          </div>
        </div>
        <FloatButton.Group shape="circle">
          <Feedback />
          <FloatButton.BackTop />
        </FloatButton.Group>
      </App>
    </ConfigProvider>
  );
};

export default UserLayout;
