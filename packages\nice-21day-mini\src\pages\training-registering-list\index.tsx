import {
  ListWrapper,
  ListWrapperHandler,
  TrainingListItem,
} from '@/components';
import { getTrainingList } from '@/service';
import {
  EState,
  ETrainingProgress,
  IQueryTrainingParams,
  ITraining,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import { useRef, useState } from 'react';
import './index.scss';

export default function TrainingRegisteringList() {
  const ref = useRef<ListWrapperHandler>(null);

  const [isFirstShow, setIsFirstShow] = useState(true);
  useDidShow(() => {
    if (!isFirstShow) {
      ref.current?.refresh();
      return;
    }

    setIsFirstShow(false);
  });

  return (
    <View>
      <View className="home_wrapper">
        <ListWrapper<ITraining, IQueryTrainingParams>
          ref={ref}
          height="100%"
          requestFn={getTrainingList}
          params={{
            progress: ETrainingProgress.Registering,
            state: EState.Enable,
            size: 20,
          }}
          emptyText="当前没有报名阶段的训练营"
          renderItem={(record) => (
            <TrainingListItem key={record.id} training={record} />
          )}
        />
      </View>
    </View>
  );
}
