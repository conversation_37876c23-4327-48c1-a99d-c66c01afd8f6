import { queryAttendanceComments } from '@/services';
import { useRequest } from 'ahooks';
import { Empty, List } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import CommentItem from './CommentItem';

export interface IAttendanceCommentsRef {
  refresh: () => void;
  scrollToTop: () => void;
}
export interface IAttendanceCommentsProps {
  attendance_id: string;
}

const AttendanceComments = forwardRef<
  IAttendanceCommentsRef,
  IAttendanceCommentsProps
>(({ attendance_id }, ref) => {
  const wrapRef = useRef<HTMLDivElement>(null);

  const { loading, data, run, refreshAsync } = useRequest(
    queryAttendanceComments,
    {
      manual: true,
    },
  );

  useEffect(() => {
    run({
      target_id: attendance_id,
      page: 1,
      size: 50,
    });
  }, [attendance_id]);

  useImperativeHandle(ref, () => ({
    refresh: refreshAsync,
    scrollToTop: () => {
      wrapRef.current?.scrollIntoView({
        behavior: 'smooth',
      });
    },
  }));

  return (
    <div ref={wrapRef}>
      <List
        className="comment-list"
        loading={loading}
        itemLayout="horizontal"
        dataSource={data?.rows || []}
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无评论"
            />
          ),
        }}
        renderItem={(comment) => (
          <CommentItem
            comment={comment}
            onReplyFinished={() => {
              refreshAsync();
            }}
          />
        )}
      />
    </div>
  );
});

export default AttendanceComments;
