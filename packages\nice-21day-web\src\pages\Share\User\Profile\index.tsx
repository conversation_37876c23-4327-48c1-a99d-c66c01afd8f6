import WxamapIconButton from '@/components/WxamapIconButton';
import { findUserTrainings, getwxacode } from '@/services';
import { formatNumber, getFileUrl } from '@nice-people/nice-21day-shared';
import { useOutletContext } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Popover, Spin } from 'antd';
import { IUserLayoutContext } from '../Layout';

import Training, { TrainingSkeleton } from '@/pages/Share/components/Training';
import Banner from '../../components/Banner';

export default () => {
  const { user } = useOutletContext<IUserLayoutContext>();

  const { loading, data } = useRequest(findUserTrainings, {
    ready: Boolean(user.id),
    refreshDeps: [user.id],
    defaultParams: [{ user_id: user.id, page: 1, size: 100 }],
  });

  const {
    data: imgUrl,
    loading: qrCodeLoading,
    run: getQrCode,
  } = useRequest(
    () =>
      getwxacode({
        path: `pages/user-homepage/index?user_id=${user.id}`,
      }),
    {
      ready: <PERSON><PERSON><PERSON>(user.id),
      refreshDeps: [user.id],
    },
  );

  return (
    <div className="py-4">
      {/* 个人信息卡片 */}
      <div className="bg-white rounded-2xl shadow-sm p-3 mx-3 ">
        <div className="flex items-center space-x-4 justify-between gap-2">
          <div className="flex items-center space-x-4">
            <img
              src={getFileUrl(user.avatar_url)}
              alt="头像"
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <h2 className="text-xl font-bold text-gray-800">
                {user.nick_name}
              </h2>
              <p className="text-gray-600">{user.about_me}</p>
            </div>
          </div>
          <div>
            <Popover
              placement="bottomRight"
              onOpenChange={(open) => {
                if (open && !imgUrl) {
                  getQrCode();
                }
              }}
              content={
                <Spin spinning={qrCodeLoading}>
                  {imgUrl ? (
                    <img
                      src={imgUrl}
                      style={{ width: 200, height: 200 }}
                      alt="小程序二维码"
                    />
                  ) : (
                    <div className="w-[200px] h-[200px]"></div>
                  )}
                </Spin>
              }
            >
              {/* WARNING: 这里必须得加一个 div 才行 */}
              <div>
                <WxamapIconButton />
              </div>
            </Popover>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4 mt-3">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(data?.total || 0)}
            </div>
            <div className="text-sm text-gray-600">参与训练营</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatNumber(user?.followee_count || 0)}
            </div>
            <div className="text-sm text-gray-600">关注</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-pink-600">
              {formatNumber(user?.follower_count || 0)}
            </div>
            <div className="text-sm text-gray-600">粉丝</div>
          </div>
        </div>
      </div>

      <div className="px-4 space-y-3 pb-3">
        {/* 加入更多训练营 */}
        <Banner />

        {loading ? (
          <div className="space-y-3">
            <TrainingSkeleton />
            <TrainingSkeleton />
            <TrainingSkeleton />
          </div>
        ) : (
          <>
            {(data?.rows || []).map((row) => (
              <Training key={row.id} training={row} />
            ))}
          </>
        )}
      </div>
    </div>
  );
};
