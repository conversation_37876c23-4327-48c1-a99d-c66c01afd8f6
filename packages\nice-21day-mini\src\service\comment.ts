import request from '@/utils/request';
import {
  IComment,
  ICreateCommentParams,
  IPageFactory,
  IQueryCommentParams,
} from '@nice-people/nice-21day-shared';

/**
 * 获取打卡记录列表（分页）
 */
export const queryAttendanceComments = async (params: IQueryCommentParams) =>
  await request.get<IPageFactory<IComment>>(
    '/comments/attendance-comments',
    params,
  );

/**
 * 提交打卡记录的评论
 */
export const createAttendanceComment = async (data: ICreateCommentParams) =>
  await request.post<IComment>('/comments/attendance-comments', data);
