import { Button, Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import {
  AtModal,
  AtModalAction,
  AtModalContent,
  AtNoticebar,
  AtTag,
} from 'taro-ui';
import styles from './index.module.scss';

const contactQRCode =
  'https://oss.yayujs.com/alioss/2024-01-01/0578cc0a-4a00-4390-a728-175b2fe48504.png';

interface IContactProps {
  showDesc?: boolean;
}
export const Contact = ({ showDesc = false }: IContactProps) => {
  const [showDialog, setShowDialog] = useState<boolean>(false);

  const handlePreview = () => {
    Taro.previewImage({
      current: contactQRCode,
      urls: [contactQRCode],
      complete: () => {
        Taro.showToast({
          title: '长按添加好友',
          icon: 'none',
          duration: 3000,
        });
      },
    });
  };

  const model = (
    <AtModal isOpened={showDialog} closeOnClickOverlay={false}>
      <AtModalContent>
        <View className={styles.contactDialogContent}>
          <View className={styles.contactTitle}>
            <View>加入微习惯大本营</View>
            <View>可获得更好的打卡体验</View>
            <View>请联系我们的班主任</View>
          </View>
          <Image onClick={handlePreview} src={contactQRCode} />
          <View className="text-center text-[#999]">点击二维码可预览</View>
        </View>
      </AtModalContent>
      <AtModalAction>
        <Button onClick={() => setShowDialog(false)}>我知道了</Button>
      </AtModalAction>
    </AtModal>
  );

  if (showDesc) {
    return (
      <View>
        <AtNoticebar icon="volume-plus">
          加入微习惯大本营可获得更好的打卡体验，请联系我们的班主任
          <View className="text-right flex justify-end">
            <AtTag
              size="small"
              active
              circle
              onClick={() => setShowDialog(true)}
            >
              <View>联系班主任</View>
            </AtTag>
          </View>
        </AtNoticebar>
        {model}
      </View>
    );
  }
  return (
    <>
      <AtTag active circle onClick={() => setShowDialog(true)}>
        <View className="flex gap-6px items-center justify-center">
          <Image className="w-30px h-30px" src={require('./icon.svg')} />
          <View>联系班主任</View>
        </View>
      </AtTag>
      {model}
    </>
  );
};
