import { Card } from '@/components';
import { View } from '@tarojs/components';
import { useShareAppMessage } from '@tarojs/taro';
import './index.scss';

const Help = () => {
  useShareAppMessage(() => {
    return {
      title: `帮助中心`,
      path: `/pages/help/index`,
    };
  });

  return (
    <View className="help-page p-2">
      <Card title="打卡规则">
        <View className="at-article">
          <View className="at-article__p">
            凌晨 4 点之前可正常提交、编辑昨天的打卡。例如：
          </View>
          <View className="at-article__p">
            [2023-04-07 04:00 ~ 2023-04-08 04:00] 这个时间段内可以打卡、编辑
            2023-04-07
          </View>

          <View className="at-article__p">
            如果你想在凌晨 4 点之前打卡当天日期，目前是不可以的哦
          </View>
        </View>
      </Card>
      <Card title="积分计算规则">
        <View className="at-article">
          <View className="at-article__p">
            初始积分为 0，计算每次打卡得分后累加。
          </View>
          <View className="at-article__p">
            每次打卡，排除周任务后的其他子任务，1 个子任务未完成，则减 0.5
            分，上限为减 1 分。一次打卡内容得分计算公式如下：
          </View>
          <View className="at-article__p !text-red-300">
            1 - Math.min(1, 未完成子任务数量 * 0.5)
          </View>
          <View className="at-article__p">
            周任务认定：任务名称中包含『每周』、『周任务』关键字则认为是周任务。
          </View>
        </View>
      </Card>
    </View>
  );
};

export default Help;
