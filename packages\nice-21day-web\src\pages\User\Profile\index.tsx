import { UserInfo } from '@/components/UserInfo';
import { queryUserDetail } from '@/services';
import {
  EBooleanString,
  IUser,
  USER_GENDER_MAPPING,
  USER_LOVE_STATE_MAPPING,
} from '@nice-people/nice-21day-shared';
import { useParams } from '@umijs/max';
import {
  Card,
  Descriptions,
  Divider,
  Image,
  Result,
  Skeleton,
  Tag,
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

import styles from './index.less';

const renderLink = (url?: string) => {
  if (!url) {
    return;
  }
  return (
    <a target="_blank" rel="noreferrer" href={url}>
      {url}
    </a>
  );
};

const allColors = [
  'magenta',
  'red',
  'volcano',
  'orange',
  'gold',
  'lime',
  'green',
  'cyan',
  'blue',
  'geekblue',
  'purple',
];

const renderTags = (tagText?: string) => {
  if (!tagText) {
    return;
  }
  return tagText.split(',').map((tag) => {
    const randomIndex = Math.floor(Math.random() * allColors.length);
    return (
      <Tag color={allColors[randomIndex]} key={tag}>
        {tag}
      </Tag>
    );
  });
};

const UserProfile: React.FC = () => {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<IUser>();

  const queryUserProfile = useCallback(() => {
    if (!params.id) {
      return;
    }
    setLoading(true);
    queryUserDetail(params.id)
      .then((res) => {
        setUser(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.id]);

  useEffect(() => {
    queryUserProfile();
  }, [params.id]);

  if (loading) {
    return <Skeleton loading />;
  }

  if (!user?.id) {
    return <Result status="error" title="用户不存在或已被删除" />;
  }

  return (
    <Card bordered={false}>
      <Descriptions
        title={<UserInfo {...user} />}
        layout="horizontal"
        column={1}
        styles={{
          label: { width: 120, textAlign: 'right', display: 'block' },
          content: { whiteSpace: 'pre-line' },
        }}
      >
        <Descriptions.Item label="用户">{user.nick_name}</Descriptions.Item>
        {/* <Descriptions.Item label="头像">
          <Image width="80px" src={user.avatar_url} />
        </Descriptions.Item> */}
        <Descriptions.Item label="性别">
          {USER_GENDER_MAPPING[user.gender]}
        </Descriptions.Item>
        <Descriptions.Item label="居住地">{user.location}</Descriptions.Item>
        <Descriptions.Item label="生日">{user.birthday}</Descriptions.Item>
        <Descriptions.Item label="爱好">{user.hobby}</Descriptions.Item>
        <Descriptions.Item label="个人介绍">{user.about_me}</Descriptions.Item>
        <Descriptions.Item label="个人图片">
          <Image.PreviewGroup>
            {user.about_me_files?.split(',').map((img) => (
              <Image
                rootClassName={styles.userFiles}
                key={img}
                width="100px"
                src={img}
              />
            ))}
          </Image.PreviewGroup>
        </Descriptions.Item>

        <Descriptions.Item>
          <Divider orientation="left">公司信息</Divider>
        </Descriptions.Item>

        <Descriptions.Item label="公司">{user.company}</Descriptions.Item>
        <Descriptions.Item label="职称">{user.job_title}</Descriptions.Item>
        <Descriptions.Item label="技能标签">
          {renderTags(user.skill_tags)}
        </Descriptions.Item>

        <Descriptions.Item>
          <Divider orientation="left">社交网站</Divider>
        </Descriptions.Item>
        <Descriptions.Item label="个人网站">
          {renderLink(user.websites)}
        </Descriptions.Item>
        <Descriptions.Item label="微信公众号">
          {user.mp_weixin_name}
        </Descriptions.Item>
        <Descriptions.Item label="Github">
          {renderLink(user.github_address)}
        </Descriptions.Item>
        <Descriptions.Item label="掘金">
          {renderLink(user.juejin_address)}
        </Descriptions.Item>
        <Descriptions.Item label="知乎">
          {renderLink(user.zhihu_address)}
        </Descriptions.Item>
        <Descriptions.Item label="思否">
          {renderLink(user.segmentfault_address)}
        </Descriptions.Item>

        <Descriptions.Item>
          <Divider orientation="left">内推信息</Divider>
        </Descriptions.Item>
        <Descriptions.Item label="提供内推">
          {user.internal_referral_state === EBooleanString.YES ? '是' : '否'}
        </Descriptions.Item>
        <Descriptions.Item label="内推公司">
          {renderTags(user.internal_referral_companies)}
        </Descriptions.Item>
        <Descriptions.Item label="内推介绍">
          {user.internal_referral_description}
        </Descriptions.Item>

        <Descriptions.Item>
          <Divider orientation="left">恋爱信息</Divider>
        </Descriptions.Item>
        <Descriptions.Item label="恋爱状况">
          {USER_LOVE_STATE_MAPPING[user.love_state]}
        </Descriptions.Item>
        <Descriptions.Item label="恋爱宣言">
          {user.love_expectation}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default UserProfile;
