import {
  ETrainingType,
  IPageFactory,
  IQueryTrainingParams,
  IRefreshTrainingUserScoreResult,
  ITraining,
  ITrainingAttendanceRate,
} from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';
import { IQueryMyTrainingParams } from './my';

/**
 * 查询训练营列表（所有的）
 */
export const queryAllTrainings = async (name?: string) => {
  return await request<ITraining[]>('/trainings/as-list', {
    params: { name },
  });
};

export const findUserTrainings = async (params: IQueryMyTrainingParams) => {
  return await request<IPageFactory<ITraining>>('/trainings/search-by-user', {
    params,
  });
};

/**
 * 查询训练营列表（分页）
 */
export const queryTrainings = async (params: IQueryTrainingParams) => {
  return await request<IPageFactory<ITraining>>('/trainings', {
    params,
  });
};

/**
 * 查询训练营详情
 */
export const queryTrainingDetail = async (id: string) => {
  return await request<ITraining>(`/trainings/${id}`);
};

/**
 * 查询某类训练营下一期序号
 */
export const queryTrainingNextPeriod = async (type: ETrainingType) => {
  return await request<{ next_period: number }>(`/trainings/next-periods`, {
    params: { type },
  });
};

/**
 * 查询训练营打卡率统计
 */
export const queryTrainingAttendanceRate = async (
  id: string,
  attendance_date?: string,
) => {
  return await request<ITrainingAttendanceRate>(
    `/analysis/trainings/${id}/attendances-rate`,
    {
      params: { attendance_date },
    },
  );
};

/**
 * 创建训练营
 */
export const createTraining = async (data: ITraining) => {
  return await request<ITraining>('/trainings', {
    method: 'POST',
    data: { ...data },
  });
};

/**
 * 编辑训练营
 */
export const updateTraining = async ({ id, ...rest }: ITraining) => {
  return await request<ITraining>(`/trainings/${id}`, {
    method: 'PUT',
    data: { ...rest },
  });
};

/**
 * 修改状态
 */
export const changeStatus = async (id: string, state: string) => {
  return await request<ITraining>(`/trainings/${id}/state`, {
    method: 'PUT',
    data: { state },
  });
};

/**
 * 刷新训练营下成员的积分
 */
export const updateTrainingAllUserScore = async (trainingId: string) => {
  return await request<IRefreshTrainingUserScoreResult>(
    `/trainings/${trainingId}/refresh-user-scores`,
    {
      method: 'POST',
    },
  );
};

/**
 * 刷新某个成员的积分
 */
export const updateUserScore = async (trainingUserId: string) => {
  return await request(`/training-users/${trainingUserId}/refresh-scores`, {
    method: 'POST',
  });
};

/**
 * 删除训练营
 */
export const delTraining = async (id: string) => {
  return await request(`/trainings/${id}`, {
    method: 'DELETE',
  });
};
