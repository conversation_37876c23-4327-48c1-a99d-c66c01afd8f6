<svg width="161" height="53" viewBox="0 0 161 53" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3 10C3 6.68629 5.68629 4 9 4H155C158.314 4 161 6.68629 161 10V47C161 50.3137 158.314 53 155 53H9C5.68629 53 3 50.3137 3 47V10Z" fill="url(#paint0_linear_710_2088)"/>
<g filter="url(#filter0_i_710_2088)">
<path d="M0 6C0 2.68629 2.68629 0 6 0H152C155.314 0 158 2.68629 158 6V43C158 46.3137 155.314 49 152 49H6C2.68629 49 0 46.3137 0 43V6Z" fill="url(#paint1_linear_710_2088)"/>
</g>
<g filter="url(#filter1_d_710_2088)">
<circle cx="11" cy="43" r="3" fill="#FFE7E7" fill-opacity="0.44" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_710_2088)">
<circle cx="147" cy="43" r="3" fill="#FFE7E7" fill-opacity="0.44" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_i_710_2088" x="0" y="0" width="158" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.85"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.704167 0 0 0 0 0.80475 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_710_2088"/>
</filter>
<filter id="filter1_d_710_2088" x="8" y="40" width="7.8" height="7.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0518601 0 0 0 0 0.353838 0 0 0 0 0.708333 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_710_2088"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_710_2088" result="shape"/>
</filter>
<filter id="filter2_d_710_2088" x="144" y="40" width="7.8" height="7.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0518601 0 0 0 0 0.353838 0 0 0 0 0.708333 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_710_2088"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_710_2088" result="shape"/>
</filter>
<linearGradient id="paint0_linear_710_2088" x1="-4.47297" y1="53" x2="199.966" y2="53" gradientUnits="userSpaceOnUse">
<stop stop-color="#37ADED"/>
<stop offset="1" stop-color="#52B8F0"/>
</linearGradient>
<linearGradient id="paint1_linear_710_2088" x1="8.54054" y1="1.1379e-05" x2="180.562" y2="55.2062" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DD5EF"/>
<stop offset="1" stop-color="#477EF3"/>
</linearGradient>
</defs>
</svg>
