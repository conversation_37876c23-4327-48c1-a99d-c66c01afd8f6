import { Image, View } from '@tarojs/components';
import { IPageProps } from '../../type';
import styles from './index.module.scss';
import HighlightWrapper from '../../components/HighlightWrapper';
import Quotation from '../../components/Quotation';

const quotationList = [
  ['合抱之木，生于毫末；', '九层之台，起于累土；', '千里之行，始于足下。'],
  ['在想要成就更好的路上', '你从未缺席', '也不曾放弃'],
  ['日日行，不怕千万里', '常常做，不怕千万事'],
  ['隐形翅膀上的根根毛羽', '都是你如常的点点累积'],
  ['璀璨的逐梦人从不曾忽视筑梦的基石', '每一步跃迁都是厚积薄发的昂扬'],
];

export default ({ reportData, isActive }: IPageProps) => {
  const { statistics } = reportData;

  return (
    <View className={`${styles.container} flex justify-center items-center`}>
      <View className="bottom-0 right-0 absolute">
        <Image
          className={`w-[268px] h-[304px] absolute z-2 bottom-200px right-100px ${styles.location}`}
          src={require('./assets/location_2.svg')}
        />
        <Image
          className="w-[710px] h-[434px] absolute z-1 bottom-40px right-[-110px]"
          src={require('./assets/location_1.svg')}
        />
      </View>

      <View className="report-2023__content absolute top-80px">
        <View>这一年</View>
        <View>你一共打卡了</View>
        <View>
          <HighlightWrapper
            active={isActive}
            data={statistics.attendance_words_count}
          />{' '}
          个文字，
          <HighlightWrapper
            active={isActive}
            data={statistics.attendance_image_count}
          />{' '}
          张图像
        </View>

        <View>生活的美好瞬间</View>
        <View>都被你的文字和图像所记录</View>

        <View className="report-2023__content--desc text-center mt-50rpx">
          <Quotation data={quotationList} />
        </View>
      </View>
    </View>
  );
};
