import { AppContext } from '@/appContext';
import { updateWechatProfile } from '@/service';
import { taroUpload } from '@/utils';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import { Button, ButtonProps, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { CSSProperties, useCallback, useContext, useState } from 'react';
import { AtAvatar } from 'taro-ui';
import { AtAvatarProps } from 'taro-ui/types/avatar';

interface IAvatarProps {
  /** 头像地址 */
  avatar: string;
  /** 人名 */
  userName?: string;
  /** 布局方式：水平、垂直 */
  layout?: /** 水平 */ 'horizontal' | /** 垂直 */ 'vertical';
  /** 头像大小 */
  size?: AtAvatarProps['size'];
  /** 是否显示 link 图标 */
  link?: boolean;
  /** 是否可以修改自己的头像 */
  editable?: boolean;
  onClick?: () => void;
  style?: CSSProperties;
}
const Avatar: React.FC<IAvatarProps> = ({
  avatar,
  userName,
  layout = 'vertical',
  size = 'normal',
  link = false,
  editable = false,
  onClick,
  style,
}) => {
  const { currentUser, refreshCurrentUser } = useContext(AppContext);
  // 新头像
  const [newAvatar, setNewAvatar] = useState<string>();

  const onChooseAvatar: ButtonProps['onChooseAvatar'] = useCallback(
    async (e) => {
      const { avatarUrl } = e.detail;

      const { path } = await taroUpload(avatarUrl);
      setNewAvatar(path);

      // 只能编辑自己的微信信息，所以这里可以放心的取当前登录人的名称
      updateWechatProfile({
        avatar_url: path,
        nick_name: userName || currentUser.nick_name,
      })
        .then(() => {
          refreshCurrentUser?.();
        })
        .catch(() => {
          Taro.showToast({
            title: '出错了，请联系开发人员处理',
            icon: 'error',
            duration: 1000,
          });
        });
    },
    [userName],
  );

  return (
    <View
      className={`flex items-center justify-center gap-1 ${
        layout === 'vertical' ? 'flex-col mb-5 mt-3' : 'flex-row'
      }`}
      style={style}
      onClick={onClick}
    >
      <View className="relative h-full">
        {/*  */}
        {editable && (
          <View
            className="absolute left-half top-half w-full h-full"
            style={{ transform: 'translate(-50%,-50%)' }}
          >
            <Button
              open-type="chooseAvatar"
              className="bg-neutral-500 opacity-40 border-none rounded-none h-full"
              onChooseAvatar={onChooseAvatar}
            />
            <View
              className="at-icon at-icon-camera text-light-800 text-2xl absolute left-half top-half"
              style={{
                transform: 'translate(-50%,-50%)',
                color: '#fff',
                pointerEvents: 'none',
              }}
            />
          </View>
        )}
        <AtAvatar size={size} image={getFileUrl(newAvatar || avatar)} />
      </View>
      {userName && (
        <View
          className={`${layout === 'vertical' ? 'm1' : ''}`}
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '70%',
          }}
        >
          {userName}
        </View>
      )}
      {link && userName && (
        <View
          className="at-icon at-icon-chevron-right"
          style={{ fontSize: 14, color: '#999' }}
        />
      )}
    </View>
  );
};

export default Avatar;
