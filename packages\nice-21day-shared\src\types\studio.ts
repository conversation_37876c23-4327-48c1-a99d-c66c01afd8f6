import { ICommonFields, IPageParams } from './global';
import { IUser } from './user';

/** 声音创作分类 */
export enum EStudioTag {
  /** 21天综合训练营 */
  '21Day' = '21day',
  /** 英语 */
  English = 'english',
  /** 运动 */
  Sports = 'sports',
  /** 早安 */
  GoodMorning = 'good_morning',
  /** 留言祝福 */
  Blessing = 'blessing',
  /** 不便分类 */
  Other = 'other',
}

/** 声音创作标签映射关系 */
export const STUDIO_TAG_MAPPING: Record<EStudioTag, string> = {
  [EStudioTag['21Day']]: '21天训练营',
  [EStudioTag.English]: '英语打卡',
  [EStudioTag.Sports]: '运动打卡',
  [EStudioTag.GoodMorning]: '早安',
  [EStudioTag.Blessing]: '留言',
  [EStudioTag.Other]: '不便分类',
};

/** 声音创作公开范围 */
export enum EStudioPublicScope {
  /** 公开 */
  'Public' = 'public',
  /** 私密 */
  Private = 'private',
}

/** 声音创作公开范围映射关系 */
export const STUDIO_PUBLIC_SCOPE_MAPPING: Record<EStudioPublicScope, string> = {
  [EStudioPublicScope.Public]: '全部可见',
  [EStudioPublicScope.Private]: '仅自己可见',
};

interface IStudioData {
  /** 分类 */
  tags: string;
  /** 公开范围 */
  public_scope: EStudioPublicScope;
  /** 创作的内容 */
  content: string;
  /**
   * 音频文件列表
   * @description 目前只支持1个音频，以后如果支持多个，可以传 csv 格式
   */
  files: string;
  /** 创作人ID */
  creator_id: string;
}

/**
 * 声音创作
 */
export interface IStudio extends ICommonFields, IStudioData {
  /** 创作人用户信息 */
  user?: IUser;
}

/**
 * 查询声音创作的查询参数
 */
export interface IQueryStudioParams extends IPageParams {
  /** 创作人ID */
  creator_id?: string;
  /** 分类 */
  type?: string;
}

/**
 * 随机推荐声音创作的查询参数
 */
export interface IRecommendStudioParams
  extends Partial<Pick<IStudio, 'public_scope' | 'tags'>> {
  /**
   * 推荐条数
   * @default 1
   */
  limit?: number;
  /**
   * 排除用户ID
   * @default 默认为当前登录用户
   */
  exclude_user_id?: string;
}

/**
 * 新建声音创作的 Body 内容
 */
export interface ICreateStudioFormData
  extends Omit<IStudio, 'id' | 'creator_id'> {}

/**
 * 编辑声音创作的 Body 内容
 */
export interface IUpdateStudioFormData extends Omit<IStudio, 'creator_id'> {}
