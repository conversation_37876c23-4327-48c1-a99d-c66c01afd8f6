import React, { CSSProperties, FC } from 'react';
import { View } from '@tarojs/components';
import styles from './index.module.scss';

export interface IParagraphProps {
  style?: CSSProperties;
  className?: string;
}
const Paragraph: FC<IParagraphProps> & {
  ParagraphGroup: typeof ParagraphGroup;
} = ({ style, className, children }) => {
  return (
    <View style={style} className={`${className} ${styles.paragraph}`}>
      {children}
    </View>
  );
};

const ParagraphGroup: FC<IParagraphProps> = ({
  style,
  children,
  className,
}) => {
  return (
    <View className={className} style={style}>
      {children
        ? React.Children.map(children, (child, index) =>
            React.cloneElement(child as React.ReactElement, {
              style: {
                animationDelay: `${(index + 1) * 300}ms`,
              },
            }),
          )
        : null}
    </View>
  );
};

Paragraph.ParagraphGroup = ParagraphGroup;

export default Paragraph;
