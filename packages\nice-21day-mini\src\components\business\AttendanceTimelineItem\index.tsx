import { FilePreview } from '@/components';
import {
  computedAttendanceLogScore,
  EAttendanceState,
  extractText,
  getFileUrl,
  IAttendanceLog,
  IAttendanceTask,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { Swiper, SwiperItem, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { AtAvatar } from 'taro-ui';
import './index.scss';

const getAttendanceStateText = (attendanceLog: IAttendanceLog) => {
  if (attendanceLog.attendance_state === EAttendanceState.Leave) {
    return '请假';
  }
  // 提交日期和打卡日期不一致
  if (
    dayjs(attendanceLog.created_at).format('MM-DD') !==
    dayjs(attendanceLog.attendance_date).format('MM-DD')
  ) {
    return '补卡';
  }
  return '打卡';
};

interface IAttendanceTimelineItemProps {
  training_id: string;
  attendance: IAttendanceLog;
  user_id?: string;
  marginSize?: 'default' | 'small';
}

const AttendanceTimelineItem = ({
  training_id,
  attendance,
  user_id,
  marginSize = 'default',
}: IAttendanceTimelineItemProps) => {
  const attendanceTasks = parseArrayJson<IAttendanceTask>(
    attendance.attendance_tasks,
  ).filter(
    (task) => task.attendance_content || task.attendance_files?.length > 0,
  );

  return (
    <View
      key={attendance.id}
      className={`timeline-wrapper status-${attendance.attendance_state} ${
        marginSize === 'small' ? 'timeline-wrapper__small' : ''
      }`}
    >
      <View>
        {/* 这里是打卡内容 */}
        <View
          className="timeline-box"
          onClick={() => {
            Taro.navigateTo({
              url: `/pages/attendance-detail/index?training_id=${training_id}&attendance_id=${attendance.id}`,
            });
          }}
        >
          <View className="timeline-header">
            <View
              className="flex gap-2 justify-center items-center"
              onClick={(e) => {
                e.preventDefault();
                // 没有用户id表示是训练营全部打卡列表
                // 这时候可以查看某个人的打卡列表
                if (!user_id) {
                  Taro.navigateTo({
                    url: `/pages/attendance-timeline-user/index?training_id=${training_id}&user_id=${attendance.user_id}`,
                  });
                } else {
                  Taro.navigateTo({
                    url: `/pages/analytics/user-attendance-analysis/index?training_id=${training_id}&user_id=${attendance.user_id}`,
                  });
                }
              }}
            >
              <View>
                {/* 左边的头像 */}
                <AtAvatar
                  size="small"
                  image={getFileUrl(attendance.user?.avatar_url)}
                />
              </View>
              <View>
                <View className="user-name">{attendance.user?.nick_name}</View>
                <View style={{ fontSize: 12 }} className="text-gray-500">
                  积分 +{computedAttendanceLogScore(attendance)}
                </View>
              </View>
            </View>
            {/* 查看详情可点击的标识 */}
            <View className="at-icon at-icon-chevron-right"></View>
          </View>
          <View className="timeline-content">
            {/* 打卡 */}
            {attendance.attendance_state === EAttendanceState.Attendance && (
              <Swiper
                indicatorColor="#999"
                indicatorActiveColor="#333"
                // 是否纵向
                vertical={false}
                // 循环播放
                circular
                // 是否显示面板指示点
                indicatorDots={attendanceTasks.length > 1}
                onChange={(e) => {
                  e.stopPropagation();
                }}
              >
                {attendanceTasks.map((task) => (
                  <SwiperItem key={task.id}>
                    {/* 打卡内容 */}
                    <View className="attendance-wrapper">
                      {/* 任务名称 */}
                      <View className="attendance-header line-clamp-1">
                        任务: {task.name}
                      </View>
                      {/* 打卡内容 */}
                      {task.attendance_content && (
                        <View
                          className={`attendance-content ${
                            (task.attendance_files || []).length === 0
                              ? 'no-image'
                              : ''
                          }`}
                        >
                          <Text className="text">
                            {extractText(task.attendance_content)}
                          </Text>
                        </View>
                      )}
                      {/* 打卡的文件 */}
                      <View className="attendance-footer">
                        <FilePreview
                          files={(task.attendance_files || [])
                            .filter((file) => file)
                            .map((file) => getFileUrl(file))}
                        />
                      </View>
                    </View>
                  </SwiperItem>
                ))}
              </Swiper>
            )}
            {/* 请假 */}
            {attendance.attendance_state === EAttendanceState.Leave && (
              <View className="attendance-content">
                <Text className="text">{attendance.description}</Text>
              </View>
            )}
          </View>
        </View>
        <View className="timeline-footer">
          <View className="flex justify-between items-center">
            <View>
              {getAttendanceStateText(attendance)} {attendance.attendance_date}
            </View>
            <View style={{ fontSize: 12 }}>
              积分 +{computedAttendanceLogScore(attendance)}
            </View>
          </View>
          <View className="flex justify-between items-center">
            <View>
              提交于 {dayjs(attendance.created_at).fromNow()}
              {attendance.created_at !== attendance.updated_at && (
                <> 修改于 {dayjs(attendance.updated_at).fromNow()}</>
              )}
            </View>
            <View className="flex gap-2">
              <View>
                <view
                  className="at-icon at-icon-heart-2"
                  style={{ color: 'var(--like-color)' }}
                />{' '}
                {attendance.like_count}
              </View>
              <View>
                <view className="at-icon at-icon-message" />{' '}
                {attendance.comment_count}
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default AttendanceTimelineItem;
