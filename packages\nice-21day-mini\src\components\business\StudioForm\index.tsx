import { AudioPlayer, FabBox, <PERSON><PERSON>ield, Recorder, Title } from '@/components';
import { createStudio, updateStudio } from '@/service';
import {
  EStudioPublicScope,
  EStudioTag,
  ICreateStudioFormData,
  IStudio,
  STUDIO_PUBLIC_SCOPE_MAPPING,
  STUDIO_TAG_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Picker, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useState } from 'react';
import { AtButton, AtListItem, AtNoticebar, AtTag, AtTextarea } from 'taro-ui';

interface IStudioFormProps {
  studio?: IStudio;
}
const StudioForm: React.FC<IStudioFormProps> = ({ studio }) => {
  const [isRecording, setIsRecording] = useState<boolean>(false);

  const [studioFormData, setStudioFormData] = useState<ICreateStudioFormData>({
    tags: studio?.tags || EStudioTag['21Day'],
    public_scope: studio?.public_scope || EStudioPublicScope.Public,
    content: studio?.content || '',
    files: studio?.files || '',
  });

  const saveStudio = useCallback(() => {
    (studio?.id
      ? updateStudio(studio.id, studioFormData)
      : createStudio(studioFormData)
    )
      .then(() => {
        Taro.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000,
        });
        Taro.navigateBack();
      })
      .catch(() => {
        Taro.showToast({
          title: '保存失败',
          icon: 'error',
          duration: 1000,
        });
      })
      .finally(() => {});
  }, [studioFormData, studio]);

  const handleFinish = useCallback(() => {
    const { files, content } = studioFormData;
    if (!files) {
      Taro.showModal({
        title: '保存失败',
        content: '请录制声音',
        showCancel: false,
        confirmText: '我知道了',
      });
      return;
    }

    if (!content) {
      Taro.showModal({
        title: '提示',
        content: '录制的文本内容为空，确定继续保存吗？',
        cancelText: '返回修改',
        confirmText: '继续保存',
        success: function (res) {
          if (res.confirm) {
            saveStudio();
          }
        },
      });
      return;
    }

    Taro.showModal({
      title: '确定保存吗？',
      success: function (res) {
        if (res.confirm) {
          saveStudio();
        }
      },
    });
  }, [saveStudio, studioFormData]);

  const handleCancel = useCallback(() => {
    Taro.navigateBack();
  }, []);

  const updateTags = useCallback(
    (tag: string) => {
      const tagsSplit = studioFormData.tags.split(',').filter((el) => el);
      if (tagsSplit.includes(tag)) {
        setStudioFormData((prevData) => ({
          ...prevData,
          tags: tagsSplit.filter((el) => el !== tag).join(','),
        }));
      } else {
        setStudioFormData((prevData) => ({
          ...prevData,
          tags: [...tagsSplit, tag].join(','),
        }));
      }
    },
    [studioFormData.tags],
  );

  return (
    <View className="studio-create-page p-2.5 min-h-100vh pb-280px bg-light-50">
      <AtNoticebar
        icon="volume-plus"
        className="mt-1"
        single
        showMore
        moreText="查看详情"
        onGotoMore={() => {
          Taro.showModal({
            title: '声音创作设置说明',
            content:
              '标签说明：\r\n录制时选择不同的标签，你录制的音频将会出现在对应的群里；\r\n如果选择留言标签，这个音频就相当于你对社群的吐槽或者祝福了。\r\n\r\n可见范围说明：\r\n选择全部可见，该音频可以在小程序里看到；选择仅自己可见，该音频只会随机由大脸发出。\r\n\r\n大家赶紧来录制音频，留下你对其他同学的祝福和鼓励吧！\r\n\r\nO(∩_∩)O',
            showCancel: false,
            confirmText: '我知道了',
          });
        }}
      >
        声音创作设置说明
      </AtNoticebar>

      <Title title="声音设置" />
      <FormField label="录制的文本内容">
        <AtTextarea
          value={studioFormData.content}
          onChange={(val) =>
            setStudioFormData((prevData) => ({
              ...prevData,
              content: val || '',
            }))
          }
          maxLength={500}
          height={400}
          className="whitespace-pre-line"
          placeholder={`填写录制的文本内容，例如：

        顾城《门前》

        草在结它的种子
        风在摇它的叶子
        我们站着 不说话
        就十分美好
        `}
        />
      </FormField>

      <FormField label="标签">
        <View className="flex flex-wrap gap-2">
          {Object.keys(STUDIO_TAG_MAPPING).map((tag: EStudioTag) => (
            <AtTag
              key={tag}
              type="primary"
              circle
              active={studioFormData.tags.split(',').includes(tag)}
              onClick={() => updateTags(tag)}
            >
              {STUDIO_TAG_MAPPING[tag]}
            </AtTag>
          ))}
        </View>
      </FormField>

      <View className="pt-3">
        <Picker
          mode="selector"
          range={Object.values(STUDIO_PUBLIC_SCOPE_MAPPING)}
          value={Object.values(STUDIO_PUBLIC_SCOPE_MAPPING).findIndex(
            (state) => state === studioFormData.public_scope,
          )}
          onChange={(e) => {
            const index = e.detail.value as number;
            setStudioFormData((prevData) => ({
              ...prevData,
              public_scope: Object.keys(STUDIO_PUBLIC_SCOPE_MAPPING)[
                index
              ] as any,
            }));
          }}
        >
          <AtListItem
            hasBorder={false}
            title="可见范围"
            extraText={STUDIO_PUBLIC_SCOPE_MAPPING[studioFormData.public_scope]}
          />
        </Picker>
      </View>

      {studio?.files && (
        <>
          <Title title="已有声音 (重新录音后将覆盖原有声音)" />
          <AudioPlayer src={studio.files} />
        </>
      )}

      <FabBox>
        <View
          className="p-2 !pt-4"
          style={{ background: '#fff', boxShadow: '0px -4px 5px 2px #f5f5f5' }}
        >
          <Recorder
            confirmText="上传"
            onFinish={(path) =>
              setStudioFormData((prevData) => ({ ...prevData, files: path }))
            }
            onRecordingChange={setIsRecording}
            onCancel={handleCancel}
          />
        </View>
        <AtButton
          type="primary"
          className={`w-full ${
            isRecording || !studioFormData.files ? 'disabled' : ''
          }`}
          circle
          onClick={handleFinish}
        >
          <View className="flex items-center gap-1">
            <View className="at-icon at-icon-upload" />
            保存内容
          </View>
        </AtButton>
      </FabBox>
    </View>
  );
};

export default StudioForm;
