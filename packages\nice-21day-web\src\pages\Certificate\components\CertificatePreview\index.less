.template {
  @text-color: #606060;

  width: 450px;
  margin: 0 auto;
  position: relative;

  img {
    width: 100%;
  }

  // 标题
  .training-title {
    position: absolute;
    top: 176px;
    width: 100%;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: @text-color;
  }

  // 结果
  .user {
    position: absolute;
    width: 100%;
    top: 353px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    &__avatar {
      border-radius: 50%;
      overflow: hidden;
      width: 120px;
      height: 120px;

      .avator {
        width: 100%;
        height: 100%;
      }

      .part2 {
        position: absolute;
        left: 0;
        top: 80px;
        transform: translateY(-50%);
      }
    }

    &__name {
      font-size: 22px;
      color: @text-color;
      font-weight: bold;
      margin-top: 4px;
    }
  }

  .result {
    position: absolute;
    display: flex;
    height: 90px;
    bottom: 84px;
    width: 301px;
    margin: 0 auto;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    left: 49%;
    transform: translate(-50%, 0);
    border-radius: 4px;

    .resultmetric {
      position: relative;

      .resultvalue {
        width: 65px;
        line-height: 65px;
        height: 65px;
        border-radius: 50%;
        margin: 0 auto;
        background-color: #eee5b5;
        color: #fff;
        font-weight: bold;
        font-size: 18px;
      }
    }

    .part3 {
      position: absolute;
      top: 0;
      left: 0;
      width: 270px;
      transform: translateX(-42%) translateY(-50%);
    }

    .part4 {
      position: absolute;
      top: 0;
      left: 0;
      width: 270px;
      transform: translateX(-42%) translateY(-16%);
    }

    .part5 {
      position: absolute;
      top: 0;
      left: 0;
      width: 270px;
      transform: translateX(-33%) translateY(-55%);
    }
  }
}
