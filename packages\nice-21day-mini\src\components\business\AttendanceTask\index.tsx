import { Card, FilePreview, RecordAudio } from '@/components';
import { getFileUrl, taroUpload } from '@/utils/index';
import {
  filterFiles,
  IAttendanceTask,
  isWeeklyTask,
} from '@nice-people/nice-21day-shared';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useMemo } from 'react';
import { AtImagePicker, AtTag, AtTextarea } from 'taro-ui';
import { File } from 'taro-ui/types/image-picker';

import './index.scss';

interface IAttendanceItemProp {
  task: IAttendanceTask;
  /** 是否只读——用于打卡详情纯展示 */
  readonly?: boolean;
  /** 任务序号 */
  taskIndex: number;
  // 子组件更新父组件的方法
  onChange?: (task: IAttendanceTask) => void;
}

const AttendanceTask: React.FC<IAttendanceItemProp> = ({
  task,
  taskIndex,
  readonly = false,
  onChange,
}) => {
  const handleFormChange = (value: string, key: string) => {
    onChange({
      ...task,
      [key]: value,
    });
  };

  // 打卡内容通过
  const attendaceChecked = useMemo(() => {
    return task.attendance_content || task.attendance_files.length > 0;
  }, [task]);

  /** 所有的文件：图片+音频 */
  const fileList = useMemo(() => {
    return task?.attendance_files || [];
  }, [task?.attendance_files]);

  /** 格式化后的图片列表 */
  const { imageList, audioList } = useMemo(() => {
    return filterFiles(fileList);
  }, [fileList]);

  // 是否展示上传按钮
  const isShowAdd = useMemo(() => {
    return imageList.length < 3;
  }, [imageList]);

  /**
   * 处理选择图表单
   * @param files 文件列表
   */
  const handleImagePickerChange = useCallback(
    (files: File[], type: 'add' | 'remove', index?: number) => {
      console.log('type: ', type);
      const len = files.length;
      if (type === 'add') {
        // TODO: 格式限制
        handleUploadImage(files[len - 1].url);
      } else {
        // 查询找这个图片
        const delImage = imageList[index];

        onChange({
          ...task,
          attendance_files: [...fileList.filter((path) => path !== delImage)],
        });
      }
    },
    [fileList, imageList, task],
  );

  const handleUploadFail = (message: string) => {
    Taro.showModal({
      title: '上传失败',
      content: message,
      showCancel: false,
      confirmText: '我知道了',
    });
  };

  /**
   * 上传获取到的图片
   * @param imgUrl
   */
  const handleUploadImage = useCallback(
    async (tempFilePath: string) => {
      taroUpload(tempFilePath).then((file) => {
        onChange({
          ...task,
          attendance_files: [...(task?.attendance_files || []), file.path],
        });
      });
    },
    [task],
  );

  /**
   * 上传音频成功后的回调
   */
  const handleUploadAudioSuccess = useCallback(
    (audioPath: string) => {
      onChange({
        ...task,
        attendance_files: [...(task?.attendance_files || []), audioPath],
      });
    },
    [task],
  );

  const handleRemoveAudio = useCallback(
    (audioPath: string) => {
      onChange({
        ...task,
        attendance_files: [
          ...(task?.attendance_files || []).filter(
            (path) => path !== audioPath,
          ),
        ],
      });
    },
    [task],
  );

  return (
    <View className="attendance-task-view">
      <Card
        key={task.id || task.name}
        title={`任务${taskIndex + 1}: ${task.name}`}
      >
        {/* 打卡内容 */}
        {!readonly && (
          <>
            <AtTextarea
              value={task.attendance_content}
              onChange={(value, event) => {
                handleFormChange(value as string, 'attendance_content');
              }}
              height={300}
              maxLength={2000}
              disabled={readonly}
              placeholder="请输入打卡内容"
            />
            <AtImagePicker
              className="upload-wrapper"
              showAddBtn={isShowAdd}
              count={1}
              length={3}
              multiple={false}
              files={imageList.map((img) => ({ url: getFileUrl(img) }))}
              onChange={(files, type, index) => {
                handleImagePickerChange(files, type, index);
              }}
              onFail={(message) => {
                handleUploadFail(message);
              }}
              onImageClick={(index, file) => {
                console.log(index);
                console.log(file);
              }}
            />
            <RecordAudio
              files={audioList}
              onSuccess={handleUploadAudioSuccess}
              onRemove={handleRemoveAudio}
            />
          </>
        )}
        {/* 只读预览 */}
        {readonly && (
          <>
            <View className="break-all whitespace-pre-line">
              <Text user-select>{task.attendance_content}</Text>
            </View>
            <View className="mt-5">
              <FilePreview
                files={(task.attendance_files || [])
                  .filter((file) => file)
                  .map((file) => getFileUrl(file))}
              />
              <View className="flex justify-between mt-1">
                <View>
                  {/* 周任务 */}
                  {isWeeklyTask(task?.name) && (
                    <AtTag size="small" active type="primary">
                      周任务
                    </AtTag>
                  )}
                </View>
              </View>
            </View>
          </>
        )}
      </Card>
    </View>
  );
};

export default AttendanceTask;
