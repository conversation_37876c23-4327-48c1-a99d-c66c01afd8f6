import { ICurrentUser } from '@nice-people/nice-21day-shared';
import { useModel } from '@umijs/max';
import { useMemo } from 'react';

export const useCurrentUser = () => {
  const { initialState, refresh } = useModel('@@initialState');

  const currentUser = useMemo(() => {
    return initialState?.currentUser || ({} as ICurrentUser);
  }, [initialState]);

  // 是否已登录
  const isLogin = useMemo(() => {
    return Boolean(initialState?.currentUser?.id);
  }, [initialState?.currentUser]);

  return { currentUser, isLogin, refresh };
};
