import { getUserProfile } from '@/service';
import { IUser } from '@nice-people/nice-21day-shared';
import { useCallback, useEffect, useState } from 'react';

/**
 * 获取用户的详情
 * @param userId 用户ID
 * @returns
 */
export const useUserProfile = (userId: string) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [userProfile, setUserProfile] = useState<IUser>();

  const fetchUserProfile = useCallback(
    (showLoading = true) => {
      if (showLoading) {
        setLoading(true);
      }
      getUserProfile(userId)
        .then(({ data }) => {
          setUserProfile(data || ({} as IUser));
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [userId],
  );

  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  return {
    userProfile,
    setUserProfile,
    fetchUserProfile,
    loading,
  };
};
