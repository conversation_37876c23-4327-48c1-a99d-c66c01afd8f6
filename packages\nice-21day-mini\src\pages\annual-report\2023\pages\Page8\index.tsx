import { getFileUrl } from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import styles from './index.module.scss';
import { IPageProps } from '../../type';
import HighlightWrapper from '../../components/HighlightWrapper';

export default ({ reportData, isActive }: IPageProps) => {
  const { statistics } = reportData;
  return (
    <View className={`${styles.container} flex justify-center items-center`}>
      {/* <View className="absolute bottom-[-130px] right-[-100px]">
        <Image className={styles.love} src={require('./assets/love.svg')} />
      </View> */}
      <View className="absolute bottom-0px right-0px">
        <Image
          className="w-440px h-360px"
          src={require('./assets/circle.svg')}
        />
      </View>

      <View className="report-2023__content absolute top-80px">
        <View>这一年</View>
        <View>
          你关注了{' '}
          <HighlightWrapper active={isActive} data={statistics.fans_count} />{' '}
          位良师益友
        </View>
        <View>
          送出了{' '}
          <HighlightWrapper
            active={isActive}
            data={statistics.likes_sent_count}
          />{' '}
          个点赞
        </View>

        <View>这一年</View>
        {/* <View>你的坚持和努力也得到了大家的认可</View> */}
        <View>
          你收获了{' '}
          <HighlightWrapper active={isActive} data={statistics.fans_count} />{' '}
          个粉丝、
          <HighlightWrapper
            active={isActive}
            data={statistics.likes_received_count}
          />{' '}
          个点赞
        </View>
        {statistics.likes_received_topn_user_ids.length > 0 && (
          <>
            <View className="mt-20px">
              给你点赞最多的前{' '}
              <HighlightWrapper
                active={isActive}
                data={statistics.likes_received_topn_user_ids.length}
              />{' '}
              名同学是
            </View>
            <View className="flex justify-around mt-70px">
              {statistics.likes_received_topn_user_list.map((user) => (
                <View
                  key={user.id}
                  className="w-120px h-120px rounded-full overflow-hidden"
                >
                  <Image
                    className="h-full w-full"
                    src={getFileUrl(user.avatar_url)}
                  />
                </View>
              ))}
            </View>
          </>
        )}

        <View className="report-2023__content--desc mt-80px text-center">
          <View>给别人鼓掌，也是为自己加油！</View>
        </View>
      </View>
    </View>
  );
};
