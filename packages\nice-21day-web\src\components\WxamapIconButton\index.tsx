import { ReactComponent as WxappLogoSVG } from '@/assets/svg/wxapp-logo.svg';
import Icon from '@ant-design/icons';
import { Button } from 'antd';

const WxamapIconButton = () => {
  return (
    <Button
      size="large"
      type="text"
      icon={
        <Icon
          className="flex justify-center items-center"
          style={{
            fontSize: 32,
          }}
          component={WxappLogoSVG}
        />
      }
      shape="circle"
    />
  );
};

export default WxamapIconButton;
