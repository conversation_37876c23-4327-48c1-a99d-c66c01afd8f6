import { defineConfig } from '@umijs/max';
import routes from './src/routes';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {},
  mfsu: false,
  routes,
  tailwindcss: {},
  title: '21天训练营',
  favicons: ['/favicon.png'],
  // base: process.env.NODE_ENV === 'production' ? '/nice-21day-web/' : '/',
  // publicPath: process.env.NODE_ENV === 'production' ? '/nice-21day-web/' : '/',
  npmClient: 'pnpm',
  // @see: https://umijs.org/docs/api/config#esbuildminifyiife
  esbuildMinifyIIFE: true,
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  analytics: {
    baidu: '33a777cf0524b715d9fa6c5e0455caed',
  },
  proxy: {
    '/api/v1/upload': {
      target: 'https://nice.yayujs.com',
      changeOrigin: true,
    },
    '/api': {
      target: 'http://127.0.0.1:7001',
      // target: 'https://nice.yayujs.com',
      changeOrigin: true,
      // pathRewrite: { '^/api': '' },
    },
    '/public': {
      target: 'https://nice.yayujs.com',
      changeOrigin: true,
      // pathRewrite: { '^/api': '' },
    },
  },
});
