import request from '@/utils/request';
import { ICurrentUser, ISystemSetting } from '@nice-people/nice-21day-shared';

/**
 * 获取当前登录人
 */
export const getCurrentUser = async () => {
  return await request.get<ICurrentUser>(
    '/auth/current-users',
    {
      v: +new Date(),
    },
    {
      hideError: true,
    },
  );
};

export const logout = async () => {
  return await request.post('/auth/logout', {});
};

/**
 * 获取系统配置
 */
export const getSystemSettings = async () => {
  return await request.get<ISystemSetting[]>('/system/settings', {
    v: +new Date(),
  });
};
