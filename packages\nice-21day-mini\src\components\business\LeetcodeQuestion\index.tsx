import { ILeetcodeQuesiton } from '@/pages/leetcode-weekly/typings';
import React, { useMemo } from 'react';
import { AtTag } from 'taro-ui';
import styles from './index.module.scss';

interface ILeetcodeQuestionProps {
  questionId: string;
  question?: ILeetcodeQuesiton;
}
const LeetcodeQuestion: React.FC<ILeetcodeQuestionProps> = ({
  questionId,
  question,
}) => {
  const levelClassNames = useMemo(() => {
    if (!question) {
      return;
    }
    if (question?.level === 1) {
      return styles.easy;
    }
    if (question?.level === 2) {
      return styles.medium;
    }
    return styles.hard;
  }, [question]);

  if (!question) {
    return (
      <AtTag size="small" circle>
        {questionId}
      </AtTag>
    );
  }

  return (
    <AtTag size="small" circle className={`${styles.tag} ${levelClassNames}`}>
      {question.frontend_question_id}
    </AtTag>
  );
};

export default LeetcodeQuestion;
