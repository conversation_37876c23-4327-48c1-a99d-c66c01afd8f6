import { read, utils } from 'xlsx';

// TODO: 完全看不懂写的什么，也不知道容错性怎么样
// 全靠参考 @see https://github.com/SheetJS/sheetjs/issues/2674

/**
 * 读取 excel 文件
 * @param file excel 文件
 * @returns json 解析后的 JSON 数组
 */
export const readSheetFileJson = async <T>(file: ArrayBuffer) => {
  const workbook = read(file, {});
  const exname = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[exname];

  const result: T[] = [];
  const HEADERS = 3; // change this to the number of rows of headers
  const aoa = utils.sheet_to_json<any>(worksheet, {
    blankrows: false,
    header: 1,
  });

  const merges = worksheet['!merges'].map((m) =>
    typeof m === 'string' ? utils.decode_range(m) : m,
  );

  // create header names by joining the field names -- you can change key names here
  let R = 0,
    headers = [],
    range = utils.decode_range(worksheet['!ref']);
  for (let C = 0; C <= range.e.c; ++C) {
    headers[C] = '';
    for (R = 0; R < HEADERS; ++R) {
      const m = merges.find(
        // eslint-disable-next-line @typescript-eslint/no-loop-func
        (m) => m.s.c <= C && C <= m.e.c && m.s.r <= R && R <= m.e.r,
      );
      if (m) {
        if (aoa[m.s.r][m.s.c] !== null) {
          headers[C] += ' ' + aoa[m.s.r][m.s.c];
          R = m.e.r;
        }
      } else {
        if (aoa[R][C] !== null) headers[C] += ' ' + aoa[R][C];
      }
    }
    if (headers[C] === '') headers[C] = '__EMPTY_' + C;
    else headers[C] = headers[C].slice(1);
  }

  // CHANGE THE HEADERS ARRAY HERE

  // process the rest of the rows
  for (R = HEADERS; R < aoa.length; ++R) {
    const row: any[] = [];

    // pull values from topleft cell of merges
    for (let C = 0; C <= range.e.c; ++C) {
      // eslint-disable-next-line @typescript-eslint/no-loop-func
      let m = merges.find((m) => {
        return m.s.c <= C && C <= m.e.c && m.s.r <= R && R <= m.e.r;
      });
      if (m && aoa[m.s.r][m.s.c] !== null) row[C] = aoa[m.s.r][m.s.c];
      else if (!m && aoa[R][C] !== null) row[C] = aoa[R][C];
    }

    // construct object and push
    result.push(
      // @ts-ignore
      Object.fromEntries(
        headers.filter((h, i) => row[i] !== null).map((h, i) => [h, row[i]]),
      ),
    );
  }

  return result;
};
