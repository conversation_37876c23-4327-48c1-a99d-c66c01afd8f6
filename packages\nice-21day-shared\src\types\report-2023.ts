import { ICommonFields } from './global';
import { ITraining } from './training';
import { ITrainingMember, ITrainingTask } from './training-member';
import { IUser } from './user';

export interface IUserReportStatistics {
  /** 用户注册日期 */
  user_registration_date: string;
  /** 总打卡数 */
  attendance_total_count: number;
  /** 打卡率 */
  attendance_rate: number;
  /** 打卡次数最多的时间段 */
  attendance_preferred_time: string;
  /** 某个时间段打卡最多的次数 */
  attendance_count_at_preferred_time: number;
  /** 打卡时间分布 */
  addendance_time_distribution: { hours: string; count: number }[];
  /** 打卡最晚的时间 */
  // attendance_last_time: string;
  /** 打卡文字数量 */
  attendance_words_count: number;
  /** 打卡图片数量 */
  attendance_image_count: number;
  /** 打卡音频数量 */
  attendance_audio_count: number;
  /** 收获的粉丝数 */
  fans_count: number;
  /** 关注的人数 */
  follow_count: number;
  /** 收获的点赞数 */
  likes_received_count: number;
  /** 给我点赞次数 TopN 的用户IDs */
  likes_received_topn_user_ids: string[];
  /** 给我点赞次数 TopN 的用户信息 */
  likes_received_topn_user_list?: IUser[];
  /** 送出的点赞数 */
  likes_sent_count: number;
  /** 参加的训练营IDs */
  training_joined_ids: string[];
  /** 参加的训练营数量 */
  training_joined_count: number;
  /** 参加的训练营列表 */
  training_joined_list?: ITraining[];
  /** 是否参加了全部的训练营 */
  is_joined_all_training: boolean;
  /** 训练营的报名信息 */
  training_join_info_list?: ITrainingMember[];
  /** 达标的训练营数量 */
  training_standard_count: number;
  /** 训练营达标率 */
  training_standard_rete: number;
  /** 制定的任务总数量 */
  task_total_count: number;
  /** 制定的任务列表 */
  task_list: ITrainingTask[];
  /** 重复率最高的任务名称 */
  task_highest_repeated: string;
  /** 训练营个人感悟总字数 */
  training_summary_words_total_count: number;
  /** 训练营个人感悟高频关键词 */
  training_summary_words_keywords: string[];
  category_analysis: {
    category: '早起' | '冥想' | '运动' | '阅读' | '写作' | '个人成长';
    score: number;
  }[];
}

/** 全局维度的统计 */
export interface IGlobalReportStatistics {
  /** 2023年度训练营列表 */
  training_2023_list: ITraining[];
  /** 2023年度训练营的数量 */
  training_2023_count: number;
  /** 累计参加训练营的总人数 */
  training_join_user_count: number;
  /** 总打卡数 */
  attendance_total_count: number;
  /** 最喜欢打卡时间的人数分布 */
  attendance_preferred_time_user_count_map?: Record<
    string,
    { user_count: number; percent: number }
  >;
}

export interface IUserReport2023 extends ICommonFields {
  user_id: string;
  user: IUser;
  statistics: IUserReportStatistics;
  global_statistics: IGlobalReportStatistics;
}
