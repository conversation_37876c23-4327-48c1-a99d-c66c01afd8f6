import { RichText } from '@/components/RichText';
import { Comment } from '@ant-design/compatible';
import { CaretRightOutlined, MessageOutlined } from '@ant-design/icons';
import {
  extractText,
  getFileUrl,
  IComment,
} from '@nice-people/nice-21day-shared';
import { Modal, Typography } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import CreateAttendanceComment from './CreateAttendanceComment';

interface ICommentItemProps {
  comment: IComment;
  replyEnable?: boolean;
  onReplyFinished?: () => void;
}

const CommentItem = ({
  comment,
  replyEnable = true,
  onReplyFinished,
}: ICommentItemProps) => {
  const [showReply, setShowReply] = useState(false);

  return (
    <>
      <Comment
        key={comment.id}
        author={
          <div className="font-medium">
            {comment.author?.nick_name}{' '}
            {comment.reply ? (
              <>
                <CaretRightOutlined /> {comment.reply?.author?.nick_name}
              </>
            ) : (
              ''
            )}
          </div>
        }
        avatar={getFileUrl(comment.author?.avatar_url)}
        content={
          <>
            {comment?.reply && (
              <Typography.Paragraph
                className="cursor-pointer"
                onClick={() => {
                  Modal.info({
                    title: '评论详情',
                    icon: null,
                    width: 600,
                    content: (
                      <div>
                        <CommentItem
                          comment={comment.reply!}
                          replyEnable={false}
                        />
                      </div>
                    ),
                    okText: '关闭',
                  });
                }}
              >
                <blockquote className="line-clamp-2 text-gray-500">
                  {extractText(comment.reply.content)}
                </blockquote>
              </Typography.Paragraph>
            )}
            <RichText
              className="!min-h-min"
              readOnly
              initValue={comment.content}
            />
          </>
        }
        datetime={dayjs(comment.created_at).fromNow()}
        actions={
          replyEnable
            ? [
                <span key="reply" onClick={() => setShowReply((prev) => !prev)}>
                  <MessageOutlined className="mr-1" />
                  回复
                </span>,
              ]
            : []
        }
      />

      {showReply && (
        <div className="pl-[48px]">
          <CreateAttendanceComment
            height={100}
            showAvatar={false}
            attendance_id={comment.target_id}
            reply_id={comment.id}
            onFinish={() => {
              setShowReply(false);
              onReplyFinished?.();
            }}
          />
        </div>
      )}
    </>
  );
};

export default CommentItem;
