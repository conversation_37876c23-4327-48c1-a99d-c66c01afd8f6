# 21 天

## 开发

```bash
pnpm

# dev 后台管理
pnpm run dev:web
# build 后台管理
pnpm run build:web

# dev 微信小程序
pnpm run dev:weapp
# build 微信小程序
pnpm run build:weapp
```

如果是 `Node 18.x`，记得先 

```
export NODE_OPTIONS=--openssl-legacy-provider
```

## 部署

使用 github action 触发部署。

1. 在 github 页面部署: [github action 部署](https://github.com/nice-people-frontend-community/nice-21day/actions/workflows/deploy.yml)

![trigger-workflow-github](./docs/trigger-workflow-github.png)

2. 在 vscode 部署: vscode 安装 [GitHub Actions 插件](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-github-actions)

![trigger-workflow-vscode](./docs/trigger-workflow-vscode.png)

## pnpm 简单的使用指南

```bash

# 全局安装依赖
pnpm add -w typescript
# 全局安装 Dev 依赖
pnpm add -w -D typescript

# 给某个 package 安装依赖
pnpm add @nice-people/nice-21day-shared --filter @nice-people/nice-21day-web

```

