import request from '@/utils/request';
import {
  IAttendanceLog,
  IAttendanceLogLike,
  ICreateAttendanceLogLikeData,
  IQueryAttendanceLogLikeParams,
  IQueryAttendanceLogLikeTopParams,
} from '@nice-people/nice-21day-shared';

/**
 * 获取点赞列表
 */
export const queryAttendanceLogLikes = async (
  params: IQueryAttendanceLogLikeParams,
) => await request.get<IAttendanceLogLike[]>('/attendance-logs/like', params);

/**
 * 获取点赞详情
 */
export const queryAttendanceLogLikeDetail = async (likeId: string) =>
  await request.get<IAttendanceLogLike>(`/attendance-logs/like/${likeId}`);

/**
 * 获取某个训练营下点赞Top的打卡记录
 */
export const queryAttendanceLogLikeTop = async (
  params: IQueryAttendanceLogLikeTopParams,
) => await request.get<IAttendanceLog[]>(`/attendance-logs/like/hot`, params);

/**
 * 新增一个点赞
 */
export const createAttendanceLogLike = async (
  data: ICreateAttendanceLogLikeData,
) => await request.post<IAttendanceLogLike>(`/attendance-logs/like`, data);

/**
 * 取消一个点赞
 */
export const deleteAttendanceLogLike = async (likeId: string) =>
  await request.delete<IAttendanceLogLike>(`/attendance-logs/like/${likeId}`);
