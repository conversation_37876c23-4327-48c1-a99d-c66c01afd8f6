import { useCurrentUser } from '@/hooks/useCurrentUser';
import { queryUserDetail } from '@/services';
import { IUser } from '@nice-people/nice-21day-shared';
import { history, Outlet, useParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Spin } from 'antd';
import React, { useEffect, useMemo } from 'react';

export interface IUserLayoutContext {
  user: IUser;
}

const UserLayout: React.FC = () => {
  const params = useParams();
  const { currentUser } = useCurrentUser();

  const userId = useMemo(() => {
    return params.user_id || currentUser.id;
  }, [currentUser, params.user_id]);

  const { loading, data: user = {} as IUser } = useRequest(
    () => queryUserDetail(userId),
    {
      ready: Boolean(userId),
      refreshDeps: [userId],
    },
  );

  // 没有用户ID跳转到登陆页面
  useEffect(() => {
    if (!currentUser.id) {
      history.replace('/user/login');
    }
  }, [currentUser.id]);

  if (loading) {
    return (
      <div className="py-4 flex items-center justify-center">
        <Spin spinning />
      </div>
    );
  }

  return <Outlet context={{ user }} />;
};

export default UserLayout;
