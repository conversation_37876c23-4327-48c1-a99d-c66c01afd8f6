import { getFileUrl, taroInnerAudioContextManager } from '@/utils';
import { ITouchEvent, Image, View } from '@tarojs/components';
import { useCallback, useEffect, useState } from 'react';
import './index.scss';

interface IBackgroundMusicProps {
  bgmUrl: string;
  autoplay: boolean;
}

const BackgroundMusic = ({ bgmUrl, autoplay }: IBackgroundMusicProps) => {
  const innerAudioContext =
    taroInnerAudioContextManager.getTaroInnerAudioContext();
  const [audioContextIns] = useState(() => innerAudioContext); // 音频实例持久化

  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  audioContextIns.autoplay = autoplay;
  audioContextIns.loop = true;
  audioContextIns.src = getFileUrl(bgmUrl);
  audioContextIns.onPlay(() => {
    console.log('开始播放');
    setIsPlaying(true);
  });
  audioContextIns.onPause(() => {
    console.log('暂停播放');
    setIsPlaying(false);
  });
  audioContextIns.onError((res) => {
    console.log(res.errMsg);
    console.log(res.errCode);
  });

  const handleTooglePlay = useCallback(
    (event: ITouchEvent) => {
      event.preventDefault();
      event.stopPropagation();

      if (!isPlaying) {
        return audioContextIns.play();
      }
      return audioContextIns.pause();
    },
    [isPlaying],
  );

  useEffect(() => {
    return () => {
      audioContextIns.destroy();
    };
  }, []);

  return (
    <View
      className={`bmg--icon w-[50px] h-[50px] ${isPlaying ? 'playing' : ''}`}
      onClick={handleTooglePlay}
    >
      <Image src={require('./music.svg')} className="w-[50px] h-[50px]" />
    </View>
  );
};
export default BackgroundMusic;
