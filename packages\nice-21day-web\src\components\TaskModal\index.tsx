import { ITrainingTask } from '@nice-people/nice-21day-shared';
import { Button, Modal, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import React from 'react';

interface IProps {
  visible: boolean;
  tasks: ITrainingTask[];
  handleOk: () => void;
}

const TasksModal: React.FC<IProps> = (props) => {
  const { visible, handleOk, tasks } = props;

  const columns: ColumnsType<ITrainingTask> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      ellipsis: true,
      // render: (text: string) => text?.substring(0, 6),
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
    },
    // {
    //   title: '任务描述',
    //   dataIndex: 'description',
    //   key: 'description',
    // },
  ];

  return (
    <Modal
      title="任务详情"
      visible={visible}
      onCancel={handleOk}
      footer={
        <>
          <Button type="primary" onClick={handleOk}>
            我知道了
          </Button>
        </>
      }
    >
      <Table
        bordered
        rowKey="id"
        size="small"
        dataSource={tasks}
        columns={columns}
        pagination={false}
      />
    </Modal>
  );
};

export default TasksModal;
