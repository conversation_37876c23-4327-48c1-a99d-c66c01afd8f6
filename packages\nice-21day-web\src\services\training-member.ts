import {
  EState,
  IPageFactory,
  IQueryTrainingMemberParams,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';

/**
 * 训练营成员列表（分页）
 */
export const queryTrainingMembers = async (
  params: IQueryTrainingMemberParams,
) => {
  return await request<IPageFactory<ITrainingMember>>('/training-users', {
    params,
  });
};

/**
 * 查询训练营成员详情
 */
export const queryTrainingMemberDetail = async (id: string) => {
  return await request<ITrainingMember>(`/training-users/${id}`, {});
};

/**
 * 查询训练营成员详情
 */
export const queryTrainingMemberDetailByTrainingId = async (
  trainingId: string,
  userId: string,
) => {
  const response = await request<IPageFactory<ITrainingMember>>(
    `/training-users`,
    {
      params: {
        page: 1,
        size: 1,
        training_id: trainingId,
        user_id: userId,
      },
    },
  );
  return response?.rows.length > 0 ? response?.rows[0] : undefined;
};

/**
 * 创建训练营成员
 */
export const createTrainingMember = async (data: ITrainingMember) => {
  return await request<ITrainingMember>('/training-users', {
    method: 'POST',
    data: { ...data },
  });
};

/**
 * 编辑训练营成员
 */
export const updateTrainingMember = async ({
  id,
  ...rest
}: ITrainingMember) => {
  return await request<ITrainingMember>(`/training-users/${id}`, {
    method: 'PUT',
    data: { ...rest },
  });
};

/**
 * 启用，禁用
 */
export const updateTrainingMemberState = async (id: string, state: EState) => {
  return await request(`training-users/${id}/state`, {
    method: 'PUT',
    data: { state },
  });
};

/**
 * 删除训练营成员
 */
export const deleteTrainingMember = async (id: string) => {
  return await request(`/training-users/${id}`, {
    method: 'DELETE',
  });
};

/**
 * 获取训练营成员详情
 */
export const getTrainingMemberDetailByUserId = async (
  training_id: string,
  user_id: string,
) => {
  return await request<ITrainingMember>(
    `/trainings/${training_id}/users/${user_id}`,
  );
};

/**
 * 训练营成员排行榜列表
 */
export const queryTrainingMemberRankings = async (training_id: string) => {
  return await request<ITrainingMember[]>(`/training-users/rankings`, {
    params: {
      training_id,
    },
  });
};

/**
 * 更新我在某个训练营下的总结
 */
export const updateMyTrainingSummary = async (
  trainingId: string,
  summary: string,
) => {
  return await request<ITrainingMember>(
    `/my/trainings/${trainingId}/summaries`,
    {
      method: 'PUT',
      data: { summary },
    },
  );
};
