import AttenanceTimeChart from '@/components/business/AttendancesTimeDistributions/AttenanceTimeChart';
import { Image, View } from '@tarojs/components';
import { useMemo } from 'react';
import HighlightWrapper from '../../components/HighlightWrapper';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

export default ({ reportData, isActive }: IPageProps) => {
  const { statistics, global_statistics } = reportData;

  const preferredTimeAttendanceRate = useMemo(() => {
    if (
      statistics.attendance_total_count &&
      statistics.attendance_count_at_preferred_time
    ) {
      return +(
        (statistics.attendance_count_at_preferred_time /
          statistics.attendance_total_count) *
        100
      ).toFixed(0);
    }
    return 0;
  }, [
    statistics.attendance_total_count,
    statistics.attendance_count_at_preferred_time,
  ]);

  return (
    <View
      className={`${styles.container} flex justify-center flex-col items-center`}
    >
      <View className="absolute top-0px right-[-40px]">
        <Image
          className="w-[550px] h-[440px]"
          src={require('./assets/alarm.svg')}
        />
      </View>

      <View className="report-2023__content absolute top-350px">
        <View>这一年</View>
        <View>
          你制定了{' '}
          <HighlightWrapper
            active={isActive}
            data={statistics.task_total_count}
          />{' '}
          个任务
        </View>
        <View>
          累计打卡{' '}
          <HighlightWrapper
            active={isActive}
            data={statistics.attendance_total_count}
          />{' '}
          次
        </View>
        {statistics.attendance_count_at_preferred_time > 0 && (
          <>
            <View>
              你最喜欢在{' '}
              <HighlightWrapper
                active={isActive}
                data={statistics.attendance_preferred_time}
              />{' '}
              点打卡
            </View>
            <View>
              共打卡{' '}
              <HighlightWrapper
                active={isActive}
                data={statistics.attendance_count_at_preferred_time}
              />{' '}
              次，占比{' '}
              <HighlightWrapper
                active={isActive}
                data={preferredTimeAttendanceRate}
                suffix="%"
              />
            </View>
            <View>
              有{' '}
              <HighlightWrapper
                active={isActive}
                data={
                  global_statistics.attendance_preferred_time_user_count_map[
                    statistics.attendance_preferred_time
                  ]?.['user_count'] || 0
                }
              />{' '}
              位同学和你有相同的喜好
            </View>
          </>
        )}
      </View>
      <View
        className="absolute bottom-92px"
        style={{ width: '90%', height: '200px' }}
      >
        <AttenanceTimeChart
          id="user-2023-attendances-time-distributions"
          data={statistics.addendance_time_distribution}
          config={{
            axiosColor: '#FFAC0B',
            axisStyle: {
              label: { fill: '#fff' },
              grid: { stroke: '#fff' },
            },
          }}
        />
      </View>
    </View>
  );
};
