import {
  IPageFactory,
  IQueryUserParams,
  IUser,
} from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';
/**
 * 查询用户列表（所有的）
 */
export const queryAllUsers = async (nick_name?: string) => {
  return await request<IUser[]>('/users/as-list', {
    params: { nick_name },
  });
};

/**
 * 查询用户列表（分页）
 */
export const queryUsers = async (params: IQueryUserParams) => {
  return await request<IPageFactory<IUser>>('/users', {
    params: { ...params },
  });
};

/**
 * 获取用户详情
 */
export const queryUserDetail = async (id: string) => {
  return await request<IUser>(`/users/${id}`);
};

/**
 * 刷新 accessToken
 */
export const updateUserAccessToken = async (id: string) => {
  return await request<IUser>(`/users/${id}/access-token`, {
    method: 'PUT',
    data: {},
  });
};

/**
 * 修改用状态
 */
export const changeUserStatus = async (id: string, state: string) => {
  return await request(`/users/${id}/state`, {
    method: 'PUT',
    data: { state },
  });
};
