import {
  AttendancesDateDistributions,
  AttendancesTimeDistributions,
  Card,
  MetricGrid,
  TrainingLayout,
} from '@/components';
import { useAttendanceAnalysis, useRouterParams, useTraining } from '@/hooks';
import { formatNumber } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import { useShareAppMessage } from '@tarojs/taro';
import { AtLoadMore } from 'taro-ui';
import './index.scss';

const TrainingAttendanceAnalysis = () => {
  const { training_id } = useRouterParams();

  const { trainingDetail, queryTrainingLoading } = useTraining(training_id);
  const { attendanceAnalysis, queryAttendanceAnalysisLoading } =
    useAttendanceAnalysis(training_id);

  useShareAppMessage(() => {
    return {
      title: `${trainingDetail?.name}的打卡统计`,
      path: `/pages/analytics/training-attendance-analysis/index?training_id=${training_id}`,
    };
  });

  if (queryTrainingLoading || queryAttendanceAnalysisLoading) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <View className="training-analysis-page">
      <TrainingLayout training={trainingDetail} title="训练营打卡统计">
        <Card title="打卡指标统计">
          <MetricGrid
            data={[
              {
                key: '参加人数',
                label: '参加人数',
                value: attendanceAnalysis.join_user_count,
                link: `/pages/training-member-list/index?training_id=${training_id}`,
              },
              {
                key: '总打卡数',
                label: '总打卡数',
                value: formatNumber(
                  attendanceAnalysis.total_attendance_logs_count,
                ),
                link: `/pages/attendance-timeline/index?training_id=${training_id}`,
              },
              {
                key: '正常打卡数',
                label: '正常打卡数',
                value: formatNumber(attendanceAnalysis.total_attendance_count),
              },
              {
                key: '请假数',
                label: '请假数',
                value: formatNumber(attendanceAnalysis.total_level_count),
              },
              {
                key: '打卡率',
                label: '打卡率',
                value: attendanceAnalysis.total_attendance_rate + '%',
              },
              {
                key: '积分达标率',
                label: '积分达标率',
                value: attendanceAnalysis.total_storard_rate + '%',
              },
            ]}
          />
        </Card>
        <Card title="打卡日期分布" styles={{ paddingBottom: 0 }}>
          <AttendancesDateDistributions trainingId={training_id} />
        </Card>
        <Card title="打卡时间段分布" styles={{ paddingBottom: 0 }}>
          <AttendancesTimeDistributions trainingId={training_id} />
        </Card>
      </TrainingLayout>
    </View>
  );
};

export default TrainingAttendanceAnalysis;
