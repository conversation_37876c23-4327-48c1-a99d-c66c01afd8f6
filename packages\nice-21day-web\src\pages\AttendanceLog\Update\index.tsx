import { SelectTraining } from '@/components/SelectTraining';
import { SelectUser } from '@/components/SelectUser';
import {
  queryAttendanceLogDetail,
  updateAttendanceLog,
  uploadFile,
} from '@/services';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import {
  EAttendanceState,
  getFileUrl,
  IAttendanceLog,
  IAttendanceTask,
  ITrainingTask,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { history, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  notification,
  Result,
  Row,
  Skeleton,
  Space,
  Upload,
} from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import { UploadChangeParam, UploadFile } from 'antd/lib/upload';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';

const AttendanceLogForm: React.FC = () => {
  const params = useParams();
  const [submiting, setSubmiting] = useState(false);
  const [form] = Form.useForm();

  const [uploadLoading, setUploadLoading] = useState(false);

  // 维护已上传的文件
  // {任务ID: [文件数组]}
  const [fileMapping, setFileMapping] = useState<Record<string, string[]>>({});

  // 打卡记录ID
  const attendanceId = useMemo(() => params.id, [params]);

  const [loading, setLoading] = useState(true);
  const [attendance, setAttendance] = useState<IAttendanceLog>();

  const queryAttendanceDetail = useCallback(() => {
    if (!attendanceId) {
      return;
    }
    setLoading(true);
    queryAttendanceLogDetail(attendanceId)
      .then((res) => {
        // 更新每个任务的资源信息
        setAttendance(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [attendanceId]);

  useEffect(() => {
    queryAttendanceDetail();
  }, [attendanceId]);

  const attendanceTasks = useMemo(() => {
    const _attendanceTasks = parseArrayJson<IAttendanceTask>(
      attendance?.attendance_tasks || '',
    );

    // 更新文件映射关系
    const mapping: Record<string, string[]> = {};
    _attendanceTasks.forEach((attendance) => {
      mapping[attendance.id] = attendance.attendance_files || [];
    });
    setFileMapping(mapping);

    return _attendanceTasks;
  }, [attendance?.attendance_tasks]);

  const handleFileChange = useCallback(
    (fileInfo: UploadChangeParam<UploadFile<any>>, task: ITrainingTask) => {
      const mapping = _.cloneDeep(fileMapping);

      // 删除
      if (fileInfo.file.status === 'removed') {
        mapping[task.id] = [...(mapping[task.id] || [])].filter(
          (url) => url !== fileInfo.file.url,
        );
        setFileMapping(mapping);
        return;
      }

      // 上传新增
      const formData = new FormData();
      formData.append('file', fileInfo.file as any);
      message.loading('上传中...');
      setUploadLoading(true);
      uploadFile(formData)
        .then((file) => {
          mapping[task.id] = [...(mapping[task.id] || []), file.path];
          setFileMapping(mapping);
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          setUploadLoading(false);
          setTimeout(() => {
            message.destroy();
          }, 1000);
        });
    },
    [fileMapping],
  );

  const handleFinish = useCallback(
    (values: any) => {
      if (!attendance?.id) {
        notification.error({
          message: '打卡失败',
          description: '打卡记录不存在或已被删除',
        });
        return;
      }
      const { tasks_array = [] } = values;
      // 检查任务填写情况
      // 组装数据
      const _attendanceTasks: IAttendanceTask[] = [];
      tasks_array.forEach((task: IAttendanceTask) => {
        _attendanceTasks.push({
          ...task,
          attendance_content: task.attendance_content || '',
          attendance_files: fileMapping[task.id] ?? [],
        });
      });

      if (
        _attendanceTasks.every(
          (task) =>
            !task.attendance_content && task.attendance_files?.length === 0,
        )
      ) {
        notification.error({
          message: '打卡失败',
          description: '至少打卡一个任务',
        });
        return;
      }

      const data = {
        // ...attendance,
        // attendance_date: attendance.attendance_date,
        // 只需要更新打卡内容即可
        id: attendance.id,
        attendance_state: EAttendanceState.Attendance,
        attendance_tasks: JSON.stringify(_attendanceTasks),
        // audit_state: attendance.audit_state || EAttendanceLogAuditState.Valid,
        // audit_comment: attendance.audit_comment || '审核通过',
      } as IAttendanceLog;

      Modal.confirm({
        title: '确定保存吗？',
        onOk: () => {
          setSubmiting(true);
          updateAttendanceLog(data)
            .then(() => {
              message.success('打卡成功');
              history.back();
            })
            .catch(() => {
              message.error('打卡失败');
            })
            .finally(() => {
              setSubmiting(false);
            });
        },
      });
    },
    [fileMapping, attendance],
  );

  console.log('fileMapping', fileMapping);

  if (loading) {
    return <Skeleton loading />;
  }

  if (!attendance?.id) {
    return <Result status="error" title="打卡记录不存在或已被删除" />;
  }

  return (
    <ProCard>
      <Form
        name="basic"
        onFinish={handleFinish}
        autoComplete="off"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}
        form={form}
        initialValues={{
          training_id: attendance.training_id,
          user_id: attendance.user_id,
          attendance_date: dayjs(attendance.attendance_date),
          tasks_array: attendanceTasks,
        }}
      >
        <Form.Item label="id" name="id" hidden>
          <Input />
        </Form.Item>

        <Form.Item
          label="用户"
          name="user_id"
          rules={[{ required: true, message: '请选择用户' }]}
        >
          <SelectUser bordered={false} disabled />
        </Form.Item>
        <Form.Item
          label="训练营"
          name="training_id"
          rules={[{ required: true, message: '请选择训练营' }]}
        >
          <SelectTraining bordered={false} disabled />
        </Form.Item>

        <Form.Item
          label="打卡日期"
          name="attendance_date"
          rules={[{ required: true, message: '请选择打卡日期' }]}
        >
          <DatePicker
            format="YYYY-MM-DD"
            allowClear={false}
            bordered={false}
            disabled
          />
        </Form.Item>

        <Form.Item label="任务" name="tasks_array" required>
          <Form.List name="tasks_array">
            {(fields) => (
              <>
                {fields.map((field) => {
                  const taskInfo = attendanceTasks[field.key];
                  return (
                    <Card key={taskInfo.id} style={{ marginBottom: 10 }}>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        label="ID"
                        hidden
                        name={[field.name, 'id']}
                        rules={[{ required: true }]}
                      >
                        <Input placeholder="ID" />
                      </Form.Item>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        label="任务名称"
                        name={[field.name, 'name']}
                        style={{ marginBottom: 4 }}
                      >
                        <Input bordered={false} readOnly placeholder="name" />
                      </Form.Item>
                      <Form.Item
                        hidden
                        labelCol={{ span: 4 }}
                        label="任务说明"
                        name={[field.name, 'description']}
                        style={{ marginBottom: 4 }}
                      >
                        <Input
                          bordered={false}
                          readOnly
                          placeholder="description"
                        />
                      </Form.Item>
                      <Form.Item
                        labelCol={{ span: 4 }}
                        label="打卡内容"
                        name={[field.name, 'attendance_content']}
                        rules={[{ required: false }]}
                      >
                        <Input.TextArea
                          maxLength={256}
                          placeholder="打卡内容"
                        />
                      </Form.Item>
                      <Row>
                        <Col offset={4}>
                          <Upload
                            listType="picture-card"
                            accept="image/png, image/jpeg"
                            maxCount={9}
                            fileList={(
                              fileMapping[taskInfo?.id as string] || []
                            ).map((url) => ({
                              url: getFileUrl(url),
                              uid: url,
                              status: 'done',
                              name: url,
                            }))}
                            beforeUpload={() => false}
                            onChange={(info) =>
                              handleFileChange(info, taskInfo)
                            }
                          >
                            <div>
                              {uploadLoading ? (
                                <LoadingOutlined />
                              ) : (
                                <PlusOutlined />
                              )}
                              <div style={{ marginTop: 8 }}>上传图片</div>
                            </div>
                          </Upload>
                        </Col>
                      </Row>
                    </Card>
                  );
                })}
              </>
            )}
          </Form.List>
        </Form.Item>
        <Form.Item label="备注信息" name="description">
          <TextArea rows={4} maxLength={256} placeholder="请填入" />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4 }}>
          <Space>
            <Button type="primary" htmlType="submit" loading={submiting}>
              打卡
            </Button>
            <Button loading={submiting} onClick={() => history.back()}>
              返回
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default AttendanceLogForm;
