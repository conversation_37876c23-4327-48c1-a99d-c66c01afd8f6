import { UserInfo } from '@/components/UserInfo';
import { queryAttendanceLogDetail } from '@/services';
import { PageHeader } from '@ant-design/pro-components';
import {
  ATTENDANCE_LOG_AUDIT_STATE_MAPPING,
  IAttendanceLog,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Link, useParams } from '@umijs/max';
import { Descriptions, Result, Skeleton, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';
import AttendanceLogProfile from '../components/AttendanceLogProfile';
import AttendanceAudit from '../components/Audit';

const AttendanceLogProfilePage: React.FC = () => {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [attendanceLog, setAttendanceLog] = useState<IAttendanceLog>();

  const queryAttendanceLogInfo = useCallback(() => {
    if (!params.id) {
      return;
    }
    setLoading(true);
    queryAttendanceLogDetail(params.id)
      .then((res) => {
        setAttendanceLog(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.id]);

  useEffect(() => {
    queryAttendanceLogInfo();
  }, [params.id]);

  if (loading) {
    return <Skeleton loading />;
  }

  if (!attendanceLog?.id) {
    return <Result status="error" title="打卡记录不存在或已被删除" />;
  }

  return (
    <>
      <PageHeader
        ghost={true}
        title={attendanceLog.training?.name}
        subTitle={
          <Space>
            <Tag>
              进度：
              {TRAINING_PROGRESS_MAPPING[attendanceLog.training!.progress]}
            </Tag>
            <Tag>
              起止时间：{attendanceLog.training?.start_time} ~{' '}
              {attendanceLog.training?.end_time}
            </Tag>
            <Tag>参与人数：{attendanceLog.training?.join_user_count || 0}</Tag>
          </Space>
        }
        extra={
          <>
            <AttendanceAudit
              attendanceId={attendanceLog.id}
              auditState={attendanceLog.audit_state}
              onFinish={() => queryAttendanceLogInfo()}
            />
            <Link to={`/admin/attendance-log/${attendanceLog.id}/update`}>
              编辑记录
            </Link>
          </>
        }
      >
        <Descriptions
          size="small"
          column={3}
          styles={{
            label: { lineHeight: '32px' },
            content: { lineHeight: '32px' },
          }}
        >
          <Descriptions.Item label="用户">
            <UserInfo {...attendanceLog.user} />
          </Descriptions.Item>
          <Descriptions.Item label="打卡时间">
            {dayjs(attendanceLog.created_at).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="审核状态">
            {ATTENDANCE_LOG_AUDIT_STATE_MAPPING[attendanceLog.audit_state]}
          </Descriptions.Item>
          <Descriptions.Item span={3} label="审核信息">
            {attendanceLog.audit_comment}
          </Descriptions.Item>
        </Descriptions>
      </PageHeader>
      <AttendanceLogProfile attendanceLog={attendanceLog} />
    </>
  );
};

export default AttendanceLogProfilePage;
