import { usePageShowAgain, useTrainingUserList } from '@/hooks';
import { getFileUrl } from '@/utils/index';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtList, AtListItem, AtLoadMore } from 'taro-ui';

interface ITrainingMemberListProps {
  training_id: string;
}

const TrainingMemberList: React.FC<ITrainingMemberListProps> = (props) => {
  const { training_id } = props;

  const {
    fetchTrainingMembers,
    trainingUserList,
    queryTrainingUserListLoading,
  } = useTrainingUserList(training_id);

  usePageShowAgain(fetchTrainingMembers);

  if (queryTrainingUserListLoading) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <View>
      <AtList>
        {trainingUserList.map((member) => (
          <AtListItem
            key={member.id}
            title={member.user?.nick_name || '无效用户'}
            thumb={getFileUrl(member.user?.avatar_url)}
            arrow={member.user ? 'right' : undefined}
            onClick={() => {
              if (!member.user) {
                return;
              }
              Taro.navigateTo({
                url: `/pages/training-member-detail/index?member_id=${member.id}`,
              });
            }}
          />
        ))}
      </AtList>
    </View>
  );
};

export default TrainingMemberList;
