import { useCurrentUser } from '@/hooks/useCurrentUser';
import ScoreRankItem from '@/pages/Share/components/ScoreRankItem';
import { queryTrainingMemberRankings } from '@/services';
import { useRequest } from 'ahooks';
import { List } from 'antd';
import { useContext, useMemo } from 'react';
import { TrainingLayoutContext } from '../Layout';
import { useFilter } from './useFilter';

export default () => {
  const { training } = useContext(TrainingLayoutContext);
  const { currentUser } = useCurrentUser();

  const { loading, data: scoreRankings = [] } = useRequest(
    queryTrainingMemberRankings,
    {
      refreshDeps: [training.id],
      defaultParams: [training.id],
    },
  );

  const { FilterComp, filterResult } = useFilter({
    list: scoreRankings,
    training,
  });

  const myRanking = useMemo(() => {
    return scoreRankings?.filter((item) => item.user_id === currentUser.id);
  }, [scoreRankings, currentUser]);

  return (
    <div>
      {FilterComp}

      {/* 全部数据 */}
      <List
        bordered={false}
        itemLayout="horizontal"
        dataSource={[...myRanking, ...filterResult]}
        loading={loading}
        renderItem={(item) => (
          <ScoreRankItem
            trainingUser={item}
            training={training}
            isMySelf={item.user?.id === currentUser?.id}
          />
        )}
      />
    </div>
  );
};
