import { isIntersection } from '@/utils';
import { Image, View } from '@tarojs/components';
import { useMemo } from 'react';
import HighlightWrapper from '../../components/HighlightWrapper';
import Quotation from '../../components/Quotation';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

const quotationList = [
  [
    '『靡不有初，鲜克有终』',
    '而你，我的朋友，你便是其中的佼佼者',
    '知行合一，可得始终',
  ],
  [
    '每一个好习惯，犹如夜空明星',
    '揽漫天星辰，亮人生明灯',
    '繁星点点，前路璀璨',
  ],
  [
    '『世上无难事，只怕有心人』',
    '你看，其实坚持这几个小小的目标',
    '也不是什么无法做到的事情嘛',
  ],
  ['你知道吗？迄今为止', '当时可能觉得有难度的任务', '你已经坚持很久了！'],
  [
    '『常立志，不如立长志』',
    '也行走的不快，但每个脚印都踏实坚定',
    '不要着急，从第一步慢慢走',
  ],
];

export default ({ reportData, isActive }: IPageProps) => {
  const { statistics, global_statistics } = reportData;

  const joinTrainingMessage = useMemo(() => {
    // 全部参加了
    if (
      statistics.training_joined_count ===
      global_statistics.attendance_total_count
    ) {
      return '是你啊！陪我走过了每一期';
    }
    // 最近2期没有参加
    const last2TrainingIds = global_statistics.training_2023_list
      .slice(0, 2)
      .map((el) => el.id);
    if (!isIntersection(last2TrainingIds, statistics.training_joined_ids)) {
      return '好久不见，已经实现目标了吗';
    }

    // 前2期没有参加
    const first2TrainingIds = global_statistics.training_2023_list
      .slice(-2)
      .map((el) => el.id);
    if (!isIntersection(first2TrainingIds, statistics.training_joined_ids)) {
      return '相逢虽晚，来日方长';
    }

    return '人生漫漫，幸逢相伴';
  }, [
    statistics.training_joined_count,
    statistics.training_joined_ids,
    global_statistics.attendance_total_count,
    global_statistics.training_2023_list,
  ]);

  return (
    <View className={`${styles.container} flex justify-center items-center`}>
      <View className="absolute bottom-90px right-40px">
        <Image
          svg
          className="w-440px h-410px"
          src={require('./assets/find.svg')}
        />
      </View>

      {/* 文字 */}
      {/* ========= */}
      <View className="report-2023__content absolute top-80px">
        <View>这一年</View>
        <View>
          你一共参加了{' '}
          <HighlightWrapper
            active={isActive}
            data={statistics.training_joined_count}
          />{' '}
          期训练营
        </View>
        <View>
          达标{' '}
          <HighlightWrapper
            active={isActive}
            data={statistics.training_standard_count}
          />{' '}
          期训练营，达标率{' '}
          <HighlightWrapper
            active={isActive}
            data={statistics.training_standard_rete * 100}
            suffix="%"
          />{' '}
        </View>

        {joinTrainingMessage && <View>{joinTrainingMessage}</View>}

        <View className="mt-20px">你不动声色的完成了</View>
        <View>
          <HighlightWrapper
            active={isActive}
            data={statistics.training_summary_words_total_count}
          />{' '}
          个总结文字
        </View>
        {/* 平均每期总结文字 <=50 */}
        <View>
          {statistics.training_joined_count === 0 ||
          statistics.training_joined_count === 0 ||
          statistics.training_summary_words_total_count /
            statistics.training_joined_count <=
            50 ? (
            <View>言简意赅，亦是一种魅力</View>
          ) : (
            <View>想必每一期都有很多收获吧</View>
          )}
        </View>

        <View className="report-2023__content--desc text-center mt-50rpx">
          <Quotation data={quotationList} />
        </View>
      </View>
    </View>
  );
};
