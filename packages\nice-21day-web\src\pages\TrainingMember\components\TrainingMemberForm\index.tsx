import { SelectTraining } from '@/components/SelectTraining';
import { SelectUser } from '@/components/SelectUser';
import { createTrainingMember, updateTrainingMember } from '@/services';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import {
  EState,
  getObjectKeys,
  ITrainingMember,
  ITrainingTask,
  parseArray<PERSON>son,
  TRAINING_PAYMENT_STATE_MAPPING,
  uuid,
} from '@nice-people/nice-21day-shared';
import { history, useSearchParams } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Space,
  Switch,
} from 'antd';
import React, { useState } from 'react';

interface IFormData extends ITrainingMember {
  tasks_array: ITrainingTask[];
}

interface ITrainingMemberFormProps {
  trainingMember?: ITrainingMember;
}

const TrainingMemberForm: React.FC<ITrainingMemberFormProps> = ({
  trainingMember = {} as ITrainingMember,
}) => {
  const [submiting, setSubmiting] = useState(false);
  const [form] = Form.useForm();

  const [searchParams] = useSearchParams();
  const training_id = searchParams.get('training_id') || undefined;

  const handleFinish = ({ state, tasks_array, ...values }: IFormData) => {
    Modal.confirm({
      title: '确定保存吗？',
      onOk: () => {
        setSubmiting(true);
        const data = {
          ...values,
          state: state ? EState.Enable : EState.Disable,
          tasks: JSON.stringify(
            tasks_array.map((task) => ({ ...task, id: task.id || uuid() })),
          ),
        };

        (trainingMember?.id
          ? updateTrainingMember(data)
          : createTrainingMember(data)
        )
          .then(() => {
            message.success('保存成功');
            history.back();
          })
          .finally(() => {
            setSubmiting(false);
          });
      },
    });
  };

  return (
    <ProCard>
      <Form<IFormData>
        name="basic"
        onFinish={handleFinish}
        autoComplete="off"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}
        form={form}
        initialValues={{
          // @ts-ignore
          training_id,
          ...trainingMember,
          tasks_array: parseArrayJson(trainingMember.tasks),
          state: trainingMember?.id
            ? trainingMember.state === EState.Enable
            : true,
        }}
      >
        <Form.Item label="id" name="id" hidden>
          <Input />
        </Form.Item>

        <Form.Item
          label="用户"
          name="user_id"
          rules={[{ required: true, message: '请选择用户' }]}
        >
          <SelectUser />
        </Form.Item>
        <Form.Item
          label="训练营"
          name="training_id"
          rules={[{ required: true, message: '请选择训练营' }]}
        >
          <SelectTraining />
        </Form.Item>
        <Form.Item
          label="押金支付"
          name="payment_state"
          rules={[{ required: true, message: '请选择押金支付状态' }]}
        >
          <Select placeholder="请选择">
            {getObjectKeys(TRAINING_PAYMENT_STATE_MAPPING).map((key) => (
              <Select.Option key={key} value={key}>
                {TRAINING_PAYMENT_STATE_MAPPING[key]}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="任务"
          name="tasks_array"
          required
          rules={[
            ({ getFieldValue }) => ({
              validator: async () => {
                const taskList = getFieldValue('tasks_array');
                if (taskList.length === 0) {
                  return Promise.reject(new Error('请至少填写1个任务'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Form.List name="tasks_array">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field) => (
                  <Card key={field.key} style={{ marginBottom: 10 }}>
                    <Form.Item
                      {...field}
                      labelCol={{ span: 4 }}
                      label="任务名称"
                      name={[field.name, 'name']}
                      rules={[{ required: true, message: '请填写任务名称' }]}
                    >
                      <Input
                        maxLength={32}
                        placeholder="请输入任务名称。例如：每天做一道力扣算法题"
                      />
                    </Form.Item>
                    <Form.Item
                      {...field}
                      labelCol={{ span: 4 }}
                      label="任务描述"
                      name={[field.name, 'description']}
                      rules={[{ required: false }]}
                    >
                      <Input.TextArea
                        maxLength={256}
                        placeholder="请输入任务描述信息。例如：每天做一道力扣算法题"
                      />
                    </Form.Item>

                    <Button
                      danger
                      disabled={fields.length <= 1}
                      onClick={() => {
                        remove(field.name);
                        form.validateFields(['tasks_array']);
                      }}
                      block
                      icon={<MinusOutlined />}
                    >
                      删除任务
                    </Button>
                  </Card>
                ))}

                <Form.Item>
                  <Button
                    type="primary"
                    onClick={() => {
                      add();
                      form.validateFields(['tasks_array']);
                    }}
                    block
                    icon={<PlusOutlined />}
                  >
                    新增任务
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form.Item>
        <Form.Item
          label="积分"
          name="score"
          extra="更新积分后台会自动计算达标情况"
          required
        >
          <InputNumber
            min={0}
            max={100}
            precision={1}
            placeholder="请填入"
            style={{ width: '300px' }}
          />
        </Form.Item>
        <Form.Item
          label="启用状态"
          name="state"
          valuePropName="checked"
          required
        >
          <Switch />
        </Form.Item>
        <Form.Item label="训练营总结" name="summary">
          <Input.TextArea rows={4} maxLength={500} placeholder="请填入" />
        </Form.Item>

        <Form.Item label="备注信息" name="description">
          <Input.TextArea rows={4} maxLength={256} placeholder="请填入" />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4 }}>
          <Space>
            <Button type="primary" htmlType="submit" loading={submiting}>
              保存
            </Button>
            <Button loading={submiting} onClick={() => history.back()}>
              返回
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default TrainingMemberForm;
