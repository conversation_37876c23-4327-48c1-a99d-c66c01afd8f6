import { ILoginData as IAdminLoginData, IUserLoginData } from '@/typings';
import { ICurrentUser, IUploadFile } from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';

/**
 * 管理员登录
 */
export const adminLogin = async (data: IAdminLoginData) => {
  return await request<{ access_token: string }>('/auth/admin/login', {
    method: 'POST',
    data: { ...data },
  });
};

/**
 * 用户登录
 */
export const userLogin = async (data: IUserLoginData) => {
  return await request<{ access_token: string }>('/auth/user/login', {
    method: 'POST',
    data: { ...data },
  });
};

/**
 * 退出登录
 */
export const logout = async () => {
  return await request('/auth/logout', {
    method: 'POST',
  });
};

/**
 * 获取当前登录人
 */
export const queryCurrentUser = async () => {
  return await request<ICurrentUser>('/auth/current-users');
};

export const uploadFile = async (data: any) => {
  return await request<IUploadFile>('/upload/alioss', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  });
};

/**
 * 生成微信小程序指定页面的二维码
 */
export const getwxacode = async (data: { path: string }) => {
  return request('/weixin/getwxacode', {
    method: 'POST',
    data,
  });
};
