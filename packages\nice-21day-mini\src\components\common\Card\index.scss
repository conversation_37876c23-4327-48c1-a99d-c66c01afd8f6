.card {
  $padding: 30px;

  background: #fff;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: $padding;
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;

  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .title {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        background: var(--default-color);
        border-radius: 0px 6px 6px 0px;
        width: 8px;
        height: 32px;
        left: -$padding;
        top: 6px;
      }
    }
    .extra {
      color: #999;
      font-size: 28px;
    }
  }
}
