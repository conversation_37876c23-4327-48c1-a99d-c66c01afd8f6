import { getAuthorization } from '@/utils/index';
import { ADMIN_DOMAIN } from '@nice-people/nice-21day-shared';
import Taro from '@tarojs/taro';
import interceptors from './interceptors';

interceptors.forEach((interceptorItem) => Taro.addInterceptor(interceptorItem));

// TODO: 这里最好根据环境自动处理域名，之后搞
export const DEV_BASE_DOMAIN = 'https://nice.yayujs.com';
// export const DEV_BASE_DOMAIN = 'http://127.0.0.1:7001';

/**
 * 根据环境获取最终请求的url
 */
export const getUrl = (url: string) => {
  let finalurl = '';
  if (process.env.NODE_ENV === 'development') {
    //开发环境 - 根据请求不同返回不同的BASE_URL
    // finalurl = 'http://127.0.0.1:7001/api/v1' + url;
    finalurl = `${DEV_BASE_DOMAIN}/api/v1${url}`;
  } else {
    // 生产环境
    finalurl = `${ADMIN_DOMAIN}/api/v1${url}`;
  }
  return finalurl;
};

interface IRequestOptions {
  url: string;
  data: Record<string, any> | undefined;
  config?: {
    hideLoading?: boolean;
    hideError?: boolean;
  };
}
class Request {
  dealRequest<T>(
    options: IRequestOptions,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  ) {
    let { url, data, config } = options;
    const option = {
      url: getUrl(url), //地址
      data: data, //传参
      method: method || 'GET', //请求方式
      timeout: 6000, // 超时时间
      header: {
        //请求头
        'content-type': 'application/json;charset=UTF-8',
        ...getAuthorization(),
      },
      config,
    };
    return Taro.request<T>(option);
  }

  get<T>(
    url: string,
    data?: IRequestOptions['data'],
    config?: IRequestOptions['config'],
  ) {
    let option = { url, data, config };
    return this.dealRequest<T>(option, 'GET');
  }

  post<T>(
    url: string,
    data: IRequestOptions['data'],
    config?: IRequestOptions['config'],
  ) {
    let params = { url, data, config };
    return this.dealRequest<T>(params, 'POST');
  }
  put<T>(
    url: string,
    data: IRequestOptions['data'],
    config?: IRequestOptions['config'],
  ) {
    let params = { url, data, config };
    return this.dealRequest<T>(params, 'PUT');
  }
  delete<T>(
    url: string,
    data?: IRequestOptions['data'],
    config?: IRequestOptions['config'],
  ) {
    let option = { url, data, config };
    return this.dealRequest<T>(option, 'DELETE');
  }
}
export default new Request();
