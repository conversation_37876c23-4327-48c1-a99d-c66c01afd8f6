import { queryIsFollowed } from '@/service/follow';
import { useCallback, useEffect, useState } from 'react';

/**
 * 查询2个人是否存在关注
 * @param userId 粉丝ID
 * @param followeeId 被关注人ID
 */
export const useIsfollowed = (userId: string, followeeId: string) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [isfollowed, setIsfollowed] = useState<boolean>(false);

  const fetchIsFollowed = useCallback(() => {
    if (!userId || !followeeId) {
      return;
    }

    // 同一个人不查询关注，没有意义
    if (userId === followeeId) {
      return;
    }

    setLoading(true);
    queryIsFollowed(userId, followeeId)
      .then(({ data: { isFollowed } }) => {
        setIsfollowed(isFollowed);
      })
      .catch(() => {
        setIsfollowed(false);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [userId, followeeId]);

  useEffect(() => {
    fetchIsFollowed();
  }, [fetchIsFollowed]);

  return {
    loading,
    fetchIsFollowed,
    isfollowed,
  };
};
