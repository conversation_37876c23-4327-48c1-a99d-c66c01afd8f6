import { ITrainingTask, parseArray<PERSON>son } from '@nice-people/nice-21day-shared';
import { useModel, useOutletContext } from '@umijs/max';
import { Button, message, Spin } from 'antd';
import clipboardJs from 'clipboard';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef } from 'react';

import { ITrainingLayoutContext } from '../Layout';
import styles from './index.less';

interface ITrainingDate {
  dateString: string;
  weekString: string;
}

const weekStringList = [
  'Sun.',
  'Mon.',
  'Tue.',
  'Wed.',
  'Thurs.',
  'Fri.',
  'Sat.',
];

/**
 * 报名表列的key
 */
export const REGISTRATION_FORM_COLUMN_KEY = {
  uuid: 'UUID',
  nickName: '微信昵称',
  task: '目标',
  clockDays: '打卡天数',
  totalPoints: '总积分',
} as const;

const renderTaskName = (taskName: string) => {
  if (taskName.startsWith('<21天')) {
    return taskName;
  }
  return `<21天：${taskName}>`;
};

const RegistrationForm = () => {
  const tableRef = useRef<HTMLTableElement>(null);
  const { training } = useOutletContext<ITrainingLayoutContext>();

  const { loading, queryAllTrainingMembers, trainingMembers } =
    useModel('traningMemberModel');

  useEffect(() => {
    queryAllTrainingMembers({
      training_id: training.id,
    });
  }, [training.id]);

  // 计算日期
  const trainingDates = useMemo(() => {
    const { start_time, end_time } = training;
    const diff = dayjs(end_time).diff(dayjs(start_time), 'days');
    const dateList: ITrainingDate[] = [];
    // 包含最后一天
    for (let index = 0; index <= diff; index++) {
      const date = dayjs(start_time).add(index, 'days');
      const dateString = date.format('MM-DD');
      console.log(dateString);
      const weekString = weekStringList[new Date(date.format()).getDay()];

      dateList.push({
        dateString,
        weekString,
      });
    }

    return dateList;
  }, [training.start_time, training.end_time]);

  const handleCopy = () => {
    if (!tableRef.current) return;

    const clipboard = new clipboardJs('#copy-table-btn', {
      target: () => tableRef.current!,
    });
    clipboard.on('success', function (e) {
      e.clearSelection();
      message.success('复制成功');
      setTimeout(() => {
        window.open(
          'https://www.yuque.com/yayu/nice-people/gmvxeum7ap2mxbmi#qctclker4w7pwn27mv61thqc2sg5wo7g',
        );
      }, 1000);
    });
    clipboard.on('error', function () {
      message.success('复制失败');
    });
  };

  return (
    <div>
      <Spin spinning={loading}>
        <Button
          type="primary"
          onClick={handleCopy}
          className={styles.copy}
          id="copy-table-btn"
        >
          复制报名表并去语雀粘贴
        </Button>
        <table className={styles.table} ref={tableRef}>
          <thead>
            <tr>
              {/* <td>组号</td> */}
              {/* <td>组长</td> */}
              <td rowSpan={3}>{REGISTRATION_FORM_COLUMN_KEY.uuid}</td>
              <td rowSpan={3}>{REGISTRATION_FORM_COLUMN_KEY.nickName}</td>
              <td rowSpan={3}>{REGISTRATION_FORM_COLUMN_KEY.task}</td>
              {/* <td rowSpan={3}>类型</td> */}
              {trainingDates.map((date, index) => (
                <td rowSpan={1} key={`row1_${date.dateString}`}>
                  Day{index + 1}
                </td>
              ))}
              <td rowSpan={3}>{REGISTRATION_FORM_COLUMN_KEY.clockDays}</td>
              <td rowSpan={3}>{REGISTRATION_FORM_COLUMN_KEY.totalPoints}</td>
            </tr>
            <tr>
              {trainingDates.map((date) => (
                <td rowSpan={1} key={`row2_${date.dateString}`}>
                  {date.weekString}
                </td>
              ))}
            </tr>
            <tr>
              {trainingDates.map((date) => (
                <td rowSpan={1} key={`row3_${date.dateString}`}>
                  {date.dateString}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {trainingMembers.map((user) => {
              const tasks = parseArrayJson<ITrainingTask>(user.tasks);

              return (
                <>
                  <tr key={`${user.user_id}_row`}>
                    <td rowSpan={tasks.length}>
                      <div className={styles.userId}>{user.user_id}</div>
                    </td>
                    <td rowSpan={tasks.length}>{user.user?.nick_name}</td>

                    {/* 任务相关 */}
                    {tasks.map(
                      (task, j) =>
                        j === 0 && (
                          <>
                            <td key={`${user.user_id}_${task.id}`}>
                              {renderTaskName(task.name)}
                            </td>
                            {/* 任务类型 */}
                            {/* <td>TODO</td> */}
                          </>
                        ),
                    )}

                    {trainingDates.map((date) => (
                      <td
                        rowSpan={tasks.length}
                        key={`${user.user_id}${date.dateString}`}
                      ></td>
                    ))}
                    {/* 打卡天数 */}
                    <td rowSpan={tasks.length}></td>
                    {/* 总积分 */}
                    <td rowSpan={tasks.length}></td>
                  </tr>
                  {/* 任务 */}
                  {tasks.map(
                    (task, j) =>
                      j > 0 && (
                        <tr key={`${user.user_id}_row_${j + 1}`}>
                          <td key={task.id}>{renderTaskName(task.name)}</td>
                          {/* 任务类型 */}
                          {/* <td>TODO</td> */}
                        </tr>
                      ),
                  )}
                </>
              );
            })}
          </tbody>
        </table>
      </Spin>
    </div>
  );
};

export default RegistrationForm;
