import { ICurrentUser } from '@nice-people/nice-21day-shared';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { useMemo, useState } from 'react';

/**
 * 获取设备信息
 */
export const useDevice = () => {
  const { pixelRatio, screenWidth } = Taro.getSystemInfoSync();
  return {
    screenWidth,
    pixelRatio,
    ratio: pixelRatio,
  };
};

/**
 * 获取 URL 查询参数
 */
export const useRouterParams = () => {
  return useRouter().params;
};

/**
 * 判断用户称谓：是否是我自己
 * @param currentUser 当前登录人
 * @param user_id 某个用户ID
 * @returns
 */
export const useUserCall = (currentUser: ICurrentUser, user_id?: string) => {
  const isMyself = useMemo(() => {
    return currentUser?.id === user_id;
  }, [user_id, currentUser?.id]);

  const callPrefix = useMemo(() => {
    return isMyself ? '我的' : 'TA的';
  }, [isMyself]);

  return {
    isMyself,
    callPrefix,
  };
};

/**
 * 页面再次显示
 * @param callback 页面再次显示时的回调
 * @returns
 */
export const usePageShowAgain = (callback?: () => void) => {
  const [isFirstShow, setIsFirstShow] = useState(true);

  useDidShow(() => {
    if (!isFirstShow) {
      callback?.();
      return;
    }

    setIsFirstShow(false);
  });

  return {
    isFirstShow,
  };
};
