import request from '@/utils/request';
import {
  IAttendanceCalendar,
  IAttendancesDateDistributions,
  IAttendancesTimeDistributions,
  ITrainingAttendanceRate,
} from '@nice-people/nice-21day-shared';

/**
 * 获取用户打卡日历
 */
export const queryUserAttendanceCalendar = async (
  training_id: string,
  user_id: string,
) => {
  return await request.get<IAttendanceCalendar>(
    `/analysis/attendances/calendar`,
    {
      training_id,
      user_id,
    },
  );
};

/**
 * 查询训练营打卡率统计
 */
export const queryTrainingAttendanceRate = async (
  id: string,
  attendance_date?: string,
) => {
  return await request.get<ITrainingAttendanceRate>(
    `/analysis/trainings/${id}/attendances-rate`,
    {
      attendance_date,
    },
  );
};

/**
 * 查询训练营打卡时间段分布统计
 */
export const queryAttendanceTimeDistribution = async (
  training: string,
  userId?: string,
) => {
  return await request.get<IAttendancesTimeDistributions[]>(
    `/analysis/trainings/${training}/attendances-time-distributions`,
    userId
      ? {
          user_id: userId,
        }
      : {},
  );
};

/**
 * 查询训练营打卡日期分布统计
 */
export const queryAttendanceDateDistribution = async (
  training: string,
  userId?: string,
) => {
  return await request.get<IAttendancesDateDistributions[]>(
    `/analysis/trainings/${training}/attendances-date-distributions`,
  );
};
