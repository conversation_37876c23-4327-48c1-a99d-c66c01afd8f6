import { AttendanceTimelineItem } from '@/components';
import { useAttendanceRecommends } from '@/hooks';
import { ATTENDANCE_LIMIT_MORNING_HOURS } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import { memo } from 'react';
import { AtIcon } from 'taro-ui';

const DEFAULT_LIMIT = 2;
const ATTENDANCE_TIME_LIMIT = dayjs()
  .set('hour', ATTENDANCE_LIMIT_MORNING_HOURS)
  .set('minute', 0)
  .set('second', 0);

interface IRecommendAttendanceProps {
  trainingId: string;
}

const RecommendAttendance = ({ trainingId }: IRecommendAttendanceProps) => {
  const { recommendsList, freshData } = useAttendanceRecommends(
    trainingId,
    dayjs().isBefore(ATTENDANCE_TIME_LIMIT)
      ? dayjs().subtract(1, 'days').format('YYYY-MM-DD')
      : dayjs().format('YYYY-MM-DD'),
    DEFAULT_LIMIT,
  );

  if (recommendsList.length < DEFAULT_LIMIT) {
    return null;
  }

  return (
    <>
      <View className=" w-full flex justify-end mt-2 mr-2">
        <View
          className="text-sm"
          style={{ fontSize: 14, color: '#999' }}
          onClick={freshData}
        >
          换一批
          <AtIcon value="reload" size={14}></AtIcon>
        </View>
      </View>
      <View className="w-full">
        {recommendsList.map((attendance) => (
          <AttendanceTimelineItem
            key={attendance.id}
            training_id={trainingId}
            attendance={attendance}
            marginSize="small"
          />
        ))}
      </View>
    </>
  );
};
export default memo(RecommendAttendance);
