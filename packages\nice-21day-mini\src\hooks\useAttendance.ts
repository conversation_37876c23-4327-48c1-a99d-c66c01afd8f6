import {
  queryAttendanceDateDistribution,
  queryAttendanceLogs,
  queryAttendanceTimeDistribution,
  queryRecommends,
  queryTrainingAttendanceRate,
} from '@/service';
import {
  EBooleanString,
  IAttendanceLog,
  IAttendancesDateDistributions,
  IAttendancesTimeDistributions,
  ITrainingAttendanceRate,
} from '@nice-people/nice-21day-shared';
import { useCallback, useEffect, useState } from 'react';

/**
 * 获取打卡记录
 */
export const useAllAttendanceLogs = (trainingId: string, userId?: string) => {
  const [queryAttendanceLogsLoading, setQueryAttendanceLogsLoading] =
    useState<boolean>(true);
  const [attendanceLogs, setAttendanceLogs] = useState<IAttendanceLog[]>([]);

  useEffect(() => {
    setQueryAttendanceLogsLoading(true);
    queryAttendanceLogs({
      training_id: trainingId,
      ...(userId ? { user_id: userId } : {}),
      with_user: EBooleanString.YES,
      page: 1,
      // TODO: 这里先不分页了
      // 训练营按照 100 人来计算，最多 21天*100人 = 2100 条记录
      size: 3000,
    })
      .then(({ data }) => {
        setAttendanceLogs(data.rows);
      })
      .finally(() => {
        setQueryAttendanceLogsLoading(false);
      });
  }, [trainingId, userId]);

  return {
    queryAttendanceLogsLoading,
    attendanceLogs,
  };
};

/**
 * 随机推荐打卡记录
 */
export const useAttendanceRecommends = (
  training_id: string,
  attendance_date: string,
  limit: number,
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [recommendsList, setRecommendsList] = useState<IAttendanceLog[]>([]);

  const freshData = useCallback(() => {
    setLoading(true);
    queryRecommends({ training_id, attendance_date, limit })
      .then(({ data }) => {
        setRecommendsList(data);
      })
      .finally(() => setLoading(false));
  }, [training_id, attendance_date]);

  useEffect(() => {
    freshData();
  }, []);
  return {
    recommendsListLoading: loading,
    recommendsList,
    freshData,
  };
};

/**
 * 训练营打卡统计
 */
export const useAttendanceAnalysis = (
  trainingId: string,
  attendanceDate?: string,
) => {
  const [queryAttendanceAnalysisLoading, setQueryAttendanceAnalysisLoading] =
    useState<boolean>(true);
  const [attendanceAnalysis, setAttendanceAnalysis] =
    useState<ITrainingAttendanceRate>({} as ITrainingAttendanceRate);

  useEffect(() => {
    setQueryAttendanceAnalysisLoading(true);
    queryTrainingAttendanceRate(trainingId, attendanceDate)
      .then(({ data }) => {
        setAttendanceAnalysis(data);
      })
      .finally(() => {
        setQueryAttendanceAnalysisLoading(false);
      });
  }, [trainingId, attendanceDate]);

  return {
    queryAttendanceAnalysisLoading,
    attendanceAnalysis,
  };
};

/**
 * 获取某个训练营下的打卡时间分布
 */
export const useTrainingTimeDistribution = (
  trainingId: string,
  userId?: string,
) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [timeDistributions, setTimeDistributions] = useState<
    IAttendancesTimeDistributions[]
  >([]);
  useEffect(() => {
    setLoading(true);
    queryAttendanceTimeDistribution(trainingId, userId)
      .then(({ data }) => {
        setTimeDistributions(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [trainingId, userId]);

  return {
    loading,
    timeDistributions,
  };
};

/**
 * 获取某个训练营下每天打卡人数分布
 */
export const useTrainingDateDistribution = (trainingId: string) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [dateDistributions, setDateDistributions] = useState<
    IAttendancesDateDistributions[]
  >([]);
  useEffect(() => {
    setLoading(true);
    queryAttendanceDateDistribution(trainingId)
      .then(({ data }) => {
        setDateDistributions(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [trainingId]);

  return {
    loading,
    dateDistributions,
  };
};
