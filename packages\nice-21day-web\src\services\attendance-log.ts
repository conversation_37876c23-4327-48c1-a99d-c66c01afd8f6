import {
  EAttendanceLogAuditState,
  IAttendanceCalendar,
  IAttendanceLog,
  IPageFactory,
  IQueryAttendanceLogParams,
} from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';

/**
 * 获取打卡记录列表（分页）
 */
export const queryAttendanceLogs = async (params: IQueryAttendanceLogParams) =>
  await request<IPageFactory<IAttendanceLog>>('/user-attendance-logs', {
    params,
  });

/**
 * 查询打卡详情
 */
export const queryAttendanceLogByTrainingId = async (
  training_id: string,
  user_id: string,
  attendance_date: string,
) => {
  const response = await request<IPageFactory<IAttendanceLog>>(
    `/user-attendance-logs`,
    {
      params: {
        page: 1,
        size: 1,
        training_id,
        user_id,
        attendance_date,
      },
    },
  );

  return response.rows.length > 0 ? response.rows[0] : undefined;
};

/**
 * 编辑审核状态
 */
export const updateAttendanceLogAuditState = async (
  id: string,
  audit_state: EAttendanceLogAuditState,
  audit_comment?: string,
) =>
  await request(`/user-attendance-logs/${id}/audit-state`, {
    method: 'PUT',
    data: { audit_state, audit_comment },
  });

/**
 * 查询打卡详情
 */
export const queryAttendanceLogDetail = async (id: string) => {
  return await request<IAttendanceLog>(`/user-attendance-logs/${id}`, {});
};

/**
 * 创建打卡
 */
export const createAttendanceLog = async (data: IAttendanceLog) => {
  return await request<IAttendanceLog>('/user-attendance-logs', {
    method: 'POST',
    data: { ...data },
  });
};

/**
 * 编辑打卡
 */
export const updateAttendanceLog = async ({ id, ...rest }: IAttendanceLog) => {
  return await request<IAttendanceLog>(`/user-attendance-logs/${id}`, {
    method: 'PUT',
    data: { ...rest },
  });
};

/** 获取用户打卡日历 */
export const queryUserAttendanceCalendar = async (
  training_id: string,
  user_id: string,
) => {
  return await request<IAttendanceCalendar>(`/analysis/attendances/calendar`, {
    params: {
      training_id,
      user_id,
    },
  });
};
