.happy-birthday {
  position: fixed;
  top: 0;
  background: #ffcc70;
  height: 120px;
  display: flex;
  gap: 10px;
  padding: 10px;
  align-items: center;
  justify-content: space-around;
  color: #fff;
  z-index: 1000;
  box-sizing: border-box;

  left: unset;
  width: 40px;
  right: 0;

  &--open {
    width: 100%;
    left: 0;
    right: unset;
    background: linear-gradient(90deg, #ffcc70 0%, #ff2525 0.01%, #ceb510 100%);
  }

  .text {
    font-size: 60px;
    letter-spacing: 30rpx;
    font-weight: bold;
    line-height: 120px;
    height: 120px;
  }
}
