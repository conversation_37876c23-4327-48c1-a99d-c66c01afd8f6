import { Card, Result, TrainingLayout, WeeklyWarning } from '@/components';
import { Contact } from '@/components/business/Contact';
import { useRouterParams } from '@/hooks';
import {
  createTrainingJoinIn,
  getTrainingDetail,
  getTrainingJoinInDetail,
  giveupTrainingJoinIn,
  updateTrainingJoinIn,
} from '@/service';
import {
  EBooleanString,
  ETrainingPaymentState,
  ETrainingProgress,
  ITraining,
  ITrainingMember,
  ITrainingTask,
  parseArrayJson,
  uuid,
} from '@nice-people/nice-21day-shared';
import { Button, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { AtButton, AtLoadMore, AtTextarea } from 'taro-ui';
import './index.scss';

const MAX_TASK_NUM = 5;

const JoinTraining: React.FC = () => {
  const { training_id: trainingId } = useRouterParams();

  const [queryTrainingLoading, setQueryTrainingLoading] =
    useState<boolean>(true);
  const [trainingDetail, setTrainingDetail] = useState<ITraining>();
  const [queryJoinInLoading, setQueryJoinInLoading] = useState<boolean>(true);
  const [joinInDetail, setJoinInDetail] = useState<ITrainingMember>();
  const [tasks, setTasks] = useState<ITrainingTask[]>([]);

  const querJoinInfo = useCallback(() => {
    setQueryJoinInLoading(true);
    getTrainingJoinInDetail(trainingId)
      .then(({ data }) => {
        setJoinInDetail(data);
        setTasks(parseArrayJson(data?.tasks));
      })
      .finally(() => {
        setQueryJoinInLoading(false);
      });
  }, [trainingId]);

  useEffect(() => {
    setQueryTrainingLoading(true);
    getTrainingDetail(trainingId)
      .then(({ data }) => {
        setTrainingDetail(data);
      })
      .finally(() => {
        setQueryTrainingLoading(false);
      });

    querJoinInfo();
  }, [trainingId]);

  const addTask = () => {
    if (tasks.length == MAX_TASK_NUM) {
      Taro.showToast({
        title: '子任务已达上限',
        icon: 'error',
        duration: 1000,
      });
    } else {
      setTasks((prevTasks) => [
        ...prevTasks,
        { id: uuid(), name: '', description: '' },
      ]);
    }
  };

  const deleteTask = (taskId: string) => {
    setTasks((prevTasks) => prevTasks.filter((item) => taskId !== item.id));
  };

  const changeTaskValue = (
    key: 'name' | 'description',
    value: string,
    taskId: string,
  ) => {
    setTasks((prevTasks) => {
      return prevTasks.map((task) => {
        if (task.id === taskId) {
          return { ...task, [key]: value };
        }
        return task;
      });
    });
  };

  const showConfirmModal = () => {
    const postData = {
      description: '',
      training_id: trainingId,
      tasks: JSON.stringify(tasks),
      score: 0,
      reached: EBooleanString.NO,
      payment_state:
        trainingDetail?.fee === 0
          ? ETrainingPaymentState.NotRequired
          : ETrainingPaymentState.Unpaid,
    };
    Taro.showModal({
      title: joinInDetail ? '确认修改报名信息吗？' : '确认提交报名吗？',
      success: function (res) {
        if (res.confirm) {
          (joinInDetail
            ? updateTrainingJoinIn(trainingId, joinInDetail.id, postData)
            : createTrainingJoinIn(trainingId, postData)
          ).then(() => {
            Taro.showToast({
              title: joinInDetail ? '修改成功' : '报名成功',
              icon: 'success',
              duration: 1000,
            });
            Taro.navigateBack();
          });
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      },
    });
  };
  const joinInTraining = useCallback(() => {
    if (tasks.length === 0) {
      Taro.showModal({
        title: '报名失败',
        content: '请先填写任务',
        showCancel: false,
        confirmText: '我知道了',
      });
    }
    const taskNames = tasks
      .map((task) => task.name.trim())
      .filter((name) => !!name);

    // 检查任务项名称是否都填写了
    if (taskNames.length !== tasks.length) {
      Taro.showModal({
        title: '报名失败',
        content: '子任务内容不能为空',
        showCancel: false,
        confirmText: '我知道了',
      });
      return;
    }
    // 判断是否存在重复的任务
    if (Array.from(new Set(taskNames)).length !== tasks.length) {
      Taro.showModal({
        title: '报名失败',
        content: '任务名称不可以重复',
        showCancel: false,
        confirmText: '我知道了',
      });
      return;
    }
    showConfirmModal();
  }, [trainingId, tasks, trainingDetail, joinInDetail]);

  const giveupJoinInTraining = useCallback(() => {
    Taro.showModal({
      title: '确认放弃参加此次训练营吗？',
      content: '山水相逢，后会有期',
      success: function (res) {
        if (res.confirm) {
          giveupTrainingJoinIn(trainingDetail.id, joinInDetail.id)
            .then(() => {
              Taro.showToast({
                title: '取消成功',
                icon: 'success',
                duration: 1000,
              });
              Taro.navigateBack();
            })
            .catch(() => {
              Taro.showToast({
                title: '取消失败',
                icon: 'error',
                duration: 1000,
              });
            });
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      },
    });
  }, [joinInDetail, trainingDetail]);

  // 处于报名阶段
  const isRegistering = useMemo(() => {
    return trainingDetail?.progress === ETrainingProgress.Registering;
  }, [trainingDetail?.progress]);

  const renderContent = useCallback(() => {
    if (!isRegistering) {
      return <Result text="训练营已经不在报名阶段" />;
    }
    return (
      <View className="join-training-page">
        <View className="mb-20px">
          <Contact showDesc />
        </View>

        {/* 周任务提醒 */}
        <WeeklyWarning />

        <View className="task-contnet">
          {tasks?.map((task, index) => {
            return (
              <Card
                key={task.id}
                title={`任务${index + 1}`}
                extra={
                  tasks.length > 1 && (
                    <View
                      className="at-icon at-icon-trash"
                      onClick={() => deleteTask(task.id)}
                    />
                  )
                }
              >
                <AtTextarea
                  value={task.name}
                  maxLength={200}
                  count={false}
                  placeholder="请输入你要培养的小习惯"
                  onChange={(value) => changeTaskValue('name', value, task.id)}
                />
              </Card>
            );
          })}
          <View className="add-task-btn" onClick={addTask}>
            <View className="at-icon at-icon-add" />
            添加任务
          </View>
        </View>
        <View className="task-action">
          {!joinInDetail && tasks.length > 0 && (
            <Button className="join-btn" onClick={joinInTraining}>
              参加报名
            </Button>
          )}
          {joinInDetail && (
            <View className="edit-wrapper">
              <AtButton
                circle
                type="primary"
                className="giveup-btn danger-button"
                onClick={giveupJoinInTraining}
              >
                放弃参与
              </AtButton>
              <AtButton
                circle
                type="primary"
                className="edit-btn"
                onClick={joinInTraining}
              >
                保存修改
              </AtButton>
            </View>
          )}
        </View>
      </View>
    );
  }, [isRegistering, JSON.stringify(tasks), joinInDetail]);

  if (queryJoinInLoading || queryTrainingLoading) {
    return <AtLoadMore status="loading" />;
  }

  if (!trainingDetail) {
    return <Result text="训练营不存在或已被删除" />;
  }

  return (
    <TrainingLayout training={trainingDetail} title="我的目标任务">
      {renderContent()}
    </TrainingLayout>
  );
};

export default JoinTraining;
