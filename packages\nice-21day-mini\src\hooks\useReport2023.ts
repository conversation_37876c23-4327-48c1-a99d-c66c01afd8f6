import { getUserReport2023 } from '@/service';
import { IUserReport2023 } from '@nice-people/nice-21day-shared';
import { useEffect, useState } from 'react';

const emptyData: any = {
  id: 'empty_data_id',
  statistics: {
    task_list: [],
    fans_count: 0,
    follow_count: 0,
    attendance_rate: 0,
    likes_sent_count: 0,
    task_total_count: 0,
    category_analysis: [
      { category: '个人成长', score: 0 },
      { category: '阅读', score: 0 },
      { category: '运动', score: 0 },
      { category: '写作', score: 0 },
      { category: '早起', score: 0 },
      { category: '冥想', score: 0 },
    ],
    training_joined_ids: [],
    likes_received_count: 0,
    training_joined_count: 0,
    attendance_audio_count: 0,
    attendance_image_count: 0,
    attendance_total_count: 0,
    attendance_words_count: 0,
    is_joined_all_training: false,
    training_standard_rete: 0,
    user_registration_date: '',
    training_standard_count: 0,
    attendance_preferred_time: '',
    addendance_time_distribution: [
      { count: 0, hours: '00' },
      { count: 0, hours: '01' },
      { count: 0, hours: '02' },
      { count: 0, hours: '03' },
      { count: 0, hours: '04' },
      { count: 0, hours: '05' },
      { count: 0, hours: '06' },
      { count: 0, hours: '07' },
      { count: 0, hours: '08' },
      { count: 0, hours: '09' },
      { count: 0, hours: '10' },
      { count: 0, hours: '11' },
      { count: 0, hours: '12' },
      { count: 0, hours: '13' },
      { count: 0, hours: '14' },
      { count: 0, hours: '15' },
      { count: 0, hours: '16' },
      { count: 0, hours: '17' },
      { count: 0, hours: '18' },
      { count: 0, hours: '19' },
      { count: 0, hours: '20' },
      { count: 0, hours: '21' },
      { count: 0, hours: '22' },
      { count: 0, hours: '23' },
    ],
    likes_received_topn_user_ids: [],
    attendance_count_at_preferred_time: 0,
    training_summary_words_total_count: 0,
  },
  global_statistics: {
    training_2023_list: [
      {
        description: '一个人可以走得更快，一群人可以走得更远',
        created_at: '2023-11-13T09:41:26.517Z',
        updated_at: '2023-12-23T04:15:10.453Z',
        id: '9a5f1364-2ddc-452b-93f1-4df903339e2b',
        name: '21天训练营 - 第21期',
        type: '21day',
        period: 21,
        start_time: '2023-11-20',
        end_time: '2023-12-10',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '一个人不想走的时候，试着让一群人带着你走走',
        created_at: '2023-10-12T05:40:08.791Z',
        updated_at: '2023-11-21T01:57:41.072Z',
        id: '80199e02-4ab5-48ac-b487-7f10e1162cc4',
        name: '21天训练营 - 第20期',
        type: '21day',
        period: 20,
        start_time: '2023-10-16',
        end_time: '2023-11-05',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '一个 21 天不能养成一个习惯，十个呢？',
        created_at: '2023-09-08T03:39:14.497Z',
        updated_at: '2023-11-13T09:41:36.961Z',
        id: 'c5fd583d-dd10-4fa5-84d0-4cc67efed7d6',
        name: '21天训练营 - 第19期',
        type: '21day',
        period: 19,
        start_time: '2023-09-11',
        end_time: '2023-10-01',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '一个人可以走的更快，一群人可以走的更远',
        created_at: '2023-08-03T01:26:05.741Z',
        updated_at: '2023-10-07T03:42:45.621Z',
        id: '9d44809e-77ec-4a16-9539-8ad1627c7723',
        name: '21天训练营 - 第18期',
        type: '21day',
        period: 18,
        start_time: '2023-08-07',
        end_time: '2023-08-27',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '一个人可以走的更快，一群人可以走的更远',
        created_at: '2023-06-25T03:41:59.181Z',
        updated_at: '2023-08-07T00:36:31.527Z',
        id: '83c8ece2-b72a-4a5d-916f-ebfd582326b2',
        name: '21天训练营 - 第17期',
        type: '21day',
        period: 17,
        start_time: '2023-07-03',
        end_time: '2023-07-23',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '',
        created_at: '2023-05-16T02:03:40.998Z',
        updated_at: '2023-06-25T03:40:57.819Z',
        id: '2031b770-f54a-499c-8368-290a03b8950f',
        name: '21天训练营 - 第16期',
        type: '21day',
        period: 16,
        start_time: '2023-05-22',
        end_time: '2023-06-11',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '一个人走的更快，一群人走的更远',
        created_at: '2023-04-21T04:14:02.777Z',
        updated_at: '2023-05-22T00:45:59.189Z',
        id: '44bb2bb4-e9d7-4aab-8135-7e7e45862f64',
        name: '21天训练营 - 第15期',
        type: '21day',
        period: 15,
        start_time: '2023-04-24',
        end_time: '2023-05-14',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
      {
        description: '一个人可以走得更快，但一群人可以走得更远。',
        created_at: '2023-03-11T12:18:30.842Z',
        updated_at: '2023-04-23T03:44:18.961Z',
        id: '3b3c3b40-bd8c-4f80-b0da-ad116d131399',
        name: '21天训练营 - 第14期',
        type: '21day',
        period: 14,
        start_time: '2023-03-20',
        end_time: '2023-04-09',
        state: 'enable',
        fee: 120,
        standard_score: 18,
        progress: 'finished',
      },
    ],
    training_2023_count: 8,
    training_join_user_count: 386,
    attendance_total_count: 7267,
    attendance_preferred_time_user_count_map: {
      '15': { user_count: 1, percent: 0 },
      '16': { user_count: 2, percent: 0.01 },
      '18': { user_count: 1, percent: 0 },
      '19': { user_count: 3, percent: 0.01 },
      '20': { user_count: 4, percent: 0.01 },
      '21': { user_count: 12, percent: 0.04 },
      '22': { user_count: 15, percent: 0.05 },
      '23': { user_count: 53, percent: 0.18 },
      '00': { user_count: 195, percent: 0.67 },
      '07': { user_count: 3, percent: 0.01 },
      '02': { user_count: 1, percent: 0 },
      '06': { user_count: 1, percent: 0 },
    },
  },
};
/**
 * 获取用户2023年度总结数据
 * @param userId 用户ID
 * @returns
 */
export const useReport2023Data = (userId: string) => {
  const [queryReport2023Loading, setQueryReport2023Loading] =
    useState<boolean>(false);
  const [report2023Data, setReport2023Data] = useState<IUserReport2023>(
    {} as IUserReport2023,
  );

  useEffect(() => {
    if (!userId) {
      return;
    }
    setQueryReport2023Loading(true);
    getUserReport2023(userId)
      .then(({ data }) => {
        if (!data.id) {
          setReport2023Data(emptyData);
          return;
        }
        setReport2023Data(data);
      })
      .finally(() => {
        setQueryReport2023Loading(false);
      });
  }, [userId]);

  return {
    report2023Data,
    queryReport2023Loading,
  };
};
