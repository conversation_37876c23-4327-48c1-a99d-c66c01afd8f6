import { AppContext } from '@/appContext';
import {
  AttendanceComments,
  AttendanceLike,
  AttendanceTask,
  Avatar,
  FabBox,
  TrainingLayout,
} from '@/components';
import { useRouterParams, useTraining } from '@/hooks';
import { queryUserAttendanceDetail } from '@/service';
import successIcon from '@/static/success.png';
import {
  EAttendanceState,
  IAttendanceLog,
  IAttendanceTask,
  isTodayAttendance,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
  AtButton,
  AtLoadMore,
  AtModal,
  AtModalAction,
  AtModalContent,
  AtModalHeader,
  AtNoticebar,
} from 'taro-ui';

/** 某一次打卡详情*/
const AttendanceDetail: React.FC = () => {
  const { training_id, attendance_id, from } = useRouterParams();
  const { currentUser } = useContext(AppContext);

  const { trainingDetail, queryTrainingLoading } = useTraining(training_id);

  const [attendanceDetail, setAttendanceDetail] = useState<IAttendanceLog>();
  const [shareModalVisible, setShareModalVisible] = useState<boolean>(false);
  const [queryAttendanceDetailLoading, setQueryAttendanceDetailLoading] =
    useState<boolean>(true);
  useEffect(() => {
    setQueryAttendanceDetailLoading(true);
    queryUserAttendanceDetail(attendance_id)
      .then(({ data }) => {
        setAttendanceDetail(data);
      })
      .finally(() => {
        setQueryAttendanceDetailLoading(false);
      });
  }, [attendance_id]);

  // 分享
  useShareAppMessage(() => {
    // 本人当天的分享
    const isMyTodayAttendance =
      isTodayAttendance(
        attendanceDetail?.attendance_date,
        attendanceDetail?.created_at,
      ) && attendanceDetail?.user_id === currentUser?.id;

    return {
      title: `${attendanceDetail?.user?.nick_name}的打卡`,
      path: `/pages/attendance-detail/index?training_id=${
        attendanceDetail?.training_id
      }&attendance_id=${attendanceDetail?.id}${
        isMyTodayAttendance ? '&from=my_today_attendance' : ''
      }`,
    };
  });

  useEffect(() => {
    if (from === 'attendance') {
      // Taro.showModal({
      //   title: '打卡成功',
      //   content: '点击页面下方的分享按钮，分享给好友吧~',
      //   showCancel: false,
      //   confirmText: '我知道了',
      // });
      setShareModalVisible(true);
    }
  }, [from]);

  /** 获取当前taskList 变量 */
  const attendanceTaskList = useMemo(
    () =>
      parseArrayJson<IAttendanceTask>(attendanceDetail?.attendance_tasks).map(
        (task) => ({ ...task, id: task.id }),
      ),
    [attendanceDetail?.attendance_tasks],
  );

  if (
    queryTrainingLoading ||
    queryAttendanceDetailLoading ||
    !attendanceDetail
  ) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <>
      <TrainingLayout
        training={trainingDetail}
        title={
          <>
            <View className="flex gap-2  items-center justify-center flex-col">
              <Avatar
                avatar={attendanceDetail.user?.avatar_url}
                userName={attendanceDetail.user?.nick_name}
                layout="horizontal"
                size="small"
                link
                onClick={() => {
                  const userId = attendanceDetail.user?.id;
                  if (userId) {
                    Taro.navigateTo({
                      url: `/pages/user-homepage/index?user_id=${userId}`,
                    });
                  }
                }}
              />
              <View>打卡日期：{attendanceDetail?.attendance_date}</View>
            </View>
          </>
        }
      >
        {/* 判断是否请假 */}
        {attendanceDetail.attendance_state === EAttendanceState.Leave && (
          <AtNoticebar className="mb-2">
            请假：{attendanceDetail.description || '今日请假'}
          </AtNoticebar>
        )}

        <View className="pb-14">
          {attendanceTaskList.map((item, idx) => (
            <AttendanceTask
              key={idx}
              readonly
              task={{
                ...item,
                attendance_content: item.attendance_content || '',
                attendance_files: item.attendance_files || [],
              }}
              taskIndex={idx}
            />
          ))}

          {/* 点赞列表 */}
          <AttendanceLike attendanceLogId={attendanceDetail.id} />

          {/* 评论 */}
          <AttendanceComments attendanceLogId={attendanceDetail.id} />
        </View>

        <FabBox>
          <View className="flex flex-row gap-4">
            <AtButton type="primary" className="w-half" circle openType="share">
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-share-2" />
                转发分享
              </View>
            </AtButton>
            <AtButton
              type="primary"
              className="w-half"
              circle
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/analytics/user-attendance-analysis/index?training_id=${training_id}&user_id=${attendanceDetail?.user_id}`,
                });
              }}
            >
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-calendar" />
                打卡统计
              </View>
            </AtButton>
          </View>
        </FabBox>
      </TrainingLayout>
      <AtModal isOpened={shareModalVisible} closeOnClickOverlay={false}>
        <AtModalHeader>打卡成功</AtModalHeader>
        <AtModalContent>
          <Image
            style={{
              width: '100px',
              height: '100px',
              margin: '0 auto 20px',
              display: 'block',
            }}
            src={successIcon}
          />
          <View className="text-center">点击分享按钮，分享给好友吧~</View>
        </AtModalContent>
        <AtModalAction>
          <AtButton
            type="primary"
            full
            openType="share"
            onClick={() => setShareModalVisible(false)}
          >
            <View className="flex items-center gap-1">
              <View className="at-icon at-icon-share-2" />
              转发分享
            </View>
          </AtButton>
        </AtModalAction>
      </AtModal>
    </>
  );
};

export default AttendanceDetail;
