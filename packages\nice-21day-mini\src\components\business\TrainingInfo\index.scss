.training-info {
  background-color: var(--default-color);
  padding: 0 20px 40px 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  // min-height: 318px;
  box-sizing: border-box;

  .training-title {
    color: #ffff;

    .name {
      font-size: 42px;
    }
  }

  .training-date {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: #46bb7e;
    border-radius: 4px;
    color: #fff;
    padding: 16px 42px;
    position: relative;

    &__progress {
      position: absolute;
      background: var(--progress-color);
      width: 100%;
      height: 4px;
      top: 0px;
      left: 0;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
    }

    .icon-wrap {
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .training-desc {
    color: #fff;
    text {
      display: block;
    }
    .label {
      font-size: 30px;
      margin-bottom: 24px;
    }
    .desc-text {
      font-size: 30px;
    }
  }
}
