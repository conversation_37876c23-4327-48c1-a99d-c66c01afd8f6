import { AppContext } from '@/appContext';
import { getCurrentUser } from '@/service';
import notFoundImg from '@/static/404.png';
import { validateWechatInfo } from '@/utils';
import request from '@/utils/request';
import { ICurrentUser } from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useContext, useState } from 'react';
import { AtButton } from 'taro-ui';
import UpdateWechatInfo from '../UpdateWechatInfo';
import styles from './index.module.scss';

function Login() {
  const { refreshCurrentUser } = useContext(AppContext);
  const [currentUser, setCurrentUser] = useState<ICurrentUser>(
    {} as ICurrentUser,
  );

  const [needUpdateWechatInfo, setNeedUpdateWechatInfo] =
    useState<boolean>(false);

  const reLaunchApp = () => {
    refreshCurrentUser?.();
    Taro.reLaunch({
      url: '/pages/index/index',
    });
  };

  const login = () => {
    Taro.getUserProfile({
      desc: '用于完善个人资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (profileRes) => {
        // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
        Taro.login({
          success: function (res) {
            if (res.code) {
              request
                .post('/auth/wechat/login', {
                  code: res.code,
                  nick_name: profileRes.userInfo?.nickName,
                  avatar_url: profileRes.userInfo?.avatarUrl,
                })
                .then(() => {
                  // 先判断一下当前登录人信息的头像、昵称是否是默认的值
                  getCurrentUser().then(({ data }) => {
                    setCurrentUser(data);

                    const needUpdate = !validateWechatInfo({
                      avatar: data.avatar || '',
                      nickName: data.nick_name || '',
                    });

                    setNeedUpdateWechatInfo(needUpdate);

                    if (!needUpdate) {
                      reLaunchApp();
                    }
                  });
                });
            } else {
              console.log('登录失败！' + res.errMsg);
            }
          },
        });
      },
    });
  };

  if (needUpdateWechatInfo) {
    return (
      <UpdateWechatInfo
        currentUser={currentUser}
        callback={() => {
          reLaunchApp();
        }}
      />
    );
  }

  return (
    <View className={styles.login}>
      <View className={styles.login__icon}>
        <Image style={{ width: '100%', height: '100%' }} src={notFoundImg} />
      </View>
      <View className={styles.login__desc}>你还没有登录，请登录</View>
      <View className={styles.login__action}>
        <AtButton size="small" type="primary" onClick={login}>
          登录
        </AtButton>
      </View>
    </View>
  );
}

export default Login;
