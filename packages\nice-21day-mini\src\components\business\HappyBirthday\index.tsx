import { AppContext } from '@/appContext';
import { BackgroundMusic } from '@/components/common';
import { Image, View } from '@tarojs/components';
import dayjs from 'dayjs';
import { useCallback, useContext, useState } from 'react';
import './index.scss';

// 生日快乐歌
const bgm = '/alioss/2023-12-11/f414c5ad-77a2-41e9-8284-ba250f13fdbc.mp3';

const HappyBirthday = () => {
  const { currentUser } = useContext(AppContext);
  const [wrapOpen, setWrapOpen] = useState(true);

  const isMyBirthday =
    (currentUser &&
      currentUser.birthday &&
      dayjs(currentUser.birthday).format('MM-DD') ===
        dayjs().format('MM-DD')) ||
    false;

  const handleToogleOpen = useCallback(() => {
    setWrapOpen((prev) => !prev);
  }, [wrapOpen]);

  // 如果不是自己的生日
  if (!isMyBirthday) {
    return null;
  }

  return (
    <View
      className={`happy-birthday ${wrapOpen ? 'happy-birthday--open' : ''}`}
      onClick={handleToogleOpen}
    >
      {wrapOpen ? (
        <>
          <View>
            <Image
              src={require('./cake.svg')}
              className="w-[100px] h-[120px]"
            />
          </View>
          <View className="text">生日快乐</View>
          <BackgroundMusic bgmUrl={bgm} autoplay={isMyBirthday} />
          <View className="action">
            收起
            <View className="at-icon at-icon-chevron-right" />
          </View>
        </>
      ) : (
        <View className="at-icon at-icon-chevron-left" />
      )}
    </View>
  );
};

export default HappyBirthday;
