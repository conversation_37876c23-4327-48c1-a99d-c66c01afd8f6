import { AppContext } from '@/appContext';
import { FabBox } from '@/components/common';
import Taro from '@tarojs/taro';
import { useContext } from 'react';
import { AtButton } from 'taro-ui';

const LoginGuide = () => {
  const { currentUser } = useContext(AppContext);

  if (currentUser?.id) {
    return null;
  }

  return (
    <FabBox>
      <AtButton
        type="primary"
        circle
        onClick={() => {
          Taro.navigateTo({
            url: '/pages/login/index',
          });
        }}
      >
        登录解锁更多功能
      </AtButton>
    </FabBox>
  );
};

export default LoginGuide;
