import { AppContext } from '@/appContext';
import { queryUserAttendanceCalendar } from '@/service';
import { getNowDate } from '@/utils';
import {
  EAttendanceState,
  IAttendanceCalendar,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Calendar from 'custom-calendar-taro';
import dayjs from 'dayjs';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { AtActionSheet, AtActionSheetItem, AtToast } from 'taro-ui';

const currnentDate = dayjs().format('YYYY-MM-DD');

interface IToastMsg {
  isOpen: boolean;
  msg: string;
  icon: string;
}

const AttendanceLogs = () => {
  const { training_id } = Taro.getCurrentInstance().router.params;
  const { currentUser } = useContext(AppContext);

  const [calendarData, setCalendarData] = useState<IAttendanceCalendar>();

  const [showToastMsg, setShowToastMsg] = useState<IToastMsg>({
    msg: '',
    isOpen: false,
    icon: 'error',
  });

  // 选择的日期
  const [selectedDate, setSelectedDate] = useState<string>();

  const attendanceLog = useMemo(() => {
    if (!selectedDate || !calendarData?.attendanceLogs) {
      return null;
    }
    return (calendarData?.attendanceLogs ?? []).find(
      (el) => el.attendance_date === selectedDate,
    );
  }, [selectedDate, calendarData?.attendanceLogs]);

  useEffect(() => {
    queryUserAttendanceCalendar(training_id, currentUser.id).then(
      ({ data }) => {
        setCalendarData(data);
      },
    );
  }, [training_id]);

  const extraInfo = useMemo(() => {
    return (calendarData?.attendanceLogs || []).map((log) => {
      // 打卡
      if (log.attendance_state === EAttendanceState.Attendance) {
        // 判断是否是补卡
        const isReissue =
          dayjs(log.created_at).format('YYYY-MM-DD') === log.attendance_date;
        return {
          value: log.attendance_date,
          text: isReissue ? '打卡' : '补卡',
          color: isReissue ? 'green' : '#ff9800',
        };
      }
      return { value: log.attendance_date, text: '请假', color: 'red' };
    });
  }, [calendarData?.attendanceLogs]);

  const handleDayClick = useCallback(
    (date: string) => {
      // 未来的日期不需要操作
      if (dayjs(date).isAfter(dayjs())) {
        return;
      }
      // 选择的日期在今天2天之前，可以补卡
      if (
        dayjs(date).isBefore(dayjs()) &&
        dayjs().diff(dayjs(date), 'days') <= 2
      ) {
        setSelectedDate(date);
      }
    },
    [calendarData?.training, calendarData?.attendanceLogs],
  );
  /** 编辑打卡 */
  const handleEditAttence = () => {
    Taro.navigateTo({
      url: `/pages/attendance/index?training_id=${training_id}&date=${selectedDate}`,
    });
  };

  /** 点击打卡详情 */
  const handleAttenceDesc = () => {
    // Taro.navigateTo({
    //   url: `/pages/attendance-detail/index?training_id=${training_id}&date=${selectedDate}`,
    // });
  };

  return (
    <View style={{ padding: 20 }}>
      <AtToast
        isOpened={showToastMsg.isOpen}
        text={showToastMsg.msg}
        icon={showToastMsg.icon}
        onClose={() => {
          setShowToastMsg({
            msg: '',
            isOpen: false,
            icon: 'close',
          });
        }}
      ></AtToast>
      {/* @ts-ignore */}
      <Calendar
        selectedDate={calendarData?.training?.start_time}
        minDate={calendarData?.training?.start_time}
        maxDate={calendarData?.training?.end_time}
        customStyleGenerator={() => ({
          extraInfoStyle: {
            textAlign: 'left',
            paddingLeft: '0.5rem',
          },
        })}
        extraInfo={extraInfo}
        selectedDateColor="#346fc2"
        onDayClick={(item) => handleDayClick(item.value)}
      />

      <AtActionSheet
        cancelText="取消"
        onCancel={() => setSelectedDate(undefined)}
        onClose={() => setSelectedDate(undefined)}
        isOpened={!!selectedDate}
      >
        {!attendanceLog && (
          <>
            {selectedDate === currnentDate ? (
              <AtActionSheetItem
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/attendance/index?training_id=${training_id}&date=${selectedDate}`,
                  });
                }}
              >
                去打卡
              </AtActionSheetItem>
            ) : (
              <AtActionSheetItem
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/attendance/index?training_id=${training_id}&date=${selectedDate}`,
                  });
                }}
              >
                去补卡
              </AtActionSheetItem>
            )}
          </>
        )}
        {/* 只有当天可以编辑打卡 */}
        {attendanceLog && selectedDate === getNowDate() && (
          <AtActionSheetItem
            onClick={() => {
              handleEditAttence();
              setSelectedDate(undefined);
            }}
          >
            编辑打卡
          </AtActionSheetItem>
        )}
        {attendanceLog && (
          <AtActionSheetItem
            onClick={() => {
              handleAttenceDesc();
              setSelectedDate(undefined);
            }}
          >
            打卡详情
          </AtActionSheetItem>
        )}
      </AtActionSheet>
    </View>
  );
};

export default AttendanceLogs;
