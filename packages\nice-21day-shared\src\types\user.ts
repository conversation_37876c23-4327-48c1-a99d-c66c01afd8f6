import { EBooleanString, EState, ICommonFields, IPageParams } from './global';

/** 用户恋爱状态 */
export enum EUserLoveState {
  /** 单身Solo */
  Single = 'single',
  /** 怦然心动 */
  Crush = 'crush',
  /** 暧昧拉扯 */
  Ambiguous = 'ambiguous',
  /** 坠入爱河 */
  FallInLove = 'fall_in_love',
  /** 结婚生子 */
  Married = 'married',
  /** 一言难尽 */
  Other = 'other',
}

/** 用户恋爱状态映射关系 */
export const USER_LOVE_STATE_MAPPING: Record<EUserLoveState, string> = {
  [EUserLoveState.Single]: '单身Solo',
  [EUserLoveState.Crush]: '怦然心动',
  [EUserLoveState.Ambiguous]: '暧昧拉扯',
  [EUserLoveState.FallInLove]: '坠入爱河',
  [EUserLoveState.Married]: '结婚生子',
  [EUserLoveState.Other]: '一言难尽',
};

/** 性别 */
export enum EUserGender {
  /** 不愿意暴露性别 */
  Other = 'none',
  /** 男 */
  Male = 'male',
  /** 女 */
  Female = 'female',
}

/** 性别映射关系 */
export const USER_GENDER_MAPPING: Record<EUserGender, string> = {
  [EUserGender.Other]: '不好说',
  [EUserGender.Male]: '男',
  [EUserGender.Female]: '女',
};
export interface IWechatUser {
  avatar_url: string;
  nick_name: string;
}
/** 注册用户 */
export interface IUser extends ICommonFields {
  /** 微信 openid */
  wechat_openid: string;
  /** 昵称 */
  nick_name: string;
  /** 头像 */
  avatar_url: string;
  /** 启用标识 */
  state: EState;
  /** 性别 */
  gender: EUserGender;
  /** 兴趣爱好(半角逗号分割) */
  hobby: string;
  /** 个人介绍 */
  about_me: string;
  /** 个人介绍图片。格式: 半角逗号分割 */
  about_me_files: string;
  /** 居住地。格式: 省份-城市-区县 */
  location: string;
  /** 生日。格式 YYYY-MM-DD */
  birthday: string;

  /** 公司 */
  company: string;
  /** 职称 */
  job_title: string;
  /** 技能标签。格式: 半角逗号分割 */
  skill_tags: string;

  /** 个人网站 */
  websites: string;
  /** 微信公众号名称 */
  mp_weixin_name: string;
  /** Github个人主页 */
  github_address: string;
  /** 掘金个人主页 */
  juejin_address: string;
  /** 知乎个人主页 */
  zhihu_address: string;
  /** 思否个人主页 */
  segmentfault_address: string;

  /** 是否提供内推。0-否, 1-是 */
  internal_referral_state: EBooleanString;
  /** 内推公司。格式: 半角逗号分割 */
  internal_referral_companies: string;
  /** 内推描述 */
  internal_referral_description: string;

  /** 恋爱状态 */
  love_state: EUserLoveState;
  /** 恋爱期望(恋爱宣言) */
  love_expectation: string;

  /** 关注数量 */
  followee_count: number;
  /** 粉丝数量 */
  follower_count: number;

  /** 授权 token */
  access_token: string;
}

/**
 * 注册用户查询参数
 */
export interface IQueryUserParams
  extends IPageParams,
    Partial<
      Pick<
        IUser,
        | 'nick_name'
        | 'state'
        | 'birthday'
        | 'gender'
        | 'company'
        | 'internal_referral_state'
        | 'internal_referral_companies'
        | 'love_state'
        | 'location'
      >
    > {}
