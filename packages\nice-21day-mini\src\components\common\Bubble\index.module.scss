.bubble {
  position: absolute;
  border-radius: 50%;
  border: 6px solid #fff;
  box-shadow: inset 0 0 8px #fff;
  animation: flutter 6s infinite;
  opacity: 0;
}

@keyframes flutter {
  0% {
    transform: translateX(0px);
    bottom: -30px;
    opacity: 1;
  }

  50% {
    transform: translateX(10px);
    opacity: 0.5;
  }

  100% {
    transform: translateX(0px);
    bottom: 100%;
    opacity: 0;
  }
}
