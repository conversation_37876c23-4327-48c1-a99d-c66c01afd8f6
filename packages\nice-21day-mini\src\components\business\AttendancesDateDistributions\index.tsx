import { useTrainingDateDistribution } from '@/hooks';
import { Axis, Chart, Interval, Tooltip } from '@antv/f2';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import React, { CSSProperties } from 'react';
import F2Canvas from 'taro-f2-react';
import { AtLoadMore } from 'taro-ui';

interface IAttendancesDateDistributionsProps {
  trainingId: string;
  height?: number;
  style?: CSSProperties;
}
const AttendancesDateDistributions: React.FC<
  IAttendancesDateDistributionsProps
> = ({ trainingId, height = 200 }) => {
  const { loading, dateDistributions } =
    useTrainingDateDistribution(trainingId);

  return (
    <View style={{ width: '100%', height }}>
      {loading ? (
        <AtLoadMore status="loading" />
      ) : (
        <F2Canvas id="attendances-date-distributions">
          <Chart data={dateDistributions}>
            <Tooltip />
            <Axis
              field="date"
              formatter={(val: string) => dayjs(val).format('MM/DD')}
              tickCount={6}
            />
            {/* TODO: 取整数 */}
            <Axis field="count" type="linear" />
            <Interval x="date" y="count" />
          </Chart>
        </F2Canvas>
      )}
    </View>
  );
};

export default AttendancesDateDistributions;
