import { Result } from '@/components';
import { useRouterParams } from '@/hooks';
import { queryUserFollowers } from '@/service/follow';
import { getFileUrl, IUser } from '@nice-people/nice-21day-shared';
import Taro from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import { AtList, AtListItem, AtLoadMore } from 'taro-ui';

const UserFollowers: React.FC = () => {
  const { user_id } = useRouterParams();
  const [loading, setLoading] = useState<boolean>(true);
  const [users, setUsers] = useState<IUser[]>([]);

  useEffect(() => {
    setLoading(true);
    queryUserFollowers(user_id)
      .then(({ data }) => {
        setUsers(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [user_id]);

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  if (users.length === 0) {
    return <Result text="这位用户还没有粉丝呢" />;
  }

  return (
    <AtList>
      {users.map((user) => (
        <AtListItem
          key={user.id}
          title={user.nick_name}
          thumb={getFileUrl(user.avatar_url)}
          arrow="right"
          onClick={() => {
            Taro.navigateTo({
              url: `/pages/user-homepage/index?user_id=${user.id}`,
            });
          }}
        />
      ))}
    </AtList>
  );
};

export default UserFollowers;
