import { getRandomIntInclusive } from '@/utils';
import { View } from '@tarojs/components';
import { memo, useEffect, useState } from 'react';

interface IProps {
  data: string[][];
  /** 切换时间，ms */
  ms?: number;
}

const Quotation = ({ data, ms = 3000 }: IProps) => {
  const [idx, setIdx] = useState<number>(() =>
    getRandomIntInclusive(0, data.length - 1),
  );

  useEffect(() => {
    const timer = setInterval(() => {
      setIdx(getRandomIntInclusive(0, data.length - 1));
    }, ms);
    return () => clearInterval(timer);
  }, [idx, data, ms]);

  return (
    <>
      {data[idx].map((item) => (
        <View key={item}>{item}</View>
      ))}
    </>
  );
};

export default memo(Quotation);
