import { getFileUrl, ITraining, IUser } from '@nice-people/nice-21day-shared';
import templateImage from '../../assets/13-1.jpg';
import part2 from '../../assets/13-2.png';
import part3 from '../../assets/13-3.png';
import part4 from '../../assets/13-4.png';
import part5 from '../../assets/13-5.png';
import fakeUserAvatar from '../../assets/fake_user_avatar.jpeg';
import styles from './index.less';

type MetricKey =
  /** 打卡天数 */
  | 'clockDays'
  /** 任务总数 */
  | 'taskCount'
  /** 总积分 */
  | 'totalPoints';
export type Metric = Record<MetricKey, string | number>;

export const mockCertificateData: ICertificatePreviewProps = {
  training: {
    name: '21天训练营',
    period: 1,
  },
  user: {
    avatar_url: fakeUserAvatar,
    nick_name: '跟着嘟嘟可去上学',
  },
  metrics: {
    clockDays: 10,
    taskCount: 3,
    totalPoints: 20,
  },
};

const metrics: {
  value: MetricKey;
  [key: string]: any;
}[] = [
  {
    value: 'clockDays',
    part: part3,
    partStyle: styles.part3,
  },
  {
    value: 'taskCount',
    part: part4,
    partStyle: styles.part4,
  },
  {
    value: 'totalPoints',
    part: part5,
    partStyle: styles.part5,
  },
];

export interface ICertificatePreviewProps {
  training?: Pick<ITraining, 'name' | 'period'>;
  user: Pick<IUser, 'nick_name' | 'avatar_url'>;
  metrics: Metric;
}

const CertificatePreview = (props: ICertificatePreviewProps) => {
  const { user = mockCertificateData.user } = props;

  return (
    <div className={styles.template} id="template">
      {/* 模板图片 */}
      <img src={templateImage} />
      {/* 标题 */}

      {/* 学员信息 */}
      <div className={styles.user}>
        <div className={styles.user__avatar}>
          <img
            src={user.avatar_url && getFileUrl(user.avatar_url)}
            className={styles.avator}
          />
          <img src={part2} className={styles.part2} alt="" />
        </div>
        <div className={styles.user__name}>{user.nick_name}</div>
      </div>
      {/* 打卡指标 */}
      <div className={styles.result}>
        {metrics.map((metric, index) => (
          <div key={index} className={styles['resultmetric']}>
            <div className={styles['resultvalue']}>
              {props.metrics[metric.value] || 0}
            </div>
            <img src={metric.part} className={metric.partStyle} alt="" />
          </div>
        ))}
      </div>
    </div>
  );
};

export default CertificatePreview;
