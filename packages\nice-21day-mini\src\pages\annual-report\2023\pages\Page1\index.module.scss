.container {
  background: url('https://oss.yayujs.com/alioss/2023-12-24/fc954a31-c880-46f7-8f17-ca37e295f3cb.png');
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: bottom;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;

  .title {
    font-family: Microsoft YaHei;
    font-size: 80px;
    color: #fff;
    font-weight: 700;
    height: 120px;
    line-height: 120px;
  }

  .year {
    font-family: DINPro-Medium;
    font-size: 216px;
    height: 220px;
    line-height: 220px;
    font-style: normal;
    font-weight: 700;
    background: linear-gradient(90deg, #fffefb 0%, #ffef94 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .desc {
    color: #fff;
    font-family: Microsoft YaHei;
    font-size: 96px;
    font-style: normal;
    font-weight: 700;
    line-height: 120px;
  }
}

.enterBtn {
  width: 380px;
  text-align: center;
  color: #fff;
  font-size: 48px;
  font-weight: 600;
  height: 100px;
  line-height: 100px;
  border-radius: 100px;
  border: 1px solid #fff;
  background: linear-gradient(
    90deg,
    rgba(69, 179, 255, 0.8) 0.08%,
    rgba(182, 243, 255, 0.8) 99.91%
  );
  backdrop-filter: blur(7.389162063598633px);
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
}

.iconAirplane {
  position: absolute;
  z-index: 20;
  animation: airplane-fly 4s linear infinite;
}

@keyframes airplane-fly {
  0% {
    top: 500px;
    left: -200px;
  }

  100% {
    top: 200px;
    left: 100%;
  }
}

.iconYear {
  position: absolute;
  left: 490px;
  top: 480px;
  z-index: 20;
}

.iconEnvelope {
  position: absolute;
  right: -46px;
  bottom: 150px;
}

.userName {
  position: absolute;
  top: 20px;
  width: 100%;
  text-align: center;
  color: #fff;
}

.iconCircle1 {
  position: absolute;
  right: -82px;
  bottom: -42px;
}

.iconCircle2 {
  position: absolute;
  left: -42px;
  bottom: 506px;
}

.iconCircle3 {
  position: absolute;
  top: 120px;
  right: 260px;
}
