import { LeetcodeQuestion } from '@/components';
import { LEETCODE_QUESTIONS_KEY } from '@/constant';
import {
  getISOWeekNumber,
  getLeetcodeFrontendQuestionIds,
  getLocalLeetcodeQuestion,
} from '@/utils';
import { Picker, PickerSelectorProps, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import {
  CSSProperties,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { AtCard, AtLoadMore, AtTag } from 'taro-ui';
import styles from './index.module.scss';
import { ILeetcodeWeekly, LeetcodeQuestionMap } from './typings';

const LEETCODE_API_PREFIX =
  'https://nice-people-frontend-community.github.io/nice-leetcode';

// 当前天
const currentDate = dayjs().format('YYYY-MM-DD');
// 当天所在的周数
const currentWeek = getISOWeekNumber(currentDate);
// 当前年份
const currentYear = dayjs().format('YYYY');

// 获取周选择的菜单
const getWeekList = () => {
  const selectList: {
    year: number;
    week: number;
    startDate: string;
    endDate: string;
  }[] = [];

  for (let i = 2022; i <= +currentYear; i++) {
    const yearStartDate = `${currentYear}-01-01`;
    // 当前年有几周
    const isoWeeks = dayjs(yearStartDate).isoWeeksInYear();

    const yearStartDateWeek = dayjs(yearStartDate).isoWeek();
    // 第一天是否归属于第一周
    const isFirstWeek = yearStartDateWeek === 1;

    for (let j = 1; j <= isoWeeks; j++) {
      // 2022年从第19周开始
      if (i === 2022 && j < 19) {
        continue;
      }
      // 超过当天所在的周数，直接跳出
      if (+currentYear === i && j > currentWeek) {
        break;
      }

      selectList.push({
        year: i,
        week: j,
        startDate: dayjs(yearStartDate)
          .startOf('isoWeek')
          .add(j - (isFirstWeek ? 1 : 0), 'week')
          .format('YYYY-MM-DD'),
        endDate: dayjs(yearStartDate)
          .endOf('isoWeek')
          .add(j - (isFirstWeek ? 1 : 0), 'week')
          .format('YYYY-MM-DD'),
      });
    }
  }

  return selectList;
};
const weekList = getWeekList();
const weekSelectOptons = weekList.reverse().map((el) => ({
  value: `${el.year}-${el.week}`,
  key: `${el.year}-${el.week}`,
  label: `${el.year}年第${el.week}周`,
}));

// 获取周报文件名称
const getWeekRollupFileName = (yearWeek: string) => {
  const [year, week] = yearWeek.split('-');
  const weekInfo = weekList.find(
    (item) => item.year === +year && item.week === +week,
  );

  const weekText = `${year}年第${week}周`;
  const dateRangeText = `${weekInfo?.startDate}_${weekInfo?.endDate}`;
  return {
    weekText,
    dateRangeText,
    fileName: `${weekText}(${dateRangeText})`,
  };
};

const LeetcodeWeekly: React.FC = () => {
  const [allQuestionMap, setAllQuestionMap] = useState(() =>
    getLocalLeetcodeQuestion(),
  );
  // 形式为 2022-33
  // 表示2022年第33周
  const [yearWeek, setYearWeek] = useState<string>(
    `${currentYear}-${currentWeek}`,
  );
  const [weeklyData, setWeeklyData] = useState<ILeetcodeWeekly>();
  const [loading, setLoading] = useState(true);

  const weekInfo = useMemo(() => {
    return getWeekRollupFileName(yearWeek);
  }, [yearWeek]);

  const pickerIndex = useMemo(() => {
    return weekSelectOptons.findIndex((el) => el.key === yearWeek);
  }, [weekSelectOptons, yearWeek]);

  useEffect(() => {
    (async () => {
      // 如果本地没有缓存就取一下缓存
      if (!allQuestionMap || Object.keys(allQuestionMap).length == 0) {
        const allQuesitonRes = await Taro.request<LeetcodeQuestionMap>({
          url: `${LEETCODE_API_PREFIX}/data/common/all_questions_map.json`,
        });
        if (allQuesitonRes.statusCode == 200) {
          Taro.setStorage({
            key: LEETCODE_QUESTIONS_KEY,
            data: JSON.stringify(allQuesitonRes.data),
          });
          setAllQuestionMap(allQuesitonRes.data);
        }
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      setLoading(true);
      const weeklyRes = await Taro.request<ILeetcodeWeekly>({
        url: `${LEETCODE_API_PREFIX}/data/weeks/${
          weekInfo.fileName
        }.json?v=${+new Date()}`,
      });
      if (weeklyRes.statusCode === 200) {
        setWeeklyData(weeklyRes.data);
      }
      setLoading(false);
    })();
  }, [weekInfo]);

  /** 计算题目难度分布占比 */
  const computedLevelRatioStyle = useCallback(
    (questionIdsText: string): CSSProperties => {
      const weeklyQuestionIds = getLeetcodeFrontendQuestionIds(questionIdsText);
      const total = weeklyQuestionIds.length;
      // 遍历题目，获取题目的难题程度
      let easyCount = 0;
      let mediumCount = 0;
      let hardCount = 0;
      let otherCount = 0;
      weeklyQuestionIds.forEach((id) => {
        const question = allQuestionMap[id];
        if (!question) {
          otherCount++;
        } else if (question.level === 1) {
          easyCount++;
        } else if (question.level === 2) {
          mediumCount++;
        } else if (question.level === 3) {
          hardCount++;
        }
      });

      const easyRatio = (easyCount / total) * 100;
      const mediumRatio = (mediumCount / total) * 100;
      const hardRatio = (hardCount / total) * 100;
      const otherRatio = (otherCount / total) * 100;

      return {
        height: 10,
        marginBottom: 10,
        borderRadius: 5,
        background: `linear-gradient(to right, var(--q-easy-color) 0, var(--q-easy-color) ${easyRatio}%, var(--q-medium-color) 0, var(--q-medium-color) ${
          easyRatio + mediumRatio
        }%, var(--q-hard-color) 0, var(--q-hard-color) ${
          easyRatio + mediumRatio + hardRatio
        }%, var(--q-other-color) 0, var(--q-other-color))`,
      };
    },
    [allQuestionMap],
  );

  const handleWeekchange: PickerSelectorProps['onChange'] = (e) => {
    console.log(e);
    const index = +e.detail.value;
    setYearWeek(weekSelectOptons[index]?.key);
  };

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <View>
      <View className={styles.header}>
        <Picker
          mode="selector"
          value={pickerIndex}
          range={weekSelectOptons}
          rangeKey="label"
          onChange={handleWeekchange}
        >
          <View>{weekInfo.weekText}</View>
        </Picker>
        <View>{weekInfo.dateRangeText}</View>
      </View>
      {weeklyData?.records.map((record) => (
        <AtCard
          key={record.userId}
          title={record.userName}
          extra={`${record.newQuestionsTotal}（#${record.ranking}）`}
          className={styles.card}
        >
          <View style={computedLevelRatioStyle(record.weekly.join(''))}></View>
          {weeklyData.weekly.map(
            (date, index) =>
              record.weekly[index] && (
                <View
                  key={record.userId + '_' + date}
                  className={styles.card__row}
                >
                  <AtTag type="primary" circle>
                    {date}
                  </AtTag>
                  <View style={{ margin: '6px 0' }}>
                    {getLeetcodeFrontendQuestionIds(record.weekly[index]).map(
                      (questionId) => (
                        <LeetcodeQuestion
                          key={record.userId + '_' + date + '_' + questionId}
                          questionId={questionId}
                          question={allQuestionMap[questionId]}
                        />
                      ),
                    )}
                  </View>
                </View>
              ),
          )}
        </AtCard>
      ))}
    </View>
  );
};

export default LeetcodeWeekly;
