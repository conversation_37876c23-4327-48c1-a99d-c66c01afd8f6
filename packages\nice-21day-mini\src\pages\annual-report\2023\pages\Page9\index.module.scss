.container {
  background-image: url('./assets/background.svg');
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
}

.titleContainer {
  width: 80%;
  margin-top: 110px;
  color: #0f59a1;
  margin-bottom: 50px;

  .title {
    font-size: 60px;
    font-weight: bold;
  }

  .subTitle {
    font-size: 40px;
    font-weight: 500;
    margin-top: 20px;
  }
}

.inputContainer {
  $height: 130px;

  position: relative;
  height: $height;
  width: 80%;
  font-size: 42px;
  font-weight: bold;
  text-align: center;
  color: #fff;

  .input {
    border-radius: 66px;
    height: $height;
    border: 0.478px solid #dccbff;
    background: linear-gradient(
      90deg,
      rgba(222, 197, 255, 0.8) 0.08%,
      rgba(72, 145, 255, 0.8) 99.91%
    );
    box-shadow: 0px 0.478px 0px 0px rgba(255, 255, 255, 0.25),
      0px 0px 4.776px 0px rgba(141, 141, 141, 0.16) inset,
      -1.91px 4.776px 0px 0px rgba(255, 255, 255, 0.16) inset,
      1.91px 0px 1.91px 0px rgba(69, 68, 68, 0.16) inset;
    backdrop-filter: blur(5.731342315673828px);
    margin-left: 0;
    padding: 0;
  }

  .refresh {
    position: absolute;
    height: 100%;
    width: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    right: 0rpx;
    top: 50%;
    transform: translateY(-50%);
  }

  :global {
    .at-input__container {
      height: 100%;
    }
    .at-input__input {
      color: #fff;
      height: 100%;
      font-size: 42px !important;
    }
    .at-input__input::placeholder {
      color: #fff !important;
      font-size: 42px !important;
    }
  }

  &::before {
    content: '';
    position: absolute;
    width: calc(100% - 4px);
    height: 100%;
    border-radius: 66px;
    left: 4px;
    top: 16px;

    background: linear-gradient(
      90deg,
      #8730cb 0.08%,
      #a448ed 8.5%,
      #c266fa 83.1%,
      #b9a0ff 92.67%,
      #e09eff 99.91%
    );
  }
}

:global {
  .keyword-btn-text {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    color: #fff;

    .keyword-text {
      position: absolute;
      transform: translateY(-5px);
    }
  }
}
