import { queryAttendanceLogs } from '@/services';
import { EBooleanString } from '@nice-people/nice-21day-shared';
import { useRequest } from 'ahooks';
import { useContext, useEffect, useRef, useState } from 'react';
import AttendanceTimeline from '../../components/AttendanceTimeline';
import { TrainingLayoutContext } from '../Layout';

export default () => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const { training } = useContext(TrainingLayoutContext);

  const [initLoading, setInitLoading] = useState(true);

  const {
    loading = true,
    data,
    run,
  } = useRequest(queryAttendanceLogs, {
    manual: true,
    onFinally: () => {
      setInitLoading(false);
    },
  });

  useEffect(() => {
    run({
      training_id: training.id,
      with_user: EBooleanString.YES,
      page: 1,
      size: 50,
    });
  }, [training?.id]);

  return (
    <div ref={containerRef}>
      <AttendanceTimeline
        training={training}
        scene="training-detail"
        loading={loading || initLoading}
        list={data?.rows || []}
        pagination={{
          current: data?.page || 1,
          total: data?.total || 0,
          pageSize: 50,
          showSizeChanger: false,
          hideOnSinglePage: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page) => {
            run({
              training_id: training.id,
              with_user: EBooleanString.YES,
              page: page,
              size: 50,
            });

            containerRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            });
          },
        }}
      />
    </div>
  );
};
