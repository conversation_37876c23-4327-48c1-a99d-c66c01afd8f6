import { useCurrentUser } from '@/hooks/useCurrentUser';
import { queryTrainings } from '@/services';
import { useRequest } from 'ahooks';
import Banner from '../components/Banner';
import Training, { TrainingSkeleton } from '../components/Training';

// 训练营详情
export default () => {
  const { currentUser } = useCurrentUser();

  const { loading, data } = useRequest(queryTrainings, {
    refreshDeps: [currentUser.id],
    defaultParams: [{ page: 1, size: 100 }],
  });

  return (
    <div className="px-4 space-y-3 pb-3">
      {/* 加入更多训练营 */}
      <Banner />

      {loading ? (
        <div className="space-y-3">
          <TrainingSkeleton />
          <TrainingSkeleton />
          <TrainingSkeleton />
        </div>
      ) : (
        <>
          {(data?.rows || []).map((row) => (
            <Training key={row.id} training={row} />
          ))}
        </>
      )}
    </div>
  );
};
