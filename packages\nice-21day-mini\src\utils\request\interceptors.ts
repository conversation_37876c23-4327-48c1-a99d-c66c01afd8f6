import { ACCESS_TOKEN_LOCAL_KEY } from '@/constant';
import Taro, { Chain } from '@tarojs/taro';

const customInterceptor = (chain: Chain) => {
  const requestParams = chain.requestParams;
  //请求添加 loading
  Taro.showLoading({
    title: '加载中...',
  });

  return chain
    .proceed(requestParams)
    .then((res: any) => {
      Taro.hideLoading();
      // 未登录
      if (res.statusCode === 401) {
        Taro.setStorageSync(ACCESS_TOKEN_LOCAL_KEY, '');
        return Promise.reject(res);
      }
      // 错误
      if (
        res.statusCode === 400 ||
        res.statusCode === 403 ||
        (res.statusCode >= 500 && res.statusCode <= 504) ||
        (res.statusCode >= 404 && res.statusCode < 422)
      ) {
        return Promise.reject(res);
      }
      // 成功
      // 如果是登录接口
      if (requestParams.url.includes('/wechat/login')) {
        Taro.setStorageSync(
          ACCESS_TOKEN_LOCAL_KEY,
          `${res.data?.access_token}`,
        );
        return;
      }
      return res;
    })
    .catch((error: any) => {
      Taro.hideLoading();

      const statusCode = error?.data?.statusCode;
      console.log('statusCode', statusCode);
      let errMsg = error?.data?.message ?? '请求错误';
      if (statusCode === 401) {
        errMsg = '未登录';

        if (error?.data) {
          error.data.message = errMsg;
        }
      }

      if (!requestParams.config?.hideError) {
        Taro.showToast({
          icon: 'error',
          title: errMsg,
          duration: 2000,
        });
      }
      return Promise.reject(error);
    });
};
const interceptors = [customInterceptor];

export default interceptors;
