import WeAppQrcode from '@/assets/weappcode.png';
import {
  BulbOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import {
  formatNumber,
  ITraining,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Link, Outlet, useModel, useParams } from '@umijs/max';
import { Image, Result, Skeleton } from 'antd';
import React, { createContext, useEffect } from 'react';

export interface ITrainingLayoutContext {
  training: ITraining;
}

export const TrainingLayoutContext = createContext<ITrainingLayoutContext>({
  training: {} as ITraining,
});

const TrainingLayout: React.FC = () => {
  const params = useParams();

  const { queryDetailLoading, queryTrainingDetail, training } =
    useModel('trainingModel');

  useEffect(() => {
    queryTrainingDetail(params.training_id);
  }, [params.training_id]);

  // 没什么人扫码，先屏蔽掉吧
  // const { data: imgUrl } = useRequest(
  //   () =>
  //     getwxacode({
  //       path: `pages/attendance-leader-board/index?training_id=${params.training_id}`,
  //     }),
  //   {
  //     ready: Boolean(training?.id),
  //     refreshDeps: [training?.id],
  //   },
  // );

  if (queryDetailLoading) {
    return (
      <div className="py-4">
        <div className="bg-white mx-3 rounded-2xl shadow-sm p-3">
          <Skeleton loading />
        </div>
      </div>
    );
  }

  if (!training?.id) {
    return (
      <div className="py-4">
        <div className="bg-white mx-3 rounded-2xl shadow-sm p-3">
          <Result status="warning" subTitle="训练营不存在或已被删除" />
        </div>
      </div>
    );
  }

  return (
    <div className="py-4">
      <div className="bg-white mx-3 rounded-2xl shadow-sm p-3">
        <div className="flex gap-4 items-center mb-3">
          <div className="w-16 h-16 rounded-2xl mx-auto flex items-center justify-center">
            {/* {imgUrl ? (
              <Image src={imgUrl} />
            ) : (
              <Skeleton.Avatar active size={64} />
            )} */}
            <Image src={WeAppQrcode} />
          </div>
          <div className="flex-1">
            <Link to={`/training/${training.id}`}>
              <h2 className="text-lg text-blue-600 font-bold mb-1">
                {training.name}
              </h2>
            </Link>
            <p className="text-sm text-gray-600 line-clamp-2">
              {training.description}
            </p>
          </div>
        </div>

        <div className="p-3 bg-gray-50 rounded-xl">
          <div className="text-sm text-gray-600 mb-1">
            <CalendarOutlined className="mr-1" />
            训练周期
          </div>
          <div className="text-sm font-medium text-gray-800">
            {training.start_time} 至 {training.end_time}
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3">
          <div className="bg-blue-50 rounded-xl p-3 text-center">
            <UsergroupAddOutlined className="text-lg text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-blue-600">
              {training.join_user_count}
            </div>
            <div className="text-xs text-gray-600">参与人数</div>
          </div>
          <div className="bg-blue-50 rounded-xl p-3 text-center">
            <CheckCircleOutlined className="text-lg text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-blue-600">
              {formatNumber(training.attendance_user_count || 0)}
            </div>
            <div className="text-xs text-gray-600">打卡次数</div>
          </div>
          <div className="bg-green-50 rounded-xl p-3 text-center">
            <BulbOutlined className="text-lg text-green-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-green-600">
              {training.standard_score}
            </div>
            <div className="text-xs text-gray-600">达标积分</div>
          </div>
          <div className="bg-green-50 rounded-xl p-3 text-center">
            <BulbOutlined className="text-lg text-green-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-green-600">
              {TRAINING_PROGRESS_MAPPING[training.progress]}
            </div>
            <div className="text-xs text-gray-600">当前阶段</div>
          </div>
        </div>
      </div>

      <div className="px-3 mt-3">
        <TrainingLayoutContext.Provider
          value={{
            training,
          }}
        >
          <Outlet />
        </TrainingLayoutContext.Provider>
      </div>
    </div>
  );
};

export default TrainingLayout;
