import { View } from '@tarojs/components';
import './index.scss';
import NumberWrapper from '../NumberWrapper';

interface IHighlightWrapperProps {
  data: string | number;
  /** 正在被展示 */
  active?: boolean;
  /** 后缀 */
  suffix?: string;
  /** 文字高亮颜色 */
  color?: string;
}

export default ({ active, suffix, data }: IHighlightWrapperProps) => {
  return (
    <View className="report-2023__highlight">
      {typeof data === 'number' ? (
        <View className="flex">
          <View className="report-2023__highlight--number">
            <NumberWrapper active={active} target={data} />
          </View>
          <View className="report-2023__highlight--suffix">{suffix}</View>
        </View>
      ) : (
        <View className="report-2023__highlight--text">{data}</View>
      )}
    </View>
  );
};
