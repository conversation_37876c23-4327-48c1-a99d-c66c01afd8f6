.timeline {
  &-wrapper {
    gap: 20px;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.06);
    border-radius: 16px;
    background-color: #fff;
    padding: 20px;
    margin: 0 20px 20px 20px;

    &:first-child {
      margin-top: 16px;
    }

    &__small {
      margin: 0 0 20px 0;
    }
  }

  &-header {
    font-size: 30px;
    font-weight: bold;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .user-name {
      word-break: keep-all;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }
  }

  &-content {
    swiper {
      height: 260px;
    }
  }

  &-footer {
    margin-top: 10px;
    font-size: 28px;
    color: #999;
  }
}

.attendance {
  &-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 10px;
  }

  &-header {
    position: relative;
    padding-left: 20px;

    &::before {
      content: '';
      position: absolute;
      background: var(--default-color);
      border-radius: 0px 6px 6px 0px;
      width: 8px;
      height: 32px;
      left: 0;
      top: 6px;
    }
  }

  &-content {
    flex: 1;

    .text {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
    }

    &.no-image {
      .text {
        -webkit-line-clamp: 4;
      }
    }
  }

  &-footer {
  }
}
