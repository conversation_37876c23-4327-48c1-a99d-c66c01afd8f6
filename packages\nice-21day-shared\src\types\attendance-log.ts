import { EBooleanString, ICommonFields, IPageParams } from './global';
import { ITraining } from './training';
import { ITrainingTask } from './training-member';
import { IUser } from './user';

/** 用户打卡记录 */
export interface IAttendanceLog extends ICommonFields {
  /** 用户ID */
  user_id: string;
  /** 用户详情 */
  user?: IUser;
  /** 训练营ID */
  training_id: string;
  /** 训练营详情 */
  training?: ITraining;
  /** 打卡日期 */
  attendance_date: string;
  /** 打卡状态 */
  attendance_state: EAttendanceState;
  /** 打卡内容 */
  attendance_tasks: string;
  /** 审核状态 */
  audit_state: EAttendanceLogAuditState;
  /** 审核备注 */
  audit_comment: string;
  /** 点赞数量 */
  like_count?: number;
  /** 评论数量 */
  comment_count?: number;
}

/** 打卡内容 */
export interface IAttendanceTask extends ITrainingTask {
  /** 打卡内容 */
  attendance_content?: string;
  /** 打卡上传的视频 */
  attendance_files?: string[];
}

/** 打卡状态 */
export enum EAttendanceState {
  /** 打卡 */
  Attendance = 'attendance',
  /** 请假 */
  Leave = 'leave',
}

/** 打卡状态映射关系 */
export const ATTENDANCE_STATE_MAPPING = {
  [EAttendanceState.Attendance]: '打卡',
  [EAttendanceState.Leave]: '请假',
};

/** 打卡审核状态 */
export enum EAttendanceLogAuditState {
  /** 审核中 */
  Pending = 'pending',
  /** 审核通过 */
  Valid = 'valid',
  /** 审核未通过 */
  Invalid = 'invalid',
}

/** 打卡审核状态映射关系 */
export const ATTENDANCE_LOG_AUDIT_STATE_MAPPING: Record<
  EAttendanceLogAuditState,
  string
> = {
  [EAttendanceLogAuditState.Pending]: '审核中',
  [EAttendanceLogAuditState.Invalid]: '未通过',
  [EAttendanceLogAuditState.Valid]: '通过',
};

/** 打卡记录查询参数 */
export interface IQueryAttendanceLogParams
  extends IPageParams,
    Partial<
      Pick<
        IAttendanceLog,
        'user_id' | 'training_id' | 'attendance_date' | 'audit_state'
      >
    > {
  /** 返回值是否包含训练营详情 */
  with_training?: EBooleanString;
  /** 返回值是否包含用户详情 */
  with_user?: EBooleanString;
}

export interface IExportAttendanceLogsParams {
  user_id: string;
  /**
   * 训练营ID
   * @description 不传时导出指定用户的所有打卡记录
   */
  training_id?: string;
}
