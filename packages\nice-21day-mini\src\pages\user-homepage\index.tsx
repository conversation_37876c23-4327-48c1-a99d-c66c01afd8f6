import { AppContext } from '@/appContext';
import { Login, Result, UserHomepage } from '@/components';
import { useRouterParams } from '@/hooks';
import { useContext } from 'react';

const UserHomepagePage: React.FC = () => {
  const { user_id } = useRouterParams();
  const { currentUser } = useContext(AppContext);

  if (!currentUser?.id) {
    return <Login />;
  }

  if (!user_id) {
    return <Result text="未找到人员信息" />;
  }

  return <UserHomepage userId={user_id} />;
};

export default UserHomepagePage;
