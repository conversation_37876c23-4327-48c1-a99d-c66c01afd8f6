import UserAttendanceCalendar from '@/components/AttendanceCalendar';
import { UserInfo } from '@/components/UserInfo';
import { queryUserAttendanceCalendar } from '@/services';
import { IAttendanceCalendar } from '@nice-people/nice-21day-shared';
import { history, Link, useSearchParams } from '@umijs/max';
import { Modal, Result, Skeleton } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';

const AttendanceCalendar: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [calendarData, setCalendarData] = useState<IAttendanceCalendar>();
  const [queryLoading, setQueryLoading] = useState(true);

  const trainingId = useMemo(
    () => searchParams.get('training_id'),
    [searchParams],
  );
  const userId = useMemo(() => searchParams.get('user_id'), [searchParams]);

  useEffect(() => {
    if (!trainingId || !userId) {
      return;
    }
    setQueryLoading(true);
    queryUserAttendanceCalendar(trainingId, userId)
      .then((res) => {
        setCalendarData(res);
      })
      .finally(() => {
        setQueryLoading(false);
      });
  }, [trainingId, userId]);

  const handleCalendarChange = (date: Dayjs) => {
    Modal.confirm({
      title: '去补卡',
      okText: '去补卡',
      onOk: () => {
        history.push(
          `/admin/attendance-log/create?training_id=${trainingId}&user_id=${userId}&attendance_date=${dayjs(
            date,
          ).format('YYYY-MM-DD')}`,
        );
      },
    });
  };

  if (queryLoading) {
    return <Skeleton loading />;
  }

  if (!calendarData?.training || !calendarData?.user) {
    return <Result status="error" title="训练营、用户不存在或已被删除" />;
  }

  return (
    <>
      <div>
        训练营：
        <Link
          to={`/admin/training/${calendarData.training.id}/attendance-rate`}
        >
          {calendarData.training.name}
        </Link>
      </div>
      <div>
        成员：
        <Link to={`/admin/user/${calendarData.user.id}`}>
          <UserInfo {...calendarData.user} />
        </Link>
      </div>
      <UserAttendanceCalendar
        calendarData={calendarData}
        onSelect={handleCalendarChange}
      />
    </>
  );
};

export default AttendanceCalendar;
