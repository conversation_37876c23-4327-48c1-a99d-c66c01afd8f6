import {
  filterFiles,
  getFileUrl,
  IA<PERSON>danceLog,
  IAttendanceTask,
  parseArray<PERSON><PERSON>,
} from '@nice-people/nice-21day-shared';
import { Card, Image, Space } from 'antd';
import React, { useMemo } from 'react';

interface IAttendanceLogProfileProps {
  attendanceLog: IAttendanceLog;
}

const AttendanceLogProfile: React.FC<IAttendanceLogProfileProps> = ({
  attendanceLog,
}) => {
  const tasks = useMemo(
    () => parseArrayJson<IAttendanceTask>(attendanceLog.attendance_tasks),
    [attendanceLog.attendance_tasks],
  );

  return (
    <>
      {tasks.map((task) => {
        const files = task.attendance_files || [];
        const { imageList, audioList } = filterFiles(files);
        return (
          <Card
            style={{ marginTop: 20 }}
            size="small"
            key={task.name}
            title={task.name}
          >
            {task.attendance_content && <p>{task.attendance_content}</p>}
            {/* 文件信息 */}
            <Space>
              {imageList.map((imgUrl) => (
                <Image
                  key={imgUrl}
                  width={140}
                  height={140}
                  src={getFileUrl(imgUrl)}
                />
              ))}
            </Space>
            <div>
              {audioList.map((audioUrl) => (
                <audio key={audioUrl} controls src={getFileUrl(audioUrl)} />
              ))}
            </div>
          </Card>
        );
      })}

      <p style={{ marginTop: 20 }}>备注信息：{attendanceLog.description}</p>
    </>
  );
};

export default AttendanceLogProfile;
