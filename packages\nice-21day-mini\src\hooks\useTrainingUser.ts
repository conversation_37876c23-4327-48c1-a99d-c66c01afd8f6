import {
  getTrainingMemberDetailByUserId,
  getTrainingMembers,
  queryTrainingMemberRankings,
} from '@/service';
import { ITrainingMember } from '@nice-people/nice-21day-shared';
import { useCallback, useEffect, useState } from 'react';

/**
 * 获取某个训练营下的报名用户列表
 */
export const useTrainingUserList = (trainingId: string) => {
  // 获取某个训练营下的成员列表
  const [queryTrainingUserListLoading, setQueryTrainingUserListLoading] =
    useState<boolean>(true);
  const [trainingUserList, setTrainingUserList] = useState<ITrainingMember[]>(
    [],
  );
  const fetchTrainingMembers = useCallback(() => {
    setQueryTrainingUserListLoading(true);
    // 这里先获取全部人
    getTrainingMembers({ training_id: trainingId, page: 1, size: 200 })
      .then(({ data }) => {
        setTrainingUserList(data?.rows);
      })
      .finally(() => {
        setQueryTrainingUserListLoading(false);
      });
  }, [trainingId]);
  useEffect(() => {
    fetchTrainingMembers();
  }, [fetchTrainingMembers]);

  return {
    trainingUserList,
    fetchTrainingMembers,
    queryTrainingUserListLoading,
  };
};

/**
 * 获取某个训练营下的某个用户信息
 */
export const useTrainingUser = (trainingId: string, userId: string) => {
  const [queryTrainingUserDetailLoading, setQueryTrainingUserDetailLoading] =
    useState<boolean>(true);
  const [trainingUserDetail, setTrainingUserDetail] = useState<ITrainingMember>(
    {} as ITrainingMember,
  );

  const queryTrainingUser = useCallback(() => {
    if (!userId) {
      setQueryTrainingUserDetailLoading(false);
      return;
    }

    setQueryTrainingUserDetailLoading(true);
    getTrainingMemberDetailByUserId(trainingId, userId)
      .then(({ data }) => {
        setTrainingUserDetail(data);
      })
      .finally(() => {
        setQueryTrainingUserDetailLoading(false);
      });
  }, [trainingId, userId]);

  useEffect(queryTrainingUser, [queryTrainingUser]);

  return {
    queryTrainingUser,
    trainingUserDetail,
    queryTrainingUserDetailLoading,
    setTrainingUserDetail,
  };
};

/**
 * 获取训练营下的用户排行榜
 * @param trainingId 训练营ID
 * @returns
 */
export const useTrainingUserRankings = (trainingId: string) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [scoreRankings, setScoreRankings] = useState<ITrainingMember[]>([]);

  useEffect(() => {
    setLoading(true);
    queryTrainingMemberRankings(trainingId)
      .then(({ data }) => {
        setScoreRankings(data);
      })
      .finally(() => setLoading(false));
  }, [trainingId]);
  return {
    scoreRankingsLoading: loading,
    scoreRankings,
  };
};
