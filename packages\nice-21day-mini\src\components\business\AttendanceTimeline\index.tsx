import { AppContext } from '@/appContext';
import {
  AttendanceTimelineItem,
  Avatar,
  ListWrapper,
  TrainingLayout,
} from '@/components';
import {
  useAttendanceAnalysis,
  useMyTrainingScore,
  useTraining,
  useTrainingUser,
  useTrainingUserList,
} from '@/hooks';
import { queryAttendanceLogs } from '@/service';
import {
  EBooleanString,
  formatNumber,
  IAttendanceLog,
  IQueryAttendanceLogParams,
} from '@nice-people/nice-21day-shared';
import { Picker, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useContext, useMemo } from 'react';
import { AtFab, AtLoadMore, AtProgress } from 'taro-ui';
import './index.scss';

const today = dayjs().format('YYYY-MM-DD');

interface IAttendanceTimelineProps {
  training_id: string;
  user_id?: string;
}

const AttendanceTimeline: React.FC<IAttendanceTimelineProps> = ({
  training_id,
  user_id,
}) => {
  const { currentUser } = useContext(AppContext);

  const { trainingDetail, queryTrainingLoading } = useTraining(training_id);
  const { refreshMyScore } = useMyTrainingScore(training_id);
  const {
    trainingUserDetail,
    queryTrainingUserDetailLoading,
    setTrainingUserDetail,
  } = useTrainingUser(training_id, user_id);

  const { attendanceAnalysis, queryAttendanceAnalysisLoading } =
    useAttendanceAnalysis(training_id, today);

  const { trainingUserList } = useTrainingUserList(training_id);

  const userPickerRange = useMemo(() => {
    return (trainingUserList || []).map((member) => ({
      name: member.user?.nick_name,
      id: member.user?.id,
    }));
  }, [trainingUserList]);

  // 判断今日是否是否在打卡范围内
  // 如果在打卡范围内，统计今天打卡打卡率
  const todayIsNeedAttendance = useMemo(() => {
    if (!trainingDetail) {
      return false;
    }
    return (
      +new Date() >= +new Date(trainingDetail.start_time) &&
      +new Date() <= +new Date(trainingDetail.end_time)
    );
  }, [trainingDetail]);

  // 判断我今天是否已打卡
  const showAttendanceButton = useMemo(() => {
    if (!todayIsNeedAttendance) {
      return false;
    }
    return false;
    // return !attendanceLogs.find(
    //   (row) => row.attendance_date === today && row.user_id === currentUser.id,
    // );
  }, [todayIsNeedAttendance]);

  if (
    queryTrainingLoading ||
    queryTrainingUserDetailLoading ||
    queryAttendanceAnalysisLoading
  ) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <View className="timeline-page">
      <TrainingLayout
        title={
          <>
            {/* 有人员ID表示：训练营下这个用户的打卡时间轴 */}
            {user_id && (
              <>
                <Avatar
                  avatar={trainingUserDetail.user?.avatar_url}
                  userName={trainingUserDetail.user?.nick_name}
                  layout="horizontal"
                  size="small"
                  link
                  onClick={() => {
                    const userId = trainingUserDetail.user?.id;
                    if (userId) {
                      Taro.navigateTo({
                        url: `/pages/user-homepage/index?user_id=${userId}`,
                      });
                    }
                  }}
                />
              </>
            )}
            {/* 没有人员ID表示：训练营下所有用户的打卡时间轴 */}
            {!user_id && (
              <>
                <View>打卡时间轴</View>
                <View>
                  <Picker
                    mode="selector"
                    range={userPickerRange}
                    rangeKey="name"
                    onChange={(e) => {
                      const index = e.detail.value;
                      const userId = userPickerRange[+index]?.id;
                      if (!userId) {
                        Taro.showToast({
                          title: '人员ID错误',
                          icon: 'error',
                        });
                        return;
                      }
                      Taro.navigateTo({
                        url: `/pages/attendance-timeline-user/index?training_id=${training_id}&user_id=${userId}`,
                      });
                    }}
                  >
                    <View
                      className="mt-2"
                      style={{ color: '#999', fontSize: 14 }}
                    >
                      报名人员筛选{' '}
                      <View className="at-icon at-icon-chevron-down" />
                    </View>
                  </Picker>
                </View>
              </>
            )}
          </>
        }
        training={trainingDetail}
        trainingProfileProps={{
          desc: (
            <View>
              {/* 用用户ID就展示用户积分 */}
              {user_id && (
                <>
                  <View className="flex justify-start gap-2 items-center h-7">
                    <Text>用户:</Text>
                    <Text>{trainingUserDetail.user?.nick_name}</Text>
                  </View>
                  {/* <View className="flex justify-start gap-3 items-center h-7">
                    <Text>打卡次数: </Text>{' '}
                    <Text>{trainingUserDetail.total_attendance_count}</Text>
                  </View> */}
                  <View className="flex justify-between items-center h-7">
                    <View className="flex gap-2 justify-center">
                      <View>当前积分:</View>
                      <View className="flex items-center gap-2">
                        <Text>{trainingUserDetail?.score}</Text>
                        {trainingUserDetail?.score_updated_at && (
                          <View
                            className="at-icon at-icon-alert-circle"
                            onClick={() => {
                              Taro.showModal({
                                title: '积分最后更新时间',
                                content: `${dayjs(
                                  trainingUserDetail.score_updated_at,
                                ).format('YYYY-MM-DD HH:mm:ss')}`,
                                showCancel: false,
                                confirmText: '我知道了',
                              });
                            }}
                          />
                        )}
                      </View>
                    </View>
                    {user_id === currentUser.id && (
                      <View
                        className="flex gap-1 items-center text-sm"
                        onClick={() => {
                          // 判断更新时间不要太频繁
                          if (
                            trainingUserDetail?.score_updated_at &&
                            dayjs().diff(
                              dayjs(trainingUserDetail?.score_updated_at),
                              'minutes',
                            ) <= 10
                          ) {
                            Taro.showModal({
                              title: '操作过于频繁',
                              content: '10分钟内只能刷新一次哦',
                              showCancel: false,
                              confirmText: '我知道了',
                            });
                            return;
                          }

                          refreshMyScore().then((response) => {
                            setTrainingUserDetail(response.data);
                          });
                        }}
                      >
                        <View className="at-icon at-icon-reload" />
                        刷新积分
                      </View>
                    )}
                  </View>
                </>
              )}
              {/* 全部用户就展示今日打卡率 */}
              {!user_id && (
                <>
                  <View className="flex justify-start gap-3 items-center h-7">
                    <Text>全部打卡统计: </Text>{' '}
                    <Text>
                      打卡{' '}
                      {formatNumber(attendanceAnalysis.total_attendance_count)}{' '}
                      , 请假 {attendanceAnalysis.total_level_count}
                    </Text>
                  </View>
                  <View className="flex gap-3 items-center h-7">
                    <AtProgress
                      className="flex-1"
                      percent={attendanceAnalysis.total_attendance_rate}
                      strokeWidth={18}
                      isHidePercent
                      color="var(--progress-color)"
                    />
                    <Text className="w-10">
                      {attendanceAnalysis.total_attendance_rate}%
                    </Text>
                  </View>
                  {todayIsNeedAttendance && (
                    <>
                      <View className="flex justify-start gap-3 items-center h-7">
                        <Text>今日打卡统计: </Text>{' '}
                        <Text>
                          打卡 {attendanceAnalysis.attendance_count} , 请假{' '}
                          {attendanceAnalysis.level_count}
                        </Text>
                      </View>
                      <View className="flex gap-3 items-center h-7">
                        <AtProgress
                          className="flex-1"
                          percent={attendanceAnalysis.attendance_rate}
                          strokeWidth={18}
                          isHidePercent
                          color="var(--progress-color)"
                        />
                        <Text className="w-10">
                          {attendanceAnalysis.attendance_rate}%
                        </Text>
                      </View>
                    </>
                  )}
                </>
              )}
            </View>
          ),
        }}
      >
        <ListWrapper<IAttendanceLog, IQueryAttendanceLogParams>
          height="100%"
          requestFn={queryAttendanceLogs}
          refresherEnabled={false}
          params={{
            training_id,
            ...(user_id ? { user_id } : {}),
            with_user: EBooleanString.YES,
            page: 1,
            size: 20,
          }}
          emptyText="没有找到相关的打卡记录"
          renderItem={(attendance) => (
            <AttendanceTimelineItem
              attendance={attendance}
              training_id={training_id}
              user_id={user_id}
            />
          )}
        />
      </TrainingLayout>

      {/* 如果我当天未打卡，显示打卡按钮 */}
      {showAttendanceButton && (
        <View
          className="fixed bottom-5 right-5"
          onClick={() => {
            Taro.navigateTo({
              url: `/pages/attendance/index?training_id=${training_id}`,
            });
          }}
        >
          <AtFab>去打卡</AtFab>
        </View>
      )}
    </View>
  );
};

export default AttendanceTimeline;
