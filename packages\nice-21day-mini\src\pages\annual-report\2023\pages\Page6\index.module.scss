.container {
  background-image: url('./assets/background.svg');
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: bottom;
  background-repeat: no-repeat;

  :global {
    .report-2023__content,
    .report-2023__content--desc,
    .report-2023__highlight {
      color: #cc5600 !important;
    }
  }
}

.location {
  // animation: jump 1s ease-in infinite;
}

@keyframes jump {
  0% {
    transform: translateY(0) scale(1.15, 0.9);
  }

  20% {
    transform: translateY(-35px) scaleY(1.1);
  }

  50% {
    transform: translateY(-50px) scale(1);
  }

  80% {
    transform: translateY(-35px) scale(1);
  }

  to {
    transform: translateY(0) scale(1.15, 0.9);
  }
}

@keyframes jump_v1 {
  0% {
    bottom: 200px;
  }

  50% {
    bottom: 240px;
  }

  100% {
    bottom: 200px;
  }
}
