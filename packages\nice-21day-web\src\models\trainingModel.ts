import {
  queryAllTrainings as queryAll,
  queryTrainingDetail as queryDetail,
} from '@/services';
import { ITraining } from '@nice-people/nice-21day-shared';
import { useCallback, useState } from 'react';

export default () => {
  const [queryAllloading, setQueryAllLoading] = useState(true);
  const [allTrainings, setAllTrainings] = useState<ITraining[]>([]);

  const [training, setTraining] = useState<ITraining>();
  const [queryDetailLoading, setQueryDetailLoading] = useState(true);

  const queryAllTrainings = useCallback(() => {
    setQueryAllLoading(true);
    queryAll()
      .then((res) => {
        setAllTrainings(res);
      })
      .finally(() => {
        setQueryAllLoading(false);
      });
  }, []);

  const queryTrainingDetail = useCallback((id?: string) => {
    if (!id) {
      setTraining(undefined);
    }
    setQueryDetailLoading(true);
    queryDetail(id!)
      .then((res) => {
        setTraining(res);
      })
      .finally(() => {
        setQueryDetailLoading(false);
      });
  }, []);

  return {
    queryAllloading,
    queryAllTrainings,
    allTrainings,

    queryDetailLoading,
    queryTrainingDetail,
    training,
  };
};
