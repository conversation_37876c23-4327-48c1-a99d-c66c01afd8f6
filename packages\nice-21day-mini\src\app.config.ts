export default defineAppConfig({
  pages: [
    'pages/index/index', // layout，判断是否登录，分发跳转

    // 登录
    // =============
    'pages/login/index', // 登录页面

    // 训练营
    // =============
    'pages/training-list/index', // 所有的训练营列表
    'pages/training-registering-list/index', // 所有在报名阶段中的训练营列表
    'pages/training-certificate/index', // 我的证书

    // 训练营成员
    // =============
    'pages/training-member-list/index', // 训练营成员列表
    'pages/training-member-detail/index', // 训练营成员详情

    // 报名参加训练营
    // =============
    'pages/join-training/index', // 去报名
    'pages/join-training-detail/index', // 报名详情

    // 打卡
    // =============
    'pages/attendance/index', // 去打卡
    // 'pages/attendance-leave/index', // 请假
    'pages/attendance-detail/index', // 打卡详情
    'pages/attendance-logs/index', // 打卡记录
    'pages/attendance-timeline/index', // 打卡时间轴
    'pages/attendance-timeline-user/index', // 成员打卡时间轴
    'pages/attendance-leader-board/index', // 打卡排行榜
    'pages/user-training-summary/index', // 训练营总结

    // 我需要打卡的训练营列表
    'pages/my-training-processing-list/index',

    // 声音创作
    // =============
    'pages/studio-list/index', // 声音创作列表
    'pages/studio-list-user/index', // 某个人的声音创作列表
    'pages/studio-detail/index', // 声音创作详情
    'pages/studio-create/index', // 录制声音
    'pages/studio-update/index', // 编辑声音

    // 积分
    // =============
    // 'pages/score-logs/index', // 用户在训练营下的积分变更记录

    // 社区成员
    // =============
    'pages/user-list/index', // 社区成员列表
    'pages/user-homepage/index', // 个人主页
    'pages/user-detail/index', // 社区成员详情
    'pages/user-following/index', // 关注人列表
    'pages/user-followers/index', // 粉丝列表

    // 力扣
    // =============
    // 'pages/leetcode-weekly/index', // 力扣周报

    // 我的
    // =============
    'pages/my/index', // 我的
    'pages/my-training-list/index', // 我的训练营（已报名未开始、打卡中、已结束）
    'pages/my-profile/index', // 我的个人信息
    'pages/help/index', // 帮助中心
  ],
  subpackages: [
    {
      root: 'pages/analytics/',
      pages: [
        'user-attendance-analysis/index', // 用户打卡统计
        'training-attendance-analysis/index', // 训练营打卡统计
      ],
    },
    {
      root: 'pages/annual-report/',
      pages: [
        '2023/landing/index', // 2023年度总结分享落地页
        '2023/index', // 2023年度总结
      ],
    },
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
  },
  lazyCodeLoading: 'requiredComponents',
  tabBar: {
    color: '#9c9d9e',
    selectedColor: '#5dc991',
    backgroundColor: '#24292F',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/training-list/index',
        iconPath: './static/tab-bar/home_v2.png',
        selectedIconPath: './static/tab-bar/home_v2_active.png',
        text: '首页',
      },
      {
        pagePath: 'pages/my-training-processing-list/index',
        iconPath: './static/tab-bar/attendance.png',
        selectedIconPath: './static/tab-bar/attendance_active.png',
        text: '去打卡',
      },
      // {
      //   pagePath: 'pages/studio-list/index',
      //   iconPath: './static/tab-bar/studio.png',
      //   selectedIconPath: './static/tab-bar/studio_active.png',
      //   text: '声音创作',
      // },
      // {
      //   pagePath: 'pages/my-training-processing-list/index',
      //   iconPath: './static/tab-bar/attendance.png',
      //   selectedIconPath: './static/tab-bar/attendance_active.png',
      //   text: '我的打卡',
      // },
      // {
      //   pagePath: 'pages/leetcode-weekly/index',
      //   iconPath: './static/tab-bar/attendance.png',
      //   selectedIconPath: './static/tab-bar/attendance_active.png',
      //   text: '力扣',
      // },
      // {
      //   pagePath: 'pages/user-list/index',
      //   iconPath: './static/tab-bar/attendance.png',
      //   selectedIconPath: './static/tab-bar/attendance_active.png',
      //   text: '已报名成员',
      // },
      {
        pagePath: 'pages/my/index',
        iconPath: './static/tab-bar/user_v2.png',
        selectedIconPath: './static/tab-bar/user_v2_active.png',
        text: '我的',
      },
    ],
  },
  usingComponents: {},
});
