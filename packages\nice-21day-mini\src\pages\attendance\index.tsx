import { AppContext } from '@/appContext';
import { AttendanceTask, Result, TrainingLayout, Login } from '@/components';
import { Contact } from '@/components/business/Contact';
import { useTraining } from '@/hooks';
import {
  editUserAttendance,
  getTrainingJoinInDetail,
  queryMyAttendanceDetail,
  userAttendance,
  userLeave,
} from '@/service';
import { getNowDate } from '@/utils/index';
import {
  ATTENDANCE_LIMIT_MORNING_HOURS,
  EAttendanceLogAuditState,
  EAttendanceState,
  ETrainingProgress,
  IAttendanceLog,
  IAttendanceTask,
  parseArrayJson,
  uuid,
} from '@nice-people/nice-21day-shared';
import { Button, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';

import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  AtButton,
  AtLoadMore,
  AtModal,
  AtModalAction,
  AtModalContent,
  AtModalHeader,
  AtNoticebar,
  AtProgress,
  AtTextarea,
} from 'taro-ui';

const today = dayjs().format('YYYY-MM-DD');
const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
const todayHours = +dayjs().format('H');

const AttendancePage: React.FC = () => {
  const { currentUser } = useContext(AppContext);
  // 获取路由训练营id，选择的日期
  const { training_id, date } = Taro.getCurrentInstance().router.params;

  const { trainingDetail, queryTrainingLoading } = useTraining(training_id);

  const [isEdit, setIsEdit] = useState(false);
  // 定义打卡信息
  const [attendanceDetail, setAttendanceDetail] = useState<IAttendanceLog>(
    {} as IAttendanceLog,
  );

  const [attendanceTaskList, setAttendanceTaskList] = useState([]);

  const [queryAttendanceDetailLoading, setQueryAttendanceDetailLoading] =
    useState(true);

  // 请假相关状态
  const [leaveModalVisible, setLeaveModalVisible] = useState(false);
  const [leaveReason, setLeaveReason] = useState<string>('');

  // 打卡日期
  const [attendanceDate, setAttendanceDate] = useState(
    () => date || getNowDate(),
  );

  if (!training_id) {
    Taro.atMessage({
      message: '获取训练营id错误，请返回列表重试',
      type: 'error',
    });
  }

  const allowAttendanceDateList: string[] = useMemo(() => {
    // 表示外部指定打某一天的卡
    if (date) {
      return [date];
    }

    if (!trainingDetail.start_time) {
      return [today];
    }
    // 判断当前时间
    const isFirstDate = dayjs().isSame(dayjs(trainingDetail.start_time), 'D');
    const isAfterFirstDate = dayjs().isAfter(
      dayjs(trainingDetail.start_time),
      'D',
    );

    // 第一天打卡的话只能打当天的卡
    if (isFirstDate) {
      return [today];
    }
    // 第一天以后的凌晨4点之前可以选择昨天
    if (isAfterFirstDate && todayHours < ATTENDANCE_LIMIT_MORNING_HOURS) {
      return [yesterday, today];
    }
    return [today];
  }, [date, trainingDetail.start_time]);

  const allowAttendance = useMemo(() => {
    if (
      date &&
      today !== date &&
      !(date === yesterday && todayHours < ATTENDANCE_LIMIT_MORNING_HOURS)
    ) {
      return false;
    }

    return true;
  }, [date]);

  useEffect(() => {
    // 判断是否在打卡范围内
    if (!allowAttendance) {
      Taro.showModal({
        title: '提示',
        content: `${date}已经不在打卡时间窗口内。如需补卡或修改打卡记录，请联系系统管理员`,
        showCancel: false,
        confirmText: '知道了',
        success: (res) => {
          if (res.confirm) {
            Taro.reLaunch({
              url: '/pages/index/index',
            });
          }
        },
      });
    }
  }, [allowAttendance]);

  const changeAttendanceDate = (newDate: string) => {
    Taro.showModal({
      title: '确定切换打卡日期吗？',
      content: '切换打卡日期后，填写的内容将被清空',
      success: (res) => {
        if (res.confirm) {
          setAttendanceDate(newDate);
        }
      },
    });
  };

  /** 设置state上的tasklist */
  const updateAttendanceTasks = useCallback((taskList: IAttendanceTask[]) => {
    setAttendanceTaskList(
      taskList.map((task) => ({ ...task, id: task.id || uuid() })),
    );
  }, []);

  // !获取打卡记录
  // 如果没有记录，则新建打卡请求训练营信息
  useEffect(() => {
    const fetchData = async () => {
      setIsEdit(false);
      setAttendanceDetail({} as IAttendanceLog);
      setQueryAttendanceDetailLoading(true);
      setAttendanceTaskList([]);

      const { data } = await queryMyAttendanceDetail(
        training_id,
        attendanceDate,
      );
      setQueryAttendanceDetailLoading(false);
      const detail = data || ({} as IAttendanceLog);
      // 设置数据
      setAttendanceDetail(detail);
      setAttendanceTaskList(parseArrayJson(detail.attendance_tasks));

      // 如果存在打卡记录，则为编辑模式
      if (data.id) {
        setIsEdit(true);
        return;
      }

      // 没有打卡记录，请求报名信息
      const { data: joinData } = await getTrainingJoinInDetail(training_id);
      const tastList = parseArrayJson<IAttendanceTask>(joinData?.tasks).map(
        // 设置初始值，不然接口会报错
        (task) => ({
          ...{
            attendance_content: '',
            attendance_files: [],
          },
          ...task,
        }),
      );
      updateAttendanceTasks(tastList);
    };

    fetchData();
  }, [attendanceDate, training_id]);

  /**
   * 子组件更新父组件的task的方法
   * @param newTask 当前操作的task的值
   */
  const handleUpdateTask = useCallback(
    (newTask: IAttendanceTask) => {
      const taskList = [...attendanceTaskList];
      for (let i = 0; i < taskList.length; i++) {
        if (taskList[i].id === newTask.id) {
          taskList[i] = {
            ...taskList[i],
            ...newTask,
          };
          break;
        }
      }
      updateAttendanceTasks(taskList);
    },
    [attendanceTaskList, updateAttendanceTasks],
  );

  /**
   * 提交打卡
   */
  const handlePostAttendance = useCallback(async () => {
    // 检查是否打卡一项内容
    const empty = attendanceTaskList.every(
      (task) => !task.attendance_content && task.attendance_files.length === 0,
    );

    if (empty) {
      Taro.showModal({
        title: '打卡失败',
        content: '请至少打卡一个任务',
        showCancel: false,
        confirmText: '知道了',
      });
      return;
    }

    // 部分内容先固定，等二期扩充：如 attendance_state、audit_state、audit_comment
    const postData: Omit<IAttendanceLog, 'id' | 'user_id'> = {
      training_id,
      attendance_tasks: '',
      attendance_date: attendanceDate,
      attendance_state: EAttendanceState.Attendance,
      audit_state: EAttendanceLogAuditState.Valid,
      audit_comment: '审核通过',
      description: '',
    };
    postData.attendance_tasks = JSON.stringify(attendanceTaskList);

    Taro.showModal({
      title: '确认保存打卡吗？',
      content: `打卡日期[${postData.attendance_date}]，请确认好日期后再提交`,
      success: async function (res) {
        if (res.confirm) {
          try {
            const { data: newAttendanceDetail } = await (isEdit
              ? editUserAttendance(attendanceDetail.id, postData)
              : userAttendance(training_id, postData));
            // 提示成功
            Taro.showToast({
              title: '打卡成功',
              icon: 'success',
              duration: 2000,
            });
            // 跳转到打卡详情页
            Taro.redirectTo({
              url: `/pages/attendance-detail/index?training_id=${training_id}&attendance_id=${newAttendanceDetail.id}&from=attendance`,
            });
          } catch (error) {
            Taro.showModal({
              title: '打卡失败',
              content: error?.data?.message || '',
              showCancel: false,
              confirmText: '知道了',
            });
          }
        }
      },
    });
  }, [
    attendanceDetail?.id,
    attendanceTaskList,
    attendanceDate,
    isEdit,
    training_id,
  ]);

  // 请假
  const handleLeave = useCallback(() => {
    Taro.showModal({
      title: '确认请假吗？',
      success: function (res) {
        if (res.confirm) {
          userLeave(training_id, {
            attendance_date: attendanceDate,
            description: leaveReason || '',
          })
            .then(() => {
              Taro.showToast({
                title: '请假成功',
                icon: 'success',
                duration: 1000,
              });
              Taro.navigateBack();
            })
            .catch((error) => {
              Taro.showModal({
                title: '请假失败',
                content: error?.data?.message || '',
                showCancel: false,
                confirmText: '知道了',
              });
            });
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      },
    });
  }, [training_id, attendanceDate, leaveReason]);

  useEffect(() => {
    if (!leaveModalVisible) {
      setLeaveReason('');
    }
  }, [leaveModalVisible]);

  // 计算打卡百分比
  const attendanceProgress = useMemo(() => {
    if (!trainingDetail) {
      return {
        trainingTotalDays: 0,
        attendancePercent: 0,
        myAttendanceCount: 0,
      };
    }
    const {
      end_time,
      start_time,
      my_attendance_count: myAttendanceCount,
    } = trainingDetail;
    // 总天数
    const trainingTotalDays = dayjs(end_time).diff(start_time, 'days') + 1;
    // 打卡进度
    const attendancePercent = Math.trunc(
      (myAttendanceCount / trainingTotalDays) * 100,
    );

    return {
      trainingTotalDays,
      attendancePercent,
      myAttendanceCount,
    };
  }, [trainingDetail]);

  // 判断昨天是否已经打卡
  const yesterdayIsAttendance = useMemo(() => {
    if (attendanceDetail?.id && attendanceDate === yesterday) {
      return true;
    }
    return false;
  }, [attendanceDate, attendanceDetail]);

  // 判断是否处于打卡阶段
  const renderContent = useCallback(() => {
    if (!trainingDetail.id) {
      return null;
    }
    if (trainingDetail.progress !== ETrainingProgress.Processing) {
      return <Result text="训练营已经不在打卡阶段" />;
    }

    return (
      <>
        <View className="flex items-center gap-2 mb-3">
          <Text>打卡日期</Text>
          <View className="flex gap-2">
            {allowAttendanceDateList.map((cDate) => (
              <AtButton
                key={cDate}
                circle
                size="small"
                type={cDate === attendanceDate ? 'primary' : 'secondary'}
                onClick={() => {
                  if (cDate !== attendanceDate) {
                    changeAttendanceDate(cDate);
                  }
                }}
              >
                {cDate}
              </AtButton>
            ))}
          </View>
        </View>

        <View
          className="iphone-botton-padding-safe mb-100px"
          key={attendanceDate}
        >
          {attendanceTaskList.map((item, idx) => (
            <AttendanceTask
              key={`${idx}_${attendanceDate}`}
              task={{
                ...item,
                attendance_content: item.attendance_content || '',
                attendance_files: item.attendance_files || [],
              }}
              taskIndex={idx}
              onChange={handleUpdateTask}
            />
          ))}
        </View>

        {/* 首次打卡时，可以请假 */}
        {/* 增加ios底部安全距离防止遮挡，增加模糊体验 */}
        <View className="fixed bottom-0 w-[calc(100%-40px)] z-10 iphone-botton-padding-safe backdrop-filter backdrop-blur-md">
          {!isEdit && (
            <View className="flex gap-4">
              <AtButton
                circle
                type="primary"
                className="danger-button w-28"
                onClick={() => setLeaveModalVisible(true)}
              >
                请假
              </AtButton>
              <AtButton
                circle
                type="primary"
                className="flex-1"
                onClick={() => {
                  handlePostAttendance();
                }}
              >
                保存打卡
              </AtButton>
            </View>
          )}
          {/* 编辑时可以删除打卡和编辑打卡 */}
          {isEdit && (
            <View className="rounded-3xl overflow-hidden">
              <AtButton
                circle
                onClick={() => {
                  handlePostAttendance();
                }}
                type="primary"
              >
                保存打卡
              </AtButton>
            </View>
          )}
        </View>
      </>
    );
  }, [
    trainingDetail,
    attendanceTaskList,
    isEdit,
    allowAttendanceDateList,
    attendanceDate,
    yesterdayIsAttendance,
  ]);

  if (!currentUser?.id) {
    return <Login />;
  }

  // loading
  if (queryTrainingLoading || queryAttendanceDetailLoading) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <>
      <TrainingLayout
        title={
          <>
            <View>[{attendanceDate}] 打卡</View>
            <View>
              <AtNoticebar
                icon="volume-plus"
                className="mt-1"
                single
                showMore
                moreText="查看详情"
                onGotoMore={() => {
                  Taro.showModal({
                    title: '打卡规则调整',
                    content:
                      '凌晨 4 点之前可正常提交、编辑昨天的打卡。如果你想在凌晨 4 点之后打卡当天日期，目前是不可以的哦，请你睡醒再来吧。',
                    showCancel: false,
                    confirmText: '我知道了',
                  });
                }}
              >
                打卡规则调整
              </AtNoticebar>
            </View>
          </>
        }
        training={trainingDetail}
        trainingProfileProps={{
          desc: (
            <View>
              {/* TODO: 积分有问题，先下掉 */}
              {/* <View className="flex justify-start gap-3 items-center h-7">
                <Text>我的当前积分: </Text>{' '}
                <Text>{trainingDetail?.my_join_info?.score}</Text>
              </View> */}

              {/* 打卡日期 */}
              {/* <View className="flex justify-start gap-3 items-center h-7">
                <Text>当前打卡日期: </Text>
                <Text>{attendanceDate}</Text>
              </View> */}
              {/* 打卡进度 */}
              <View className="flex justify-between gap-3 items-center h-7">
                <Text>我已打卡 {attendanceProgress.myAttendanceCount} 天</Text>
                <Contact />
              </View>
              <View className="flex gap-3 items-center h-7">
                <AtProgress
                  className="flex-1"
                  percent={attendanceProgress.attendancePercent}
                  strokeWidth={18}
                  isHidePercent
                  color="var(--progress-color)"
                />
                <Text>
                  {attendanceProgress.myAttendanceCount} /{' '}
                  {attendanceProgress.trainingTotalDays}
                </Text>
              </View>
            </View>
          ),
        }}
      >
        {renderContent()}
      </TrainingLayout>

      {/* 请假模态框 */}
      <AtModal isOpened={leaveModalVisible} closeOnClickOverlay={false}>
        <AtModalHeader>确认 {date} 请假吗？</AtModalHeader>
        <AtModalContent>
          {/* https://taro-ui.jd.com/#/docs/modal */}
          {/* 由于微信小程序的限制，在 Modal 组件中使用一些客户端创建的原生组件（如：textarea、input等）时，即使 Modal 组件处于隐藏状态 */}
          {/* 给个高度，防止弹出框高度塌陷影响体验 */}
          <View style={{ height: 106 }}>
            {leaveModalVisible && (
              <AtTextarea
                value={leaveReason}
                onChange={setLeaveReason}
                maxLength={200}
                placeholder="选填：可以填写请假原因"
              />
            )}
          </View>
        </AtModalContent>
        <AtModalAction>
          <Button onClick={() => setLeaveModalVisible(false)}>取消</Button>
          <Button onClick={() => handleLeave()}>确定</Button>
        </AtModalAction>
      </AtModal>
    </>
  );
};

export default AttendancePage;
