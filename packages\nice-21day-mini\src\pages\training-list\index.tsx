import {
  <PERSON>Wrapper,
  ListWrapperHandler,
  LoginGuide,
  TrainingListItem,
} from '@/components';
import { getTrainingList } from '@/service';
import {
  EState,
  IQueryTrainingParams,
  ITraining,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import { useRef, useState } from 'react';
import './index.scss';

export default function TrainingList() {
  const ref = useRef<ListWrapperHandler>(null);

  const [isFirstShow, setIsFirstShow] = useState(true);
  useDidShow(() => {
    if (!isFirstShow) {
      ref.current?.refresh();
      return;
    }

    setIsFirstShow(false);
  });

  return (
    <View>
      <View className="home_wrapper">
        <ListWrapper<ITraining, IQueryTrainingParams>
          ref={ref}
          height="100%"
          requestFn={getTrainingList}
          params={{
            state: EState.Enable,
            size: 20,
          }}
          emptyText="当前还没有训练营"
          renderItem={(record) => (
            <TrainingListItem key={record.id} training={record} anonymous />
          )}
        />
      </View>

      <LoginGuide />
    </View>
  );
}
