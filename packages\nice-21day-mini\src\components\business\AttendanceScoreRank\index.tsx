import { AppContext } from '@/appContext';
import { useTrainingUserRankings } from '@/hooks';
import { ITraining } from '@nice-people/nice-21day-shared';
import { useContext, useMemo } from 'react';
import { AtList, AtLoadMore } from 'taro-ui';
import ScoreRankItem from './RankItem';
import { Result } from '@/components/common';

interface IAttendanceScoreRankProps {
  training: ITraining;
}

const AttendanceScoreRank = ({ training }: IAttendanceScoreRankProps) => {
  const { scoreRankings, scoreRankingsLoading } = useTrainingUserRankings(
    training.id,
  );
  const { currentUser } = useContext(AppContext);

  const myRanking = useMemo(() => {
    return scoreRankings.find((item) => item.user_id === currentUser.id);
  }, [scoreRankings, currentUser]);

  if (scoreRankingsLoading) {
    return <AtLoadMore status="loading" />;
  }

  if (scoreRankings.length === 0) {
    return <Result />;
  }

  return (
    <>
      {myRanking && (
        <AtList hasBorder={false} className="rounded mb-2">
          <ScoreRankItem
            trainingUser={myRanking}
            training={training}
            hasBorder={false}
            isMySelf
          />
        </AtList>
      )}

      <AtList hasBorder={false} className="rounded">
        {scoreRankings.map((item, index) => (
          <ScoreRankItem
            key={item.id}
            trainingUser={item}
            training={training}
            hasBorder={index !== scoreRankings.length - 1}
            isMySelf={item.user_id === currentUser.id}
          />
        ))}
      </AtList>
    </>
  );
};

export default AttendanceScoreRank;
