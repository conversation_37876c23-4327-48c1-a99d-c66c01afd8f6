import { Area, Axis, Chart, Legend, Line, Point } from '@antv/f2';
import { IUserReportStatistics } from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import { useMemo } from 'react';
import F2Canvas from 'taro-f2-react';
import HighlightWrapper from '../../components/HighlightWrapper';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

const categorySlogenMap: Record<string, string> = {
  早起: '能够掌控早晨的人，才能掌控人生',
  读书: '闭门既是深山，读书随处净土',
  冥想: '独处不是孤独，而是一种能力',
  运动: '少年不运动，老大徒伤悲',
  写作: '写作是一种表达，更是一种思考',
  个人成长: '与其仰望别人，不如提升自己',
};

export default ({ reportData }: IPageProps) => {
  const { statistics } = reportData;

  // 重新排序，按照【个人成长、早起、冥想、阅读、写作、运动】这顺序来
  const categoryAnalysis = useMemo(() => {
    const map = (statistics.category_analysis || []).reduce(
      (acc, item) => ({
        ...acc,
        [item.category]: item.score,
      }),
      {} as any,
    );

    return ['个人成长', '早起', '冥想', '阅读', '写作', '运动'].map((name) => ({
      category: name,
      score: map[name] || 0,
    })) as IUserReportStatistics['category_analysis'];
  }, [statistics.category_analysis]);

  const favorCategory =
    (statistics.category_analysis || []).sort((a, b) => b.score - a.score)?.[0]
      ?.category ?? '';

  return (
    <View
      className={`${styles.container} flex flex-col justify-center items-center`}
    >
      <View className="absolute top-60px right-[-20px]">
        <Image
          className="w-412px h-390px"
          src={require('./assets/pendant.svg')}
        />
      </View>

      <View className="report-2023__content absolute top-320px">
        {favorCategory ? (
          <>
            <View>这一年</View>
            <View>
              <HighlightWrapper data={favorCategory} />
            </View>
            <View>是你投入度最大的方向</View>

            <View>{categorySlogenMap[favorCategory]}</View>
          </>
        ) : (
          <>
            <View>你好像在偷偷的努力</View>
            <View>没有告诉我们</View>
          </>
        )}
        <View className="report-2023__content--desc">
          <View className="w-500px h-500px" style={{ margin: '40px auto 0' }}>
            <F2Canvas id="user-2023-category-polar-chart">
              <Chart
                data={categoryAnalysis}
                coord="polar"
                scale={{
                  score: {
                    min: 0,
                    max: 100,
                    nice: false,
                    tickCount: 4,
                  },
                }}
              >
                <Axis
                  field="category"
                  grid="line"
                  style={{ label: { fill: '#731BFC' } }}
                />
                <Axis
                  field="score"
                  grid="line"
                  color="#731BFC"
                  formatter={() => ''}
                />
                <Line x="category" y="score" color="#731BFC" />
                <Area x="category" y="score" color="#731BFC" />
                <Point x="category" y="score" color="#731BFC" />
                <Legend />
              </Chart>
            </F2Canvas>
          </View>

          <View
            className="text-24px text-[#731BFC] text-center"
            style={{ transform: 'translateY(-20px)' }}
          >
            <View className="mb-6px">投入度根据打卡内容计算得出</View>
          </View>
        </View>
      </View>
    </View>
  );
};
