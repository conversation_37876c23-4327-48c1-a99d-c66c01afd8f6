import {
  getFileUrl,
  ICurrentUser,
  IStudio,
  STUDIO_PUBLIC_SCOPE_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { memo } from 'react';
import { AtAvatar, AtTag } from 'taro-ui';
import './index.scss';

interface IStudioItemProps {
  studio: IStudio;
  /** 当前登录人信息 */
  currentUser: ICurrentUser;
  /** item 点击事件 */
  onClick?: (studio: IStudio) => void;
  /** 头像点击事件 */
  onAvatarClick?: (studio: IStudio) => void;
}

const StudioItem: React.FC<IStudioItemProps> = ({
  studio,
  currentUser,
  onClick,
  onAvatarClick,
}) => {
  return (
    <View
      className="flex flex-col items-start rounded-lg first:mt-2 mb-2 p-3 last-of-type:mb-0 studio-item gap-2"
      onClick={() => {
        if (onClick) {
          onClick?.(studio);
          return;
        }
        Taro.navigateTo({
          url: `/pages/studio-detail/index?studio_id=${studio.id}`,
        });
      }}
    >
      <View className="flex justify-between items-center">
        <View
          className="flex items-center gap-1"
          onClick={(e) => {
            onAvatarClick?.(studio);
            e.stopPropagation();
          }}
        >
          <AtAvatar size="small" image={getFileUrl(studio.user?.avatar_url)} />
          <Text>{studio.user?.nick_name}</Text>
          {studio.content && <Text className="text-neutral-400">朗读了</Text>}
        </View>
        <View className="at-icon at-icon-chevron-right" />
      </View>
      {/* 内容 */}
      {studio.content && (
        <View className="studio-item__content">{studio.content}</View>
      )}
      {/* TODO: 会初始化太多的播放器，先屏蔽 */}
      {/* <AudioPlayer src={getFileUrl(studio.files)} /> */}
      <View className="flex items-center justify-between">
        <View className="text-neutral-400 text-sm">
          发布于 {dayjs(studio.created_at).fromNow()}
        </View>
        {/* 标签 */}
        <View className="flex gap-1">
          <AtTag active type="primary" circle size="small">
            {STUDIO_PUBLIC_SCOPE_MAPPING[studio.public_scope]}
          </AtTag>
          {currentUser?.id === studio.user?.id && (
            <AtTag active type="primary" circle size="small">
              我的作品
            </AtTag>
          )}
        </View>
      </View>
    </View>
  );
};

export default memo(StudioItem);
