import { updateAttendanceLogAuditState } from '@/services';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { EAttendanceLogAuditState } from '@nice-people/nice-21day-shared';
import { Button, message, Modal, Space } from 'antd';
import React, { useCallback, useState } from 'react';

interface IAttendanceAuditProps {
  /** 打卡记录ID */
  attendanceId: string;
  /** 当前的审核状态 */
  auditState: EAttendanceLogAuditState;
  /** 是否展示简单的审核按钮 */
  simple?: boolean;
  /** 操作完之后的 callback */
  onFinish?: () => void;
}
const AttendanceAudit: React.FC<IAttendanceAuditProps> = ({
  attendanceId,
  auditState,
  onFinish,
  simple,
}) => {
  // 当前的操作
  const [newState, setNewState] = useState<EAttendanceLogAuditState>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [updateLoading, setUpdateLoading] = useState(false);

  const handleChangeAuditState = useCallback(
    (auditComment?: string) => {
      if (!newState) {
        return;
      }

      Modal.confirm({
        title: '确定提交吗？',
        onOk: () => {
          setUpdateLoading(true);
          updateAttendanceLogAuditState(attendanceId, newState, auditComment)
            .then(() => {
              message.success('审核完成');
              setModalVisible(false);
              onFinish?.();
            })
            .finally(() => {
              setUpdateLoading(false);
            });
        },
      });
    },
    [newState, attendanceId],
  );

  // TODO: 现在都是审核通过
  return null;

  return (
    <ModalForm<{
      audit_comment?: string;
    }>
      title="打卡审核"
      visible={modalVisible}
      trigger={
        <Space>
          <Button
            type={simple ? 'link' : 'primary'}
            disabled={auditState === EAttendanceLogAuditState.Valid}
            onClick={() => {
              setNewState(EAttendanceLogAuditState.Valid);
              setModalVisible(true);
            }}
          >
            通过
          </Button>
          <Button
            danger={!simple}
            type={simple ? 'link' : 'primary'}
            disabled={auditState === EAttendanceLogAuditState.Invalid}
            onClick={() => {
              setNewState(EAttendanceLogAuditState.Invalid);
              setModalVisible(true);
            }}
          >
            不通过
          </Button>
        </Space>
      }
      autoFocusFirstInput
      modalProps={{
        onCancel: () => {
          setModalVisible(false);
        },
        closable: false,
        afterClose: () => {
          setNewState(undefined);
        },
      }}
      submitTimeout={2000}
      submitter={{
        searchConfig: {
          submitText:
            newState === EAttendanceLogAuditState.Invalid ? '不通过' : '通过',
        },
        submitButtonProps: {
          danger: newState === EAttendanceLogAuditState.Invalid,
          loading: updateLoading,
        },
      }}
      onFinish={async (values) => {
        handleChangeAuditState(values.audit_comment);
      }}
    >
      <ProFormTextArea
        name="audit_comment"
        label="(选填)审核备注"
        tooltip="最长为 256 位"
        placeholder="请输入审核备注"
        fieldProps={{
          maxLength: 256,
          showCount: true,
        }}
      />
    </ModalForm>
  );
};

export default AttendanceAudit;
