import { IPageFactory, IPageParams } from '@nice-people/nice-21day-shared';
import { ScrollView, View } from '@tarojs/components';
import { request } from '@tarojs/taro';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { AtLoadMore } from 'taro-ui';
import { AtLoadMoreProps } from 'taro-ui/types/load-more';
import Result from '../Result';

interface ListWrapperProps<RecordType, ParamsType> {
  /** 容器高度 */
  height: number | string;
  /** 是否开启下拉刷新 */
  refresherEnabled?: boolean;
  /** 获取数据的方法 */
  requestFn: (
    params: ParamsType,
    // @ts-ignore
  ) => Promise<request.SuccessCallbackResult<IPageFactory<RecordType>>>;
  /** 渲染每一行数据的元素 */
  renderItem: (record: RecordType, index?: number) => React.ReactNode;
  /** 请求参数 */
  params?: Omit<ParamsType, 'page' | 'size'> & Partial<IPageParams>;
  /** 每条数据的 key，遍历渲染时需要 */
  rowKey?: string;
  emptyText?: string;
  moreText?: string;
}

export interface ListWrapperHandler {
  refresh: () => void;
}

function ListWrapper<
  RecordType extends Record<string, any>,
  ParamsType extends Record<string, any>,
>(props: ListWrapperProps<RecordType, ParamsType>, ref: any) {
  const {
    height,
    rowKey = 'id',
    refresherEnabled = true,
    renderItem,
    params = {} as ParamsType,
    requestFn,
    emptyText,
    moreText = '查看更多',
  } = props;

  // 返回值
  const [response, setResponse] = useState<IPageFactory<RecordType>>({
    page: 1,
    size: params?.size ?? 20,
    total: 0,
    rows: [] as RecordType[],
  });
  const [loading, setLoading] = useState(true);
  // 重置的loading
  const [refreshLoading, setRefreshLoading] = useState(false);
  // 加载更多的loading
  const [moreLoading, setMoreLoading] = useState(true);
  // 是否已经没有更多了
  const noMore = useMemo(() => {
    return response.page >= Math.ceil(response.total / response.size);
  }, [response.size, response.total, response.page]);

  const atLoadMoreStatus: AtLoadMoreProps['status'] = useMemo(() => {
    if (moreLoading) {
      return 'loading';
    }
    if (noMore) {
      return 'noMore';
    }
    return 'more';
  }, [moreLoading, noMore]);

  useEffect(() => {
    fetchData();
  }, []);

  // 实际拉取数据
  const fetchData = useCallback(
    (page: number = 1, isRefresh: boolean = false, noMerge = false) => {
      if (isRefresh) {
        setRefreshLoading(true);
      } else {
        setMoreLoading(true);
      }
      setLoading(true);
      requestFn({
        ...params,
        page,
        size: response.size,
      } as unknown as ParamsType)
        .then(({ data }) => {
          // 下拉刷新时重置数据
          if (isRefresh) {
            setResponse(data);
            return;
          }
          if (noMerge) {
            setResponse(data);
            return;
          }
          // 加载更多时合并数据
          const newRows = [...response.rows, ...data.rows];
          setResponse({ ...data, rows: newRows });
        })
        .finally(() => {
          if (isRefresh) {
            setRefreshLoading(false);
          } else {
            setMoreLoading(false);
          }
          setLoading(false);
        });
    },
    [response.size, response.rows],
  );

  useImperativeHandle(ref, () => {
    return {
      refresh: () => fetchData(1, false, true),
    };
  });

  // 下拉刷新时
  const handleRefresherRefresh = useCallback(() => {
    if (refreshLoading) {
      return;
    }
    fetchData(1, true);
  }, [refreshLoading]);

  // 取消下拉刷新
  const handleRefresherAbort = () => {
    setRefreshLoading(false);
  };

  const handleScrollToLower = useCallback(() => {
    console.log('onScrollToLower');
    if (moreLoading || noMore) {
      return;
    }
    fetchData(response.page + 1);
  }, [moreLoading, response.page]);

  return (
    <>
      <ScrollView
        style={{ height }}
        scrollY
        fastDeceleration
        scrollWithAnimation
        // 是否开启下拉刷新
        refresherEnabled={refresherEnabled && !moreLoading}
        // 下拉刷新的状态
        refresherTriggered={refreshLoading}
        // 下拉刷新
        onRefresherRefresh={handleRefresherRefresh}
        // 终止下拉刷新
        onRefresherAbort={handleRefresherAbort}
        // 滚动到底部
        onScrollToLower={handleScrollToLower}
      >
        {response.rows.map((row, index) => (
          <>{renderItem(row, index)}</>
        ))}
        {!(response.rows.length === 0 && response.page === 1) && (
          <AtLoadMore
            moreBtnStyle={{ border: 'none', color: '#999' }}
            status={atLoadMoreStatus}
            onClick={handleScrollToLower}
            moreText={moreText}
          />
        )}
      </ScrollView>
      {!loading && response.rows.length === 0 && response.page === 1 && (
        <Result text={emptyText} />
      )}
    </>
  );
}

type RefListWrapper = <T extends object = any, P extends object = any>(
  props: React.PropsWithChildren<ListWrapperProps<T, P>> & {
    ref?: React.Ref<ListWrapperHandler>;
  },
) => React.ReactElement;

export default forwardRef(ListWrapper) as RefListWrapper;
