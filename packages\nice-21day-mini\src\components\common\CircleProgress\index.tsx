import { uuid } from '@nice-people/nice-21day-shared';
import { Canvas } from '@tarojs/components';
import { createSelectorQuery, getSystemInfoSync, nextTick } from '@tarojs/taro';
import { memo, useEffect, useState } from 'react';
import { drawCircle } from './draw';

interface CircleProgressProps {
  /** 渲染模式 */
  renderer?: 'canvas' | 'div';
  /** 进度条样式 */
  type?: 'circle';
  /** 百分比进度 */
  percent?: number;
  /** 半径 */
  r?: number;
}

function CircleProgress({
  renderer = 'canvas',
  type = 'circle',
  percent = 0,
  r,
}: CircleProgressProps) {
  const [id] = useState(() => uuid());

  useEffect(() => {
    if (renderer !== 'canvas') {
      return;
    }
    nextTick(() => {
      createSelectorQuery()
        .select(`#circle-progress-${id}`)
        .fields({
          node: true,
          size: true,
        })
        .exec((res) => {
          // 小程序 canvas 新版写法
          // https://developers.weixin.qq.com/miniprogram/dev/component/canvas.html
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          const dpr = getSystemInfoSync().pixelRatio;
          const originWidth = res[0].width;
          canvas.width = originWidth * dpr;
          canvas.height = res[0].height * dpr;
          ctx.scale(dpr, dpr);
          switch (type) {
            case 'circle': {
              return drawCircle(ctx, { width: originWidth, percent, r });
            }
          }
        });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderer, percent]);

  // TODO:
  if (renderer === 'div') {
    return null;
  }

  return (
    <Canvas
      id={`circle-progress-${id}`}
      className="absolute w-full h-full"
      type="2d"
    />
  );
}

export default memo(CircleProgress);
