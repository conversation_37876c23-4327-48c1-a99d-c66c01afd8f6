import { createAdmin, updateAdmin } from '@/services';
import { ProCard } from '@ant-design/pro-components';
import { EState, IAdmin } from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import { Button, Form, Input, message, Modal, Space, Switch } from 'antd';
import React, { useCallback, useState } from 'react';

const PASSWORD_REG =
  /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?.])\S*$/;

const LOGIN_NAME_REG = /^[a-zA-Z0-9_]{5,}$/;

interface IFormData
  extends Pick<
    IAdmin,
    'id' | 'login_name' | 'nick_name' | 'password' | 'state'
  > {
  confirmword: string;
}

interface IAdminFormProps {
  admin?: IAdmin;
}
const AdminForm: React.FC<IAdminFormProps> = ({ admin }: IAdminFormProps) => {
  const [submiting, setSubmiting] = useState(false);

  const handleFinish = useCallback(
    async (values: IFormData) => {
      Modal.confirm({
        title: '确定保存吗？',
        onOk: () => {
          const data: IAdmin = {
            ...values,
            state: values.state ? EState.Enable : EState.Disable,
          };
          Reflect.deleteProperty(data, 'confirmword');
          setSubmiting(true);

          (admin?.id ? updateAdmin(data) : createAdmin(data))
            .then(() => {
              message.success('保存成功');
              history.back();
            })
            .finally(() => setSubmiting(false));
        },
      });
    },
    [admin?.id],
  );

  return (
    <ProCard direction="column">
      <Form<IFormData>
        initialValues={{
          ...(admin || {}),
          state: admin?.id ? admin?.state === EState.Enable : true,
        }}
        autoComplete="off"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 14 }}
        onFinish={handleFinish}
      >
        <Form.Item label="id" name="id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          label="登录名"
          name="login_name"
          validateFirst
          rules={[
            { required: true, message: '请输入登录名' },
            {
              validator: async (_, value) => {
                if (!value) {
                  return Promise.reject(new Error('请输入登录名'));
                }
                if (!LOGIN_NAME_REG.test(value)) {
                  return Promise.reject(
                    new Error('最少5位，只能包含大小写英文字母、数字和下划线'),
                  );
                }
              },
            },
          ]}
        >
          <Input placeholder="请输入" maxLength={32} />
        </Form.Item>
        <Form.Item
          label="昵称"
          name="nick_name"
          rules={[{ required: true, message: '请输入昵称' }]}
        >
          <Input
            maxLength={32}
            autoComplete="new-password"
            placeholder="请输入"
          />
        </Form.Item>

        <Form.Item
          label="密码"
          name="password"
          validateFirst
          help={admin?.id ? '不填写表示不修改' : undefined}
          rules={[
            { required: !admin?.id, message: '请输入密码' },
            {
              validator: async (_, value) => {
                // 编辑状态下，允许没有密码
                // 没有密码表示不修改
                if (admin?.id && !value) {
                  return Promise.resolve();
                }
                if (!PASSWORD_REG.test(value)) {
                  return Promise.reject(
                    new Error(
                      '最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符(!@#$%^&*?.)',
                    ),
                  );
                }
              },
            },
          ]}
        >
          <Input.Password maxLength={32} placeholder="请输入" />
        </Form.Item>
        <Form.Item
          label="确认密码"
          name="comfirmword"
          validateFirst
          rules={[
            { required: !admin?.id, message: '请输入密码' },
            ({ getFieldValue }) => ({
              validator: async (_, value) => {
                const pwd = getFieldValue('password');
                if (admin?.id && !value && !pwd) {
                  return Promise.resolve();
                }
                if (value && getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入密码不一致!'));
              },
            }),
          ]}
        >
          <Input.Password
            maxLength={32}
            autoComplete="new-password"
            placeholder="请输入"
          />
        </Form.Item>

        <Form.Item label="开启状态" name="state" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item
          label="备注信息"
          name="description"
          rules={[{ required: false, message: '请输入备注信息' }]}
        >
          <Input.TextArea maxLength={256} />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4 }}>
          <Space>
            <Button type="primary" htmlType="submit" loading={submiting}>
              保存
            </Button>
            <Button loading={submiting} onClick={() => history.back()}>
              返回
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default AdminForm;
