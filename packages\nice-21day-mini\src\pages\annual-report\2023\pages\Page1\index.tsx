import { ICurrentUser } from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import { IPageProps } from '../../type';
import styles from './index.module.scss';

interface ILandingProps {
  user: ICurrentUser;
  onClick: () => void;
}
export const Landing = ({ user, onClick }: ILandingProps) => {
  return (
    <View className={`${styles.container}`}>
      <View className="absolute z-10 left-40px top-160px">
        <View className={styles.title}>我的训练营</View>
        <View className={styles.year}>2023</View>
        <View className={styles.desc}>年度打卡报告</View>
      </View>

      <View className={styles.iconAirplane}>
        <Image
          src={require('./assets/<EMAIL>')}
          className="w-[148px] h-[70px]"
        />
      </View>

      <View className={styles.iconYear}>
        <Image
          src={require('./assets/<EMAIL>')}
          className="w-[208px] h-[208px]"
        />
      </View>

      <View className={styles.iconEnvelope}>
        <Image
          src={require('./assets/<EMAIL>')}
          className="w-[480px] h-[522px]"
        />
      </View>

      <View className={styles.iconCircle1}>
        <Image
          src={require('./assets/circle.svg')}
          className="w-[238px] h-[238px]"
        />
      </View>

      <View className={styles.iconCircle2}>
        <Image
          src={require('./assets/circle.svg')}
          className="w-[108px] h-[108px]"
        />
      </View>
      <View className={styles.iconCircle3}>
        <Image
          src={require('./assets/circle.svg')}
          className="w-[100px] h-[100px]"
        />
      </View>

      <View className={styles.userName}>@{user.nick_name}</View>

      <View className={styles.enterBtn} onClick={onClick}>
        开启报告
      </View>
    </View>
  );
};

export default ({ user, currentIdx, switchSwiper }: IPageProps) => {
  return <Landing user={user} onClick={() => switchSwiper(currentIdx + 1)} />;
};
