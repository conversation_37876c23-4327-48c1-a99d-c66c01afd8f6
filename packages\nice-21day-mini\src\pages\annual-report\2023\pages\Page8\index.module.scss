.container {
  background-image: url('./assets/background.svg');
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  :global {
    .report-2023__content,
    .report-2023__content--desc,
    .report-2023__highlight {
      color: #fc6538 !important;
    }
  }
}

// .love {
//   width: 748px;
//   height: 704px;
//   animation: beat 3s ease-in-out infinite;
// }

@keyframes beat {
  0% {
    width: 748px;
    height: 704px;
  }
  50% {
    width: 698px;
    height: 654px;
  }
  100% {
    width: 748px;
    height: 704px;
  }
}
