import request from '@/utils/request';
import { IUser, IWechatUser } from '@nice-people/nice-21day-shared';

/**
 * 获取我的个人信息
 */
export const getMyProfile = async () => {
  return await request.get<IUser>(`/my/profiles`);
};

/**
 * 修改我的个人信息
 */
export const updateMyProfile = async (params: IUser) => {
  return await request.put(`/my/profiles`, params);
};

/**
 * 修改我的微信头像和昵称
 */
export const updateWechatProfile = async (params: IWechatUser) => {
  return await request.put(`/my/wechat-profiles`, params);
};

/**
 * 修改我的 access token
 */
export const updateMyAccessToken = async () => {
  return await request.put<IUser>(`/my/access-token`, {});
};
