// NumberWrapper.tsx
import React, { useEffect, useState, useRef } from 'react';
import { View } from '@tarojs/components';
import { formatNumber } from '@nice-people/nice-21day-shared';

const ANIMATION_SPEED = {
  fast: 1,
  normal: 2,
  slow: 3,
};

interface NumberWrapperProps {
  target: number;
  active: boolean;
}

const NumberWrapper: React.FC<NumberWrapperProps> = ({ target, active }) => {
  const [current, setCurrent] = useState<number>(0);
  const currentRef = useRef<number>(0);

  useEffect(() => {
    currentRef.current = current;
  }, [current]);

  useEffect(() => {
    if (active) {
      startAnimation();
    } else {
      setCurrent(0);
    }
  }, [target, active]);

  const easeOut = (t: number) => 1 - Math.pow(1 - t, 3); // Cubic ease-out function

  const startAnimation = () => {
    let animationFrame: number;
    let startTime: number;
    const initial = currentRef.current; // Use the ref to get the latest value

    const animate = (timestamp: number) => {
      if (!startTime) {
        startTime = timestamp;
      }
      const animationSpped =
        target / 10 == 0 ? 'slow' : target / 10 < 90 ? 'normal' : 'fast';
      const progress =
        (timestamp - startTime) / (ANIMATION_SPEED[animationSpped] * 1000); // Convert to seconds

      setCurrent((prev) => {
        const newValue =
          prev < target
            ? Math.ceil(initial + (target - initial) * easeOut(progress))
            : target;
        return newValue;
      });

      if (currentRef.current < target) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animationFrame);
  };

  return <View className="number-wrapper">{formatNumber(current)}</View>;
};

export default NumberWrapper;
