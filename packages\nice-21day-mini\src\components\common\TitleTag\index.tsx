import { View } from '@tarojs/components';
import React from 'react';
import './index.scss';

export const themes = [
  '#D2ECD0',
  '#F3C1C0',
  '#E5F5F9',
  '#FDEEC4',
  '#EDDEFF',
  '#C0EDF3',
];

interface ITitleTagProps {
  theme?: string;
  title: string;
}
const TitleTag: React.FC<ITitleTagProps> = ({ theme = themes[0], title }) => {
  return (
    <View
      className="c_ttitle-tag"
      style={{
        background: `linear-gradient(0deg, ${theme} 0%, ${theme} 100%), lightgray 50% / cover no-repeat`,
      }}
    >
      <View className="c_ttitle-tag--title">{title}</View>
      <View className="c_ttitle-tag--icon"></View>
    </View>
  );
};

export default TitleTag;
