import { AppContext } from '@/appContext';
import {
  Card,
  ListWrapper,
  ListWrapperHandler,
  Result,
  RichText,
} from '@/components/common';
import {
  createAttendanceComment,
  queryAttendanceComments,
} from '@/service/comment';
import {
  getFileUrl,
  IComment,
  IQueryCommentParams,
} from '@nice-people/nice-21day-shared';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useCallback, useContext, useRef, useState } from 'react';
import { AtAvatar, AtButton, AtDivider, AtTextarea } from 'taro-ui';

interface IAttendanceCommentsProps {
  /** 打卡记录ID */
  attendanceLogId: string;
}
const AttendanceComments: React.FC<IAttendanceCommentsProps> = ({
  attendanceLogId,
}) => {
  const { currentUser } = useContext(AppContext);
  const ref = useRef<ListWrapperHandler>(null);
  const [commentText, setCommentText] = useState('');
  const [submitCommentLoading, setSubmitCommentLoading] = useState(false);

  const handleSubmitComment = useCallback(() => {
    if (!currentUser?.id) {
      Taro.showToast({
        title: `请登录`,
        icon: 'error',
        duration: 2000,
      });
      return;
    }

    if (!commentText) {
      Taro.showToast({
        title: `请输入评论内容`,
        icon: 'error',
        duration: 2000,
      });
      return;
    }

    if (submitCommentLoading) {
      return;
    }

    Taro.showModal({
      title: '提示',
      content: `确定提交评论吗？`,
      success: (res) => {
        if (res.confirm) {
          setSubmitCommentLoading(true);
          createAttendanceComment({
            target_id: attendanceLogId,
            content: commentText,
          })
            .then(() => {
              setCommentText('');
              Taro.showToast({
                title: `评论成功`,
                icon: 'success',
                duration: 2000,
              });
              ref.current?.refresh();
            })
            .catch((error) => {
              Taro.showToast({
                title: error?.data?.message ?? `评论失败`,
                icon: 'error',
                duration: 2000,
              });
            })
            .finally(() => {
              setSubmitCommentLoading(false);
            });
        }
      },
    });
  }, [currentUser.id, attendanceLogId, submitCommentLoading, commentText]);

  return (
    <View className="pb-[50px]">
      <AtDivider content="评论" />

      {/* 添加评论 */}
      <View className="mb-3">
        <Card title="评论">
          {currentUser?.id ? (
            <>
              <AtTextarea
                value={commentText}
                onChange={(value) => {
                  setCommentText(value);
                }}
                height={200}
                maxLength={1000}
                placeholder="输入你的评论..."
              />
              <View className="mt-3">
                <AtButton
                  type="primary"
                  circle
                  disabled={commentText.trim().length === 0}
                  onClick={handleSubmitComment}
                  loading={submitCommentLoading}
                >
                  <View className="flex items-center gap-1">提交评论</View>
                </AtButton>
              </View>
            </>
          ) : (
            <>
              <Result text="请登录后评论" className="!my-[20px]" />
              <AtButton
                type="primary"
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pages/login/index',
                  });
                }}
              >
                登录
              </AtButton>
            </>
          )}
        </Card>
      </View>

      <ListWrapper<IComment, IQueryCommentParams>
        ref={ref}
        height="100%"
        requestFn={queryAttendanceComments}
        refresherEnabled={false}
        params={{
          target_id: attendanceLogId,
          page: 1,
          size: 20,
        }}
        emptyText="还没有评论"
        moreText="查看更多评论"
        renderItem={(comment) => (
          <Card
            key={comment.id}
            styles={{
              boxShadow: 'none',
            }}
          >
            <View className="flex gap-2 items-center mb-2">
              <View
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/user-homepage/index?user_id=${comment.author_id}`,
                  });
                }}
              >
                <AtAvatar
                  size="small"
                  image={getFileUrl(comment.author?.avatar_url)}
                />
              </View>
              <View className="flex flex-col gap-1">
                <Text
                  className="text-[#333]"
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/pages/user-homepage/index?user_id=${comment.author_id}`,
                    });
                  }}
                >
                  {comment.author?.nick_name}
                </Text>
                <Text className="text-gray-500 text-[28px]">
                  {dayjs(comment.created_at).fromNow()}
                </Text>
              </View>
            </View>
            <View>
              <RichText content={comment.content} />
            </View>
          </Card>
        )}
      />
    </View>
  );
};

export default AttendanceComments;
