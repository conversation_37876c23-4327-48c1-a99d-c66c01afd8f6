import { AppContext } from '@/appContext';
import { Card, Result } from '@/components/common';
import { useAttendanceLikes } from '@/hooks/useAttendanceLike';
import { createAttendanceLogLike, deleteAttendanceLogLike } from '@/service';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useContext, useMemo } from 'react';
import { AtAvatar, AtButton, AtLoadMore } from 'taro-ui';

interface IAttendanceLikeProps {
  /** 打卡记录ID */
  attendanceLogId: string;
  /**
   * 用户ID，未来功能预留字段，目前不使用
   * @deprecated
   */
  userId?: string;
}
const AttendanceLike: React.FC<IAttendanceLikeProps> = ({
  attendanceLogId,
}) => {
  const { currentUser } = useContext(AppContext);
  const { loading, attendanceLogLikes, reload } =
    useAttendanceLikes(attendanceLogId);

  const iLiked = useMemo(
    () => attendanceLogLikes.find((el) => el.user_id === currentUser?.id),
    [currentUser, attendanceLogLikes],
  );

  const handleLike = useCallback(() => {
    const likeMsg = iLiked ? '取消点赞' : '点赞';

    Taro.showModal({
      title: '提示',
      content: `确定${likeMsg}？`,
      success: (res) => {
        if (res.confirm) {
          (iLiked
            ? deleteAttendanceLogLike(iLiked.id)
            : createAttendanceLogLike({
                user_id: currentUser.id,
                attendance_log_id: attendanceLogId,
              })
          )
            .then(() => {
              reload();
              Taro.showToast({
                title: `${likeMsg}成功`,
                icon: 'success',
                duration: 2000,
              });
            })
            .catch((error) => {
              Taro.showToast({
                title: error?.data?.message ?? `${likeMsg}失败`,
                icon: 'error',
                duration: 2000,
              });
            });
        }
      },
    });
  }, [iLiked, currentUser.id, attendanceLogId, reload]);

  const renderExtra = useCallback(() => {
    if (!currentUser?.id) {
      return <></>;
    }

    if (loading) {
      return <></>;
    }
    if (iLiked) {
      return (
        <AtButton size="small" type="primary" onClick={handleLike}>
          <view
            className="at-icon at-icon-heart-2"
            style={{ color: 'var(--like-color)' }}
          />{' '}
          已点赞
        </AtButton>
      );
    }
    return (
      <AtButton size="small" onClick={handleLike}>
        <view
          className="at-icon at-icon-heart-2"
          style={{ color: 'var(--like-color)' }}
        />{' '}
        点赞
      </AtButton>
    );
  }, [loading, iLiked, handleLike, currentUser?.id]);

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <View className="pb-[50px]">
      <Card
        title={`点赞列表（${attendanceLogLikes.length}）`}
        extra={renderExtra()}
      >
        {attendanceLogLikes.length > 0 ? (
          <View className="flex gap-[10px] flex-wrap justify-between">
            {attendanceLogLikes.map((like) => (
              <View
                key={like.id}
                className="flex justify-left items-center w-[calc(50%-10px)] gap-[8px]"
                onClick={() => {
                  const userId = like.user?.id;
                  if (userId) {
                    Taro.navigateTo({
                      url: `/pages/user-homepage/index?user_id=${userId}`,
                    });
                  }
                }}
              >
                <AtAvatar
                  size="small"
                  image={getFileUrl(like.user?.avatar_url)}
                  className="flex-shrink-0"
                />
                <View className="line-clamp-2 text-sm">
                  {like.user?.nick_name || '未知用户' || like.user_id}
                </View>
              </View>
            ))}
          </View>
        ) : (
          <Result
            text={
              <>
                <View>还没有收获点赞呢</View>
                <View>快去送出第一个赞吧</View>
              </>
            }
          />
        )}
      </Card>
    </View>
  );
};

export default AttendanceLike;
