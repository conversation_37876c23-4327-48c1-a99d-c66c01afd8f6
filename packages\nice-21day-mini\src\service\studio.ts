import request from '@/utils/request';
import {
  ICreateStudioFormData,
  IPageFactory,
  IQueryStudioParams,
  IRecommendStudioParams,
  IStudio,
  IUpdateStudioFormData,
} from '@nice-people/nice-21day-shared';

/**
 * 获取声音创作列表（分页）
 */
export const getStudios = async (params: IQueryStudioParams) => {
  return await request.get<IPageFactory<IStudio>>('/studios', params);
};

/**
 * 随机一条声音创作详情
 */
export const getStudioDetail = async (studioId: string) => {
  return await request.get<IStudio>(`/studios/${studioId}`, {});
};

/**
 * 随机推荐声音创作
 */
export const recommentStudios = async (params: IRecommendStudioParams) => {
  return await request.get<IStudio[]>('/studios/recommends', params);
};

/**
 * 新增声音创作
 */
export const createStudio = async (data: ICreateStudioFormData) => {
  return await request.post<IStudio>('/studios', data);
};

/**
 * 编辑声音创作
 */
export const updateStudio = async (
  studioId: string,
  data: Partial<IUpdateStudioFormData>,
) => {
  return await request.put<IStudio>(`/studios/${studioId}`, data);
};

/**
 * 删除声音创作
 */
export const deleteStudio = async (studioId: string) => {
  return await request.delete(`/studios/${studioId}`, {});
};
