import { deleteAdmin, queryAllAdmins } from '@/services';
import { generateProTableValueEnum } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { IAdmin, STATE_MAPPING } from '@nice-people/nice-21day-shared';
import { history, useModel } from '@umijs/max';
import { Button, Popconfirm, Space } from 'antd';
import React, { useRef } from 'react';

const Admin: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');

  const handleDeletAdmin = (id: string) => {
    deleteAdmin(id).then(() => {
      tableRef.current?.reload();
    });
  };
  const columns: ProColumns<IAdmin>[] = [
    {
      title: '登录名',
      dataIndex: 'login_name',
    },
    {
      title: '昵称',
      dataIndex: 'nick_name',
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 140,
      valueEnum: generateProTableValueEnum(STATE_MAPPING),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 140,
      align: 'center',
      render: (_, item) => (
        <Space>
          <Button
            type="link"
            onClick={() => history.push(`/admin/admin/${item.id}/update`)}
          >
            编辑
          </Button>
          {/* 自己不可以删除自己 */}
          <Popconfirm
            disabled={item.id === initialState?.currentUser?.id}
            title="确定要删除吗？"
            onConfirm={() => handleDeletAdmin(item.id)}
          >
            <Button
              type="link"
              disabled={item.id === initialState?.currentUser?.id}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <ProTable<IAdmin>
      actionRef={tableRef}
      rowKey="id"
      size="small"
      bordered
      pagination={false}
      request={async () => {
        const admins = await queryAllAdmins();
        return {
          success: true,
          data: admins,
        };
      }}
      columns={columns}
      search={false}
      headerTitle="管理员列表"
      toolBarRender={() => [
        <Button
          key="button"
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => history.push('/admin/admin/create')}
        >
          新建
        </Button>,
      ]}
    />
  );
};

export default Admin;
