import { ListWrapper } from '@/components';
import { getUsers } from '@/service';
import { getFileUrl } from '@/utils';
import { IQueryUserParams, IUser } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtListItem } from 'taro-ui';

const UserList: React.FC = () => {
  return (
    <View>
      <ListWrapper<IUser, IQueryUserParams>
        height="100%"
        requestFn={getUsers}
        params={{
          page: 1,
          size: 20,
        }}
        renderItem={(record) => (
          <AtListItem
            key={record.id}
            title={record.nick_name}
            thumb={getFileUrl(record.avatar_url)}
            arrow="right"
            onClick={() =>
              Taro.navigateTo({
                url: `/pages/user-homepage/index?user_id=${record.id}`,
              })
            }
          />
        )}
      />
    </View>
  );
};

export default UserList;
