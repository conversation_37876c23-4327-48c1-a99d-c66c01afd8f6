{"name": "@nice-people/nice-21day-mini", "version": "1.0.0", "private": true, "description": "nice-21day-mini", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@antv/f2": "^4.0.42", "@babel/runtime": "^7.21.5", "@nice-people/nice-21day-shared": "workspace:^1.0.0", "@tarojs/cli": "4.1.1", "@tarojs/components": "4.1.1", "@tarojs/helper": "4.1.1", "@tarojs/plugin-framework-react": "4.1.1", "@tarojs/plugin-platform-weapp": "4.1.1", "@tarojs/react": "4.1.1", "@tarojs/runtime": "4.1.1", "@tarojs/shared": "4.1.1", "@tarojs/taro": "4.1.1", "@jtudev/taro-parse": "1.0.8", "custom-calendar-taro": "^1.0.12", "dayjs": "^1.11.4", "lodash.debounce": "^4.0.8", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-f2-react": "^1.1.1", "taro-ui": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.24.4", "@dcasia/mini-program-tailwind-webpack-plugin": "^1.5.6", "@tarojs/webpack5-runner": "4.1.1", "@types/lodash.debounce": "^4.0.7", "@types/react": "^18.0.0", "@tarojs/taro-loader": "4.1.1", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "less": "^4.2.0", "babel-preset-taro": "4.1.1", "eslint-config-taro": "4.1.1", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "react-refresh": "^0.11.0", "postcss": "^8.4.38", "@types/node": "^18", "windicss": "^3.5.6", "webpack": "5.91.0"}}