import {
  CaretRightOutlined,
  CarryOutOutlined,
  CloseCircleOutlined,
  CommentOutlined,
  FlagOutlined,
  LikeOutlined,
  RightOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import {
  computedAttendanceLogScore,
  EAttendanceState,
  filterFiles,
  getFileUrl,
  IAttendanceLog,
  IAttendanceTask,
  ITraining,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { history, Link } from '@umijs/max';
import {
  Avatar,
  Card,
  Collapse,
  Empty,
  Image,
  List,
  ListProps,
  Space,
  Tag,
} from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useMemo } from 'react';
import IconText from '../IconText';
import styles from './index.module.less';

import { ReactComponent as CircleCheckIcon } from '@/assets/svg/circle-check.svg';
import SubmitAttendance from '../SubmitAttendance';

const getActions = (attendance: IAttendanceLog) => [
  <IconText
    icon={LikeOutlined}
    text={String(attendance.like_count || 0)}
    key="list-vertical-like-o"
  />,
  <IconText
    icon={CommentOutlined}
    text={String(attendance.comment_count || 0)}
    key="list-vertical-comment-o"
  />,
  <IconText
    icon={CarryOutOutlined}
    text={`${dayjs(attendance.attendance_date).format('YYYY-MM-DD')}`}
    key="list-vertical-date-o"
  />,
  <IconText
    icon={TrophyOutlined}
    text={`积分 + ${computedAttendanceLogScore(attendance)}`}
    key="list-vertical-trophy"
  />,
];

const getAttendanceTasks = (attendance: IAttendanceLog) => {
  return parseArrayJson<IAttendanceTask>(attendance.attendance_tasks).filter(
    (task) =>
      task.attendance_content || (task.attendance_files || [])?.length > 0,
  );
};

const RenderAttendanceContent = ({
  attendance,
}: {
  attendance: IAttendanceLog;
}) => (
  <Space direction="vertical" className="w-full select-none">
    {getAttendanceTasks(attendance).map((task, idx) => {
      const { imageList } = filterFiles(
        (task.attendance_files || [])
          .filter(Boolean)
          .map((file) => getFileUrl(file)),
      );

      return (
        <div
          key={task.id}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <Collapse
            className={styles.collapse}
            defaultActiveKey={['1']}
            bordered={false}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            items={[
              {
                key: '1',
                showArrow: false,
                className: 'border-l-4 border-green-500 !rounded-2xl shadow-sm',
                styles: {
                  header: {
                    paddingLeft: 12,
                  },
                  body: {
                    paddingTop: 8,
                    paddingLeft: 12,
                  },
                },
                label: (
                  <div className="flex gap-1">
                    <div className="w-6 h-6 rounded-xl bg-blue-100 flex items-center justify-center flex-shrink-0">
                      <FlagOutlined className="text-blue-500" />
                    </div>
                    {`任务${idx + 1}: ` + task.name}
                  </div>
                ),
                children: (
                  <div>
                    <div className="whitespace-pre-line break-all">
                      {task.attendance_content}
                    </div>
                    {/* 附件 */}
                    {imageList.length > 0 && (
                      <Image.PreviewGroup>
                        <Space className="mt-2">
                          {imageList.map((img) => (
                            <Image
                              className="rounded-md"
                              key={img}
                              src={img}
                              height={100}
                              width={100}
                            />
                          ))}
                        </Space>
                      </Image.PreviewGroup>
                    )}
                  </div>
                ),
              },
            ]}
          />
        </div>
      );
    })}
  </Space>
);

// 训练营维度的 timeline
interface ITrainingAttendanceTimelineProps
  extends Omit<IProps, 'training' | 'scene'> {
  /** 显示详情按钮 */
  showDetailButton?: boolean;
}
export const TrainingAttendanceTimeline = ({
  loading,
  list,
  pagination,
  showDetailButton = true,
}: ITrainingAttendanceTimelineProps) => (
  <List
    className={styles.list}
    itemLayout="vertical"
    size="large"
    loading={loading}
    dataSource={list || []}
    pagination={pagination}
    locale={{
      emptyText: (
        <Card>
          <Empty />
        </Card>
      ),
    }}
    renderItem={(attendance) => (
      <Card
        className="mb-[10px]"
        styles={{
          body: {
            padding: 0,
          },
        }}
      >
        <List.Item
          key={attendance.id}
          className="!p-3"
          styles={{
            actions: {
              marginInlineStart: 4,
            },
          }}
          actions={getActions(attendance)}
        >
          <List.Item.Meta
            className={styles.meta}
            avatar={
              <Link to={`/user/${attendance.user_id}`}>
                <Avatar src={getFileUrl(attendance.user?.avatar_url)} />
              </Link>
            }
            title={
              <div className="flex justify-between items-center pr-2">
                <Link
                  className="flex items-center"
                  to={`/training/${attendance.training_id}/user/${attendance.user_id}/attendance-timeline`}
                >
                  {attendance.user?.nick_name}{' '}
                </Link>
                {showDetailButton && (
                  <div
                    className="text-gray-500 text-sm cursor-pointer flex-1 text-right"
                    onClick={() => {
                      history.push(
                        `/training/${attendance.training_id}/user/${attendance.user_id}/attendance/${attendance.id}`,
                      );
                    }}
                  >
                    <RightOutlined className="text-lg" />
                  </div>
                )}
              </div>
            }
          />

          {attendance.attendance_state === EAttendanceState.Attendance ? (
            <RenderAttendanceContent attendance={attendance} />
          ) : (
            <div>请假：{attendance.description}</div>
          )}
        </List.Item>
      </Card>
    )}
  />
);

// 用户角度的 timeline
type ITrainingUserAttendanceTimelineProps = Omit<IProps, 'scene'>;
export const TrainingUserAttendanceTimeline = ({
  training,
  loading,
  list = [],
  pagination,
  onRefresh,
}: ITrainingUserAttendanceTimelineProps) => {
  // 把 list 根据日期进行分组，用 lodash 实现
  const listGroupByDate = useMemo(() => {
    return _.groupBy(list, (item) =>
      dayjs(item.attendance_date).format('YYYY-MM-DD'),
    );
  }, [list]);

  // 根据训练营 start_time 和 end_time 生成日期列表
  // 从 start_time 到 当前日期
  const trainingDateList = useMemo(() => {
    const start = dayjs(training.start_time);
    // 截止到今日或训练营结束时间
    const end = dayjs(training.end_time).isAfter(dayjs())
      ? dayjs()
      : dayjs(training.end_time);
    const dateList = [];
    let current = start;
    while (current.isBefore(end) || current.isSame(end)) {
      dateList.push(current.format('YYYY-MM-DD'));
      current = current.add(1, 'day');
    }
    // 时间倒序
    return dateList.reverse();
  }, [training]);

  return (
    <List
      className={styles.list}
      itemLayout="vertical"
      size="large"
      loading={loading}
      dataSource={trainingDateList || []}
      pagination={pagination}
      locale={{
        emptyText: (
          <Card>
            <Empty />
          </Card>
        ),
      }}
      renderItem={(attendanceDate) => {
        const attendance = listGroupByDate[attendanceDate]?.[0];

        return (
          <Card
            className="mb-[10px]"
            styles={{
              body: {
                padding: 0,
              },
            }}
            onClick={() => {
              if (attendance) {
                history.push(
                  `/training/${attendance.training_id}/user/${attendance.user_id}/attendance/${attendance.id}`,
                );
              }
            }}
          >
            <List.Item
              key={attendance?.id || attendanceDate}
              className="!p-3"
              styles={{
                actions: {
                  marginInlineStart: 4,
                },
              }}
              actions={attendance ? getActions(attendance) : []}
            >
              <List.Item.Meta
                className={styles.meta}
                avatar={
                  attendance ? (
                    <CircleCheckIcon className="text-[24px] text-green-500" />
                  ) : (
                    <CloseCircleOutlined className="text-[24px] text-red-500" />
                  )
                }
                title={
                  <div className="flex justify-between">
                    <div>
                      <div className="font-medium">
                        {/* 计算第几天：attendance_date 和训练营开始时间的差值 */}
                        第
                        {dayjs(attendanceDate).diff(
                          dayjs(training.start_time),
                          'day',
                        ) + 1}
                        天
                      </div>
                      <div className="text-sm text-gray-500">
                        <CarryOutOutlined className="mr-1" />
                        {attendanceDate}
                      </div>
                    </div>
                    <div>
                      <SubmitAttendance
                        attendanceDate={attendanceDate}
                        attendanceLog={attendance}
                        onFinish={() => {
                          onRefresh?.();
                        }}
                      />

                      {/* 已完成，未完成 */}
                      {attendance ? (
                        <>
                          {attendance?.attendance_state ===
                          EAttendanceState.Attendance ? (
                            <Tag color="success">已打卡</Tag>
                          ) : (
                            <Tag color="warning">请假</Tag>
                          )}
                        </>
                      ) : (
                        <Tag color="error">未打卡</Tag>
                      )}
                    </div>
                  </div>
                }
              />

              {attendance?.attendance_state === EAttendanceState.Leave && (
                <div>{attendance.description}</div>
              )}

              {attendance?.attendance_state === EAttendanceState.Attendance && (
                <RenderAttendanceContent attendance={attendance} />
              )}
            </List.Item>
          </Card>
        );
      }}
    />
  );
};

interface IProps extends Pick<ListProps<IAttendanceLog>, 'pagination'> {
  training: ITraining;
  loading?: boolean;
  list: IAttendanceLog[];
  scene: 'training-detail' | 'training-user-detail';
  onRefresh?: () => void;
}
export default (props: IProps) => {
  const { scene } = props;
  // 用户打卡记录
  if (scene === 'training-user-detail') {
    return <TrainingUserAttendanceTimeline {...props} />;
  }

  // 训练营按时间排序的打卡记录
  return <TrainingAttendanceTimeline {...props} />;
};
