import { uuid } from '@nice-people/nice-21day-shared';
import { Image, View } from '@tarojs/components';
import { memo, useEffect, useState } from 'react';
import { wordList } from './contant';

const RandomKeyword = () => {
  const [randomKey, setRandomKey] = useState<string>();
  useEffect(() => {
    const timer = setInterval(() => {
      setRandomKey(uuid());
    }, 2000);
    return () => clearInterval(timer);
  }, [randomKey]);

  const getRandomKeyword = () => {
    const randomIndex = Math.floor(Math.random() * wordList.length);
    return wordList[randomIndex];
  };

  return (
    <View className="relative h-[calc(100%-640rpx)] w-[90%] mx-auto">
      <View
        className="keyword-btn-text top-54px left-[50%]"
        style={{ transform: 'translateX(-50%)' }}
      >
        <Image
          className="w-458px h-196px"
          src={require('./assets/btn_bg_blue.png')}
        />
        <View key={`${randomKey}_1}`} className="keyword-text text-38px">
          “{getRandomKeyword()}”
        </View>
      </View>
      <View
        className="keyword-btn-text top-256px left-[50%]"
        style={{ transform: 'translateX(-30%)' }}
      >
        <Image
          className="w-494px h-182px"
          src={require('./assets/btn_bg_yellow.png')}
        />
        <View key={`${randomKey}_2}`} className="keyword-text text-38px">
          “{getRandomKeyword()}”
        </View>
      </View>
      <View
        className="keyword-btn-text top-432px left-[50%]"
        style={{ transform: 'translateX(-70%)' }}
      >
        <Image
          className="w-430px h-208px"
          src={require('./assets/btn_bg_green.png')}
        />
        <View key={`${randomKey}_3}`} className="keyword-text text-38px">
          “{getRandomKeyword()}”
        </View>
      </View>

      <View className="keyword-btn-text top-390px right-18px !z-2">
        <Image
          className="w-240px h-86px"
          src={require('./assets/btn_bg_blue_opacity.png')}
        />
        <View key={`${randomKey}_4}`} className="keyword-text">
          {getRandomKeyword()}
        </View>
      </View>

      {/* 透明度更高的文字 */}
      <View className="keyword-btn-text top-230px left-128px !z-2">
        <Image
          className="w-196px h-60px"
          src={require('./assets/btn_bg_blue_opacity_2.png')}
        />
        <View key={`${randomKey}_5}`} className="keyword-text text-24px">
          {getRandomKeyword()}
        </View>
      </View>
      <View className="keyword-btn-text top-0px left-40px !z-2">
        <Image
          className="w-196px h-60px"
          src={require('./assets/btn_bg_blue_opacity_2.png')}
        />
        <View key={`${randomKey}_6}`} className="keyword-text text-24px">
          {getRandomKeyword()}
        </View>
      </View>
      <View className="keyword-btn-text top-10px right-40px !z-2 opacity-70">
        <Image
          className="w-196px h-60px"
          src={require('./assets/btn_bg_blue_opacity_2.png')}
        />
        <View key={`${randomKey}_7}`} className="keyword-text text-24px">
          {getRandomKeyword()}
        </View>
      </View>

      <View className="keyword-btn-text top-184px right-80px !z-2 opacity-70">
        <Image
          className="w-196px h-60px"
          src={require('./assets/btn_bg_blue_opacity_2.png')}
        />
        <View key={`${randomKey}_8}`} className="keyword-text text-24px">
          {getRandomKeyword()}
        </View>
      </View>
      <View className="keyword-btn-text bottom-140px left-40px !z-2 opacity-40">
        <Image
          className="w-240px h-86px"
          src={require('./assets/btn_bg_blue_opacity.png')}
        />
        <View key={`${randomKey}_9}`} className="keyword-text">
          {getRandomKeyword()}
        </View>
      </View>
      <View className="keyword-btn-text bottom-120px right-80px !z-2 opacity-40">
        <Image
          className="w-196px h-60px"
          src={require('./assets/btn_bg_blue_opacity_2.png')}
        />
        <View key={`${randomKey}_10`} className="keyword-text text-24px">
          {getRandomKeyword()}
        </View>
      </View>
      <View className="keyword-btn-text bottom-340px left-40px !z-2 opacity-80">
        <Image
          className="w-240px h-86px"
          src={require('./assets/btn_bg_blue_opacity.png')}
        />
        <View key={`${randomKey}_11}`} className="keyword-text">
          {getRandomKeyword()}
        </View>
      </View>
    </View>
  );
};

export default memo(RandomKeyword);
