{"name": "@nice-people/nice-21day-web", "version": "1.0.0", "private": true, "scripts": {"dev": "max dev", "build": "max build", "postinstall": "max setup", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^6.0.0", "@ant-design/compatible": "5.1.4", "@ant-design/pro-components": "^2.8.7", "@nice-people/nice-21day-shared": "workspace:^1.0.0", "@umijs/max": "^4.0.6", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "@wangeditor/plugin-md": "1.0.0", "ahooks": "^3.8.5", "antd": "^5.25.3", "clipboard": "^2.0.11", "dayjs": "^1.11.4", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "rc-picker": "^2.6.10", "umi-request": "^1.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/clipboard": "^2.0.7", "@types/lodash": "^4.14.184", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "prettier-plugin-organize-imports": "^2", "prettier-plugin-packagejson": "^2", "tailwindcss": "^3"}}