/**
 * 录音组件
 * 1. 根据当前是否录音完成，切换录音和播放音频
 * 2. 录音完成后上传服务器，并emit出去
 * 3. 可以删除当前录音
 */
import { FloatLayout } from '@/components';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useMemo, useState } from 'react';

import { AtButton } from 'taro-ui';
import AudioPlayer from '../AudioPlayer';
import Recorder from '../Recorder';

interface IRecordAudioProps {
  /** 录音最大数量 */
  limit?: number;
  /** 已有录音列表 */
  files?: string[];
  /** 是否为只读状态 */
  readonly?: boolean;
  /** 录音完成后的文件地址 */
  onSuccess?: (audioPath: string) => void;
  /** 删除一个录音 */
  onRemove?: (audioPath: string) => void;
}
const RecordAudio: React.FC<IRecordAudioProps> = ({
  limit = 1,
  readonly = false,
  files = [],
  onSuccess,
  onRemove,
  children,
}) => {
  console.log('files', files);
  // @see: https://developers.weixin.qq.com/miniprogram/dev/api/base/system/wx.getSystemInfo.html
  const { platform } = Taro.getSystemInfoSync();
  // 录音弹出框是否显示
  const [floatLayoutOpen, setFloatLayoutOpen] = useState<boolean>(false);

  const removeAudio = (audioPath: string) => {
    Taro.showModal({
      title: '提示',
      content: '确认删除此录音？',
      success: function (res) {
        if (res.confirm) {
          // 删除
          onRemove?.(audioPath);
        }
      },
    });
  };

  const isMobile = useMemo(() => {
    // 	Windows微信 和 macos 微信 不支持录音，麻烦
    return platform !== 'mac' && platform !== 'windows';
  }, [platform]);

  return (
    <View>
      {files.length < limit && !readonly && isMobile && (
        <View
          onClick={() => {
            setFloatLayoutOpen(true);
          }}
        >
          {children || (
            <AtButton size="small">
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-sound"></View>
                录制声音
              </View>
            </AtButton>
          )}
        </View>
      )}

      {files.map((audioPath) => (
        <View
          key={audioPath}
          className="flex h-100rpx flex-nowrap items-center gap-3"
        >
          <AudioPlayer key={audioPath} src={getFileUrl(audioPath)} />
          {/* 删除此次录音 */}
          {!readonly && (
            <View
              className="at-icon at-icon-trash text-lg text-neutral-500"
              onClick={() => removeAudio(audioPath)}
            />
          )}
        </View>
      ))}

      {/* 录音弹层 */}
      {floatLayoutOpen && (
        <FloatLayout
          isOpened={floatLayoutOpen}
          showClose={false}
          title="录制声音"
          onClose={() => setFloatLayoutOpen(true)}
        >
          <View className="flex h-full w-full items-end h-484px">
            <Recorder
              onFinish={(src) => {
                onSuccess?.(src);
                setFloatLayoutOpen(false);
              }}
              onCancel={() => {
                setFloatLayoutOpen(false);
              }}
            />
          </View>
        </FloatLayout>
      )}
    </View>
  );
};

export default RecordAudio;
