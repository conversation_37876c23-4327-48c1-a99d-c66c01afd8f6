import { FabBox, Result } from '@/components';
import { useTrainingUser } from '@/hooks';
import { getFileUrl, parseArrayJson } from '@nice-people/nice-21day-shared';
import { Canvas, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useCallback, useEffect } from 'react';
import { AtButton, AtLoadMore } from 'taro-ui';
import { downImage, drawRadiusRect, drawSingleText, toPx } from './helper';

// 证书模板的地址
const TEMPLATE_IMAGE_URL =
  'https://oss.yayujs.com/alioss/2023-04-03/44273ffb-2e45-4abe-a81e-1a192d9a4d76.jpeg';
const CANVAS_ID = 'Certificate_Canvas';

// 模板图片的宽高比
const TEMPLATE_IMAGE_RATIO = 1242 / 2208;
// 模板的宽度
const TEMPLATE_IMAGE_WIDTH = 750 / 2;
// 模板的高度
const TEMPLATE_IMAGE_HEIGHT = TEMPLATE_IMAGE_WIDTH / TEMPLATE_IMAGE_RATIO;
// 头像的宽度
const AVATAR_WIDTH = 100;
// 头像的高度
const AVATAR_HEIGHT = 100;
// 头像的 x 偏移
const AVATAR_X = 134;
// 头像的 y 偏移
const AVATAR_Y = 292;
// 画布中间 x 偏移
const CENTER_X = TEMPLATE_IMAGE_WIDTH / 2;
// 训练营第几期文字
const TRAINING_TITLE_Y = 96;

interface ICertificateProps {
  training_id: string;
  user_id: string;
}

const Certificate: React.FC<ICertificateProps> = ({ training_id, user_id }) => {
  const { trainingUserDetail, queryTrainingUserDetailLoading } =
    useTrainingUser(training_id, user_id);

  const draw = useCallback(async () => {
    if (!trainingUserDetail?.training || !trainingUserDetail?.user) {
      return;
    }

    const taskCount = parseArrayJson(trainingUserDetail.tasks).length;

    const ctx = Taro.createCanvasContext(CANVAS_ID);
    const tempPath = await downImage(TEMPLATE_IMAGE_URL);
    ctx.drawImage(
      tempPath,
      0,
      0,
      toPx(TEMPLATE_IMAGE_WIDTH),
      toPx(TEMPLATE_IMAGE_HEIGHT),
    );
    ctx.save();

    // 画头像
    drawRadiusRect(
      ctx,
      AVATAR_X,
      AVATAR_Y,
      AVATAR_WIDTH,
      AVATAR_HEIGHT,
      AVATAR_WIDTH,
    );
    const avatarTempPath = await downImage(
      getFileUrl(trainingUserDetail.user?.avatar_url),
    );
    ctx.drawImage(
      avatarTempPath,
      AVATAR_X,
      AVATAR_Y,
      AVATAR_WIDTH,
      AVATAR_HEIGHT,
    );
    // 恢复上下文
    ctx.restore();

    // 画昵称
    drawSingleText({
      ctx,
      x: CENTER_X,
      y: AVATAR_Y + AVATAR_HEIGHT + 28,
      text: trainingUserDetail.user?.nick_name,
    });

    // 训练营第几期
    drawSingleText({
      ctx,
      x: CENTER_X,
      y: TRAINING_TITLE_Y,
      text: String(`第 ${trainingUserDetail.training?.period || 0} 期`),
      fontSize: 18,
    });

    // 画打卡统计指标
    drawSingleText({
      ctx,
      x: (TEMPLATE_IMAGE_WIDTH - 5) / 2 - 85,
      y: 568,
      text: String(trainingUserDetail.total_attendance_count || 0),
    });

    drawSingleText({
      ctx,
      x: (TEMPLATE_IMAGE_WIDTH - 5) / 2,
      y: 568,
      text: String(taskCount),
    });

    drawSingleText({
      ctx,
      x: (TEMPLATE_IMAGE_WIDTH - 5) / 2 + 82,
      y: 568,
      text: String(trainingUserDetail.score),
    });

    ctx.draw();
  }, [trainingUserDetail]);

  useEffect(() => {
    draw();
  }, [draw]);

  const saveImage = () => {
    Taro.canvasToTempFilePath({
      canvasId: CANVAS_ID,
      success: function (res) {
        Taro.previewImage({
          current: res.tempFilePath,
          urls: [res.tempFilePath],
        });
      },
    });
  };

  if (queryTrainingUserDetailLoading) {
    return <AtLoadMore status="loading" />;
  }

  if (!trainingUserDetail.user) {
    return <Result text="报名用户不存在或已被删除" />;
  }

  if (!trainingUserDetail.training) {
    return <Result text="训练营不存在或已被删除" />;
  }

  return (
    <View className="pb-16 flex justify-center">
      <Canvas
        canvas-id={CANVAS_ID}
        id={CANVAS_ID}
        style={{
          width: TEMPLATE_IMAGE_WIDTH,
          height: TEMPLATE_IMAGE_HEIGHT,
        }}
      />

      {/* 生成图片 */}
      <FabBox>
        <AtButton type="primary" circle onClick={saveImage}>
          转发分享
        </AtButton>
      </FabBox>
    </View>
  );
};

export default Certificate;
