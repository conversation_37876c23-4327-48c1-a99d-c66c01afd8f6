import { AppContext } from '@/appContext';
import { ListWrapper, ListWrapperHandler, StudioItem } from '@/components';
import { useRouterParams, useUserProfile } from '@/hooks';
import { getStudios } from '@/service';
import { IQueryStudioParams, IStudio } from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import { useContext, useEffect, useRef } from 'react';

const UserStudioList: React.FC = () => {
  const { user_id } = useRouterParams();
  const ref = useRef<ListWrapperHandler>();
  const { currentUser } = useContext(AppContext);
  const { userProfile } = useUserProfile(user_id);

  useShareAppMessage(() => {
    return {
      title: `${userProfile?.nick_name}的声音作品集`,
      path: `/pages/studio-list-user/index?user_id=${user_id}`,
    };
  });

  useEffect(() => {
    if (userProfile?.nick_name) {
      Taro.setNavigationBarTitle({
        title: `${userProfile?.nick_name}的声音创作`,
      });
    }
  }, [userProfile?.nick_name]);

  return (
    <View className="p-2.5">
      <ListWrapper<IStudio, IQueryStudioParams>
        ref={ref}
        height="100%"
        requestFn={getStudios}
        refresherEnabled={false}
        params={{
          creator_id: user_id,
        }}
        emptyText="没有找到相关的声音创造数据"
        renderItem={(studio) => (
          <StudioItem
            studio={studio}
            currentUser={currentUser}
            onAvatarClick={() => {
              Taro.navigateTo({
                url: `/pages/user-homepage/index?user_id=${studio.user?.id}`,
              });
            }}
          />
        )}
      />
    </View>
  );
};

export default UserStudioList;
