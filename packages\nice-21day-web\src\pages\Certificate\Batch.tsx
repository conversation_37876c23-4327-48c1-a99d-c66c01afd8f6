import { readSheetFileJson } from '@/utils/sheetjs';
import {
  CoffeeOutlined,
  DownloadOutlined,
  EyeOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { ITraining, IUser, sleep } from '@nice-people/nice-21day-shared';
import { useModel } from '@umijs/max';
import { Button, Col, Form, message, Row, Select, Space, Table } from 'antd';
import Upload, {
  RcFile,
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from 'antd/lib/upload';
import html2canvas from 'html2canvas';
import { groupBy } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { REGISTRATION_FORM_COLUMN_KEY } from '../Training/RegistrationForm';
import CertificatePreview, {
  ICertificatePreviewProps,
  mockCertificateData,
} from './components/CertificatePreview';
import styles from './index.less';

interface ISheetDataItem {
  [REGISTRATION_FORM_COLUMN_KEY.uuid]: string;
  [REGISTRATION_FORM_COLUMN_KEY.nickName]: string;
  [REGISTRATION_FORM_COLUMN_KEY.totalPoints]: string;
  [REGISTRATION_FORM_COLUMN_KEY.clockDays]: string;
  [REGISTRATION_FORM_COLUMN_KEY.task]: string;
}

type ICertificateData = ICertificatePreviewProps;

const getSheetFileArrayBuffer = (file: RcFile): Promise<ArrayBuffer> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = (error) => reject(error);
  });

export default function BatchCreate() {
  // 当前选中的训练营
  const [trainingId, setTrainingId] = useState<string>();
  // 解析后的sheet数据
  const [certificateDataList, setCertificateDataList] = useState<
    ICertificateData[]
  >([]);
  // 当前证书预览的数据
  const [certificateData, setCertificateData] =
    useState<ICertificateData>(mockCertificateData);

  // 是否正在批量生成证书
  const [isBatchLoading, setIsBatchLoading] = useState(false);

  const {
    queryAllloading: queryTrainingLoading,
    allTrainings,
    queryAllTrainings,
  } = useModel('trainingModel');

  const {
    loading: queryUserLoading,
    allUsers,
    queryAllUsers,
  } = useModel('userModel');

  useEffect(() => {
    queryAllTrainings();
    queryAllUsers();
  }, []);

  const trainingInfo = useMemo(() => {
    return (allTrainings.find((el) => el.id === trainingId) ?? {}) as ITraining;
  }, [trainingId, allTrainings]);

  const beforeUpload = () => {
    return false;
  };

  const parseSheetJson = useCallback(
    (sheetJson: ISheetDataItem[]) => {
      const groupByUUID = groupBy(sheetJson, REGISTRATION_FORM_COLUMN_KEY.uuid);
      const _certificateDataList: ICertificateData[] = [];
      Object.keys(groupByUUID).forEach((userId) => {
        const userRows = groupByUUID[userId];
        const userInfo = allUsers.find((el) => el.id === userId) as IUser;
        _certificateDataList.push({
          user: userInfo,
          metrics: {
            taskCount: userRows.length,
            totalPoints: Number(
              userRows[0]?.[REGISTRATION_FORM_COLUMN_KEY.totalPoints] ?? 0,
            ),
            clockDays: Number(
              userRows[0]?.[REGISTRATION_FORM_COLUMN_KEY.clockDays] ?? 0,
            ),
          },
        });
      });

      setCertificateDataList(_certificateDataList);
    },
    [trainingInfo, allUsers],
  );

  const handleFileChange: UploadProps['onChange'] = async (
    info: UploadChangeParam<UploadFile<RcFile>>,
  ) => {
    // 解析 excel 文件
    if (!info.file || !info.fileList.length) {
      setCertificateDataList([]);
      return;
    }
    try {
      const arrayBuffer = await getSheetFileArrayBuffer(info.file as RcFile);
      const sheetJson = await readSheetFileJson<ISheetDataItem>(arrayBuffer);
      // 将用户的信息解析出来
      parseSheetJson(sheetJson);
      //
    } catch (error) {
      message.error('解析 excel 文件错误');
      setCertificateDataList([]);
    }
  };

  const handleCreateImage = useCallback(
    async (data: ICertificateData, download = false) => {
      setCertificateData(data);

      if (!download) {
        return;
      }

      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const imgName = `${data.user.nick_name}_${trainingInfo.name}.jpg`;
          html2canvas(document.getElementById('template')!, {
            useCORS: true,
          })
            .then((canvas) => {
              let img = document.createElement('a');
              img.href = canvas
                .toDataURL('image/jpeg')
                .replace('image/jpeg', 'image/octet-stream');
              img.download = imgName;
              img.click();

              resolve(true);
            })
            .catch(() => {
              reject(false);
            });
        }, 0);
      });
    },
    [trainingInfo],
  );

  // 一键下载
  const batchCreate = useCallback(async () => {
    if (isBatchLoading) {
      return;
    }
    // loading
    setIsBatchLoading(true);

    for (let i = 0; i < certificateDataList.length; i++) {
      const row = certificateDataList[i];
      await handleCreateImage(row, true);
      // 等一小会
      await sleep(500);
    }

    setIsBatchLoading(false);
  }, [certificateDataList, isBatchLoading]);

  return (
    <Row gutter={10} className={styles.content}>
      <Col span={12}>
        <CertificatePreview {...certificateData} training={trainingInfo} />
      </Col>
      <Col span={11}>
        <Form labelCol={{ span: 6 }}>
          <Form.Item
            name="trainingName"
            label="选择训练营"
            rules={[{ required: true, message: '请选择训练营' }]}
          >
            <Select
              placeholder="选择训练营"
              loading={queryTrainingLoading || queryUserLoading}
              onChange={setTrainingId}
            >
              {allTrainings.map((training) => (
                <Select.Option key={training.id} value={training.id}>
                  {training.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          {/* 上传 excel 文件 */}
          <Form.Item
            name="userAvatarUpload"
            label="打卡 excel 文件"
            valuePropName="file"
            rules={[{ required: true, message: '请上传打卡 excel 文件' }]}
          >
            <Upload
              name="excelFile"
              accept=".xlsx,.xls"
              beforeUpload={beforeUpload}
              onChange={handleFileChange}
              maxCount={1}
            >
              <Button type="primary" icon={<UploadOutlined />}>
                上传打卡 excel 文件
              </Button>
            </Upload>
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 6 }}>
            <Button
              type="primary"
              disabled={
                isBatchLoading || !trainingId || !certificateDataList.length
              }
              loading={isBatchLoading}
              onClick={batchCreate}
              icon={<CoffeeOutlined />}
            >
              一键下载所有证书
            </Button>
          </Form.Item>
        </Form>

        {/* 预览 */}
        <Table
          bordered
          size="small"
          columns={[
            {
              title: '昵称',
              dataIndex: ['user', 'nick_name'],
            },
            {
              title: '总目标数',
              dataIndex: ['metrics', 'taskCount'],
            },
            {
              title: '打卡天数',
              dataIndex: ['metrics', 'clockDays'],
            },
            {
              title: '总积分数',
              dataIndex: ['metrics', 'totalPoints'],
            },
            {
              title: '操作',
              dataIndex: 'action',
              render: (val, record) => {
                return (
                  <Space>
                    <Button
                      size="small"
                      type="primary"
                      disabled={!trainingId}
                      icon={<EyeOutlined />}
                      onClick={() => handleCreateImage(record)}
                    >
                      预览证书
                    </Button>
                    <Button
                      size="small"
                      type="primary"
                      disabled={!trainingId}
                      icon={<DownloadOutlined />}
                      onClick={() => handleCreateImage(record, true)}
                    >
                      生成证书
                    </Button>
                  </Space>
                );
              },
            },
          ]}
          dataSource={certificateDataList}
        ></Table>
      </Col>
    </Row>
  );
}
