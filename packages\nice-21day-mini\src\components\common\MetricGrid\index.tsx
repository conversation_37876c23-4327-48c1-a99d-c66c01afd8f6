import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import React, { ReactNode } from 'react';
import './index.scss';

interface IMetricGridProps {
  data: {
    key: string;
    label: ReactNode;
    value: any;
    link?: string;
  }[];
  columnNum?: number;
}
const MetricGrid: React.FC<IMetricGridProps> = ({
  data = [],
  columnNum = 3,
}) => {
  return (
    <View className="metrics-wrapper">
      {data.map((item) => (
        <View
          className="metrics"
          key={item.key}
          style={{ flex: `0 0 ${(100 / columnNum).toFixed(5)}%` }}
          onClick={() => {
            if (!item.link) {
              return;
            }
            Taro.navigateTo({
              url: item.link,
            });
          }}
        >
          <View className="flex justify-center items-center gap-1">
            {item.label}
            {item.link && (
              <View className="at-icon at-icon-external-link !text-sm"></View>
            )}
          </View>
          <View>{item.value}</View>
        </View>
      ))}
    </View>
  );
};

export default MetricGrid;
