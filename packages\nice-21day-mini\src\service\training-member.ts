import request from '@/utils/request';
import {
  IPageFactory,
  IQueryTrainingMemberParams,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';

/**
 * 获取训练营成员分页列表
 */
export const getTrainingMembers = async (
  params: IQueryTrainingMemberParams,
) => {
  return await request.get<IPageFactory<ITrainingMember>>(
    `/training-users`,
    params,
  );
};

/**
 * 获取训练营成员详情
 */
export const getTrainingMemberDetail = async (member_id: string) => {
  return await request.get<ITrainingMember>(`/training-users/${member_id}`);
};

/**
 * 获取训练营成员详情
 */
export const getTrainingMemberDetailByUserId = async (
  training_id: string,
  user_id: string,
) => {
  return await request.get<ITrainingMember>(
    `/trainings/${training_id}/users/${user_id}`,
  );
};
// 训练营成员排行榜列表
export const queryTrainingMemberRankings = async (training_id: string) => {
  return await request.get<ITrainingMember[]>(`/training-users/rankings`, {
    training_id,
  });
};

/**
 * 更新我在某个训练营下的总结
 */
export const updateMyTrainingSummary = async (
  trainingId: string,
  summary: string,
) => {
  return await request.put<ITrainingMember>(
    `/my/trainings/${trainingId}/summaries`,
    { summary },
  );
};
