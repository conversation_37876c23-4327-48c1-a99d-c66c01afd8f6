import { queryTrainingDetail } from '@/services';
import { ITraining } from '@nice-people/nice-21day-shared';
import { useParams } from '@umijs/max';
import { Result, Skeleton } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import TrainingForm from '../components/TrainingForm';

const CreateTraining: React.FC = () => {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [training, setTraining] = useState<ITraining>();

  const queryTrainingInfo = useCallback(() => {
    if (!params.id) {
      return;
    }
    setLoading(true);
    queryTrainingDetail(params.id)
      .then((res) => {
        setTraining(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.id]);

  useEffect(() => {
    queryTrainingInfo();
  }, [params.id]);

  if (loading) {
    return <Skeleton loading />;
  }

  if (!training?.id) {
    return <Result status="error" title="训练营不存在或已被删除" />;
  }

  return <TrainingForm training={training} />;
};

export default CreateTraining;
