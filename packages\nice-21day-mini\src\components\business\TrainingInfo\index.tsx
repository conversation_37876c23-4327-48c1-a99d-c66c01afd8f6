import {
  ETrainingProgress,
  ITraining,
  trainingAttendanceProgress,
  trainingStartAttendanceDaysCount,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { ReactNode, useMemo } from 'react';
import { AtTag } from 'taro-ui';
import './index.scss';

export interface ITrainingInfoProps {
  training: ITraining;
  desc?: ReactNode;
}

const TrainingInfo: React.FC<ITrainingInfoProps> = ({
  training = {} as ITraining,
  desc,
}) => {
  const router = useRouter();
  // 打卡第几天
  const startDaysCount = useMemo(() => {
    return trainingStartAttendanceDaysCount(training.start_time);
  }, [training.start_time]);

  // 计算当前打卡进度
  const attendanceProgress = useMemo(() => {
    return trainingAttendanceProgress(training.start_time, training.end_time);
  }, [training.start_time, training.end_time]);

  const showTrainingAnalysisBtn = useMemo(() => {
    // 报名阶段不显示
    if (training.progress === ETrainingProgress.Registering) {
      return false;
    }
    // 打卡统计页面当前页面不显示，防止死循环
    if (router.path?.includes('analytics/training-attendance-analysis/index')) {
      return false;
    }

    return true;
  }, [training.progress, router.path]);

  return (
    <View className="training-info">
      {/* 名称 */}
      <View className="training-title">
        <View className="name flex justify-between items-center">
          {training.name}
          {/* 打卡统计 */}
          {showTrainingAnalysisBtn && (
            <View
              style={{ fontSize: '14px' }}
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/analytics/training-attendance-analysis/index?training_id=${training?.id}`,
                });
              }}
            >
              打卡统计
              <View className="at-icon at-icon-chevron-right" />
            </View>
          )}
        </View>
        {/* Tag */}
        <View className="flex gap-2 mt-1">
          <AtTag type="primary" size="small" circle>
            {TRAINING_PROGRESS_MAPPING[training.progress]}
          </AtTag>
          <AtTag type="primary" size="small" circle>
            达标积分 {training.standard_score}
          </AtTag>
          <AtTag type="primary" size="small" circle>
            {training.fee ? `￥${training.fee}` : '不要押金'}
          </AtTag>
          <AtTag type="primary" size="small" circle>
            {training.join_user_count} 人参加
          </AtTag>
          {
            // 打卡中
            training.progress === ETrainingProgress.Processing && (
              <AtTag type="primary" size="small" circle>
                打卡第 {startDaysCount} 天
              </AtTag>
            )
          }
        </View>
      </View>
      <View className="training-date">
        {/* 进度条 */}
        <View
          className="training-date__progress"
          style={{ width: `${attendanceProgress}%` }}
        />
        <View className="icon-wrap">
          <View className="at-icon at-icon-clock"></View>
          {training.start_time}
        </View>
        <View className="at-icon at-icon-subtract"></View>
        <View className="icon-wrap">
          <View className="at-icon at-icon-clock"></View>
          {training.end_time}
        </View>
      </View>
      <View className="training-desc">
        {desc ?? (
          <>
            {/* <Text className="label">简介</Text> */}
            <Text className="desc-text">{training.description}</Text>
          </>
        )}
      </View>
    </View>
  );
};
export default TrainingInfo;
