import { queryAttendanceLogLikeTop, queryAttendanceLogLikes } from '@/service';
import {
  IAttendanceLog,
  IAttendanceLogLike,
  IQueryAttendanceLogLikeTopParams,
} from '@nice-people/nice-21day-shared';
import { useCallback, useEffect, useState } from 'react';

/**
 * 获取打卡记录点赞列表
 */
export const useAttendanceLikes = (attendanceLogId: string) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [attendanceLogLikes, setAttendanceLogLikes] = useState<
    IAttendanceLogLike[]
  >([]);

  const fetchAttendanceLogLikes = useCallback(() => {
    setLoading(true);
    queryAttendanceLogLikes({
      attendance_log_id: attendanceLogId,
    })
      .then(({ data }) => {
        setAttendanceLogLikes(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [attendanceLogId]);

  useEffect(() => {
    fetchAttendanceLogLikes();
  }, [fetchAttendanceLogLikes]);

  return {
    loading,
    attendanceLogLikes,
    reload: fetchAttendanceLogLikes,
  };
};

/**
 * 获取训练营打卡记录获赞的排行
 */
export const useAttendanceLikeTopN = (
  params: IQueryAttendanceLogLikeTopParams,
) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [hotAttendanceLogs, setHotAttendanceLogs] = useState<IAttendanceLog[]>(
    [],
  );

  useEffect(() => {
    setLoading(true);
    queryAttendanceLogLikeTop(params)
      .then(({ data }) => {
        setHotAttendanceLogs(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.training_id, params.count, params.atteneance_date]);

  return {
    loading,
    hotAttendanceLogs,
  };
};
