import emojiJson from '@/assets/chinesebqb.json';
import { IRichTextRef, RichText } from '@/components/RichText';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { createAttendanceComment } from '@/services';
import { SendOutlined, UserOutlined } from '@ant-design/icons';
import { getFileUrl } from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import { Avatar, Button, Image, Input, message, Modal } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';

const defaultEmojiList = [
  {
    name: '奥特曼00140-你牛逼',
    url: 'https://gitee.com/anonymity94/ChineseBQB/raw/master/043Altman_%E5%A5%A5%E7%89%B9%E6%9B%BCBQB/%E5%A5%A5%E7%89%B9%E6%9B%BC00140-%E4%BD%A0%E7%89%9B%E9%80%BC.jpg',
  },
  {
    name: '程序员00038-向优秀程序员低头',
    url: 'https://gitee.com/anonymity94/ChineseBQB/raw/master/024Programmer_%E7%A8%8B%E5%BA%8F%E5%91%98BQB/%E7%A8%8B%E5%BA%8F%E5%91%9800038-%E5%90%91%E4%BC%98%E7%A7%80%E7%A8%8B%E5%BA%8F%E5%91%98%E4%BD%8E%E5%A4%B4.jpg',
  },
];

interface IEmoji {
  name: string;
  url: string;
}

interface IEmojiProps {
  emoji: IEmoji;
  size?: number;
  onClick?: (emoji: IEmoji) => void;
}
const Emoji = ({ emoji, onClick, size = 80 }: IEmojiProps) => (
  <div
    key={emoji.url}
    className="text-lg hover:bg-gray-100 rounded px-1 transition-colors disabled:cursor-not-allowed flex flex-col items-center py-1 justify-center border border-gray-200"
  >
    {/* <Avatar
        shape="square"
        size={size}
        src={emoji.url}
        alt={emoji.name}
        className="cursor-pointer"
      /> */}
    <div
      className="flex items-center"
      style={{
        width: size,
        height: size,
      }}
    >
      <Image
        referrerPolicy="no-referrer"
        src={emoji.url}
        alt={emoji.name}
        width={size}
        className="cursor-pointer"
      />
    </div>
    <Button
      size="small"
      block
      className="mt-1 text-sm"
      onClick={() => {
        onClick?.(emoji);
      }}
    >
      使用
    </Button>
  </div>
);

interface ICreateAttendanceCommentProps {
  attendance_id: string;
  reply_id?: string;
  // 回复框是否显示当前当登录人头像
  showAvatar?: boolean;
  // 评论框高度
  height?: number;
  onFinish?: () => void;
}
const CreateAttendanceComment = ({
  attendance_id,
  onFinish,
  reply_id = '',
  showAvatar = true,
  height = 200,
}: ICreateAttendanceCommentProps) => {
  const { currentUser, isLogin } = useCurrentUser();
  const editorRef = useRef<IRichTextRef | null>(null);
  // 提交中
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  // 评论纯文本
  const [commentText, setCommentText] = useState<string>('');

  // 表情包搜索
  const [emojiModalVisible, setEmojiModalVisible] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('牛逼');
  const [displayEmojiList, setDisplayEmojiList] = useState<IEmoji[]>([]);

  const handleSubmitComment = useCallback(async () => {
    if (!currentUser?.id) {
      message.error('请登录');
    }

    if (!commentText) {
      message.error('请输入评论内容');
    }

    try {
      setIsSubmitting(true);

      await createAttendanceComment({
        content: editorRef.current?.editor?.getHtml() || '',
        target_id: attendance_id,
        reply_id,
      });

      // 重置评论内容
      setCommentText('');
      message.success('评论成功');

      onFinish?.();
      // 重置评论内容
      editorRef.current?.editor?.clear();
    } catch (error) {
      message.error('评论失败');
    } finally {
      setIsSubmitting(false);
    }
  }, [commentText, currentUser, reply_id]);

  const debouncedSearch = useCallback(
    debounce((keyword) => {
      setDisplayEmojiList(
        emojiJson.data
          .filter((item) => item.name.includes(keyword))
          .slice(0, 20),
      );
    }, 300),
    [],
  );

  const handleEmojiSearchChange = (val?: string) => {
    setSearchText(val || '');
    debouncedSearch(val || '');
  };

  useEffect(() => {
    debouncedSearch(searchText);
  }, []);

  const insertEmoji = (emoji: IEmoji) => {
    const imgNode = {
      type: 'paragraph',
      children: [
        {
          type: 'image',
          src: emoji.url,
          alt: emoji.name,
          style: {
            height: '',
            width: '200px',
          },
          children: [{ text: '' }],
        },
      ],
    };

    const editor = editorRef.current?.editor;
    editor?.insertNode(imgNode);

    if (emojiModalVisible) {
      setEmojiModalVisible(false);
    }
  };

  return (
    <div className="bg-white">
      <div className="flex space-x-3">
        {showAvatar && (
          <>
            {isLogin ? (
              <Avatar
                src={getFileUrl(currentUser.avatar)}
                alt={currentUser.nick_name}
                className="w-8 h-8 rounded-full flex-shrink-0"
              />
            ) : (
              <Avatar
                icon={<UserOutlined />}
                className="w-8 h-8 rounded-full flex-shrink-0"
              />
            )}
          </>
        )}
        <div className="flex-auto min-w-0">
          <div className="relative">
            <RichText
              simple
              maxLength={1000}
              ref={editorRef}
              readOnly={!isLogin}
              placeholder={isLogin ? '写下你的评论...' : '登录后评论'}
              // className={`!h-[${height}px] !min-h-[100px]`}
              style={{
                height: height,
                minHeight: 100,
              }}
              onChange={(editor) => {
                console.log('评论 change', editor.getText());
                setCommentText(editor.getText());
              }}
            />
            {/* 备注信息: 这是一个富文本编辑器，可以使用Markdown语法进行排版 */}
            <div className="my-1 text-neutral-400">
              富文本编辑器，支持部分 Markdown 语法，支持粘贴图片
            </div>
          </div>

          {/* 表情和发送按钮 */}
          {isLogin && (
            <div className="mt-2">
              <div className="flex gap-1">
                {/* 表情包快捷回复 */}
                {defaultEmojiList.map((emoji) => (
                  <Emoji key={emoji.url} emoji={emoji} onClick={insertEmoji} />
                ))}

                {/* 更多经常表情包 */}
                <Avatar
                  shape="square"
                  size={80}
                  className="cursor-pointer !text-sm"
                  onClick={() => setEmojiModalVisible(true)}
                >
                  更多表情
                </Avatar>
              </div>
            </div>
          )}
          <div className="mt-2">
            {isLogin ? (
              <Button
                loading={isSubmitting}
                block
                type="primary"
                icon={<SendOutlined />}
                disabled={isSubmitting || !commentText.trim()}
                onClick={handleSubmitComment}
              >
                评论
              </Button>
            ) : (
              <Button
                type="primary"
                block
                icon={<SendOutlined />}
                onClick={() => history.push('/user/login')}
              >
                登录评论
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 表情包搜索弹出框 */}
      <Modal
        title="表情包搜索"
        open={emojiModalVisible}
        onCancel={() => setEmojiModalVisible(false)}
        onOk={() => setEmojiModalVisible(false)}
        footer={() => <></>}
        width={800}
        height={800}
      >
        <Input
          placeholder="搜索表情包"
          allowClear
          autoFocus
          value={searchText}
          onChange={(e) => handleEmojiSearchChange(e.target.value)}
        />
        <div className="min-h-[500px] mt-4">
          {searchText ? (
            displayEmojiList.length > 0 ? (
              <div className="grid grid-cols-5 gap-2">
                {emojiJson.data
                  .filter((item) => item.name.includes(searchText))
                  .slice(0, 20)
                  .map((item) => (
                    <Emoji key={item.url} emoji={item} onClick={insertEmoji} />
                  ))}
              </div>
            ) : (
              <div className="text-center pt-4 text-neutral-500">
                没有找到相关表情包
              </div>
            )
          ) : (
            <div className="text-center pt-4 text-neutral-500">
              请输入关键词
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default CreateAttendanceComment;
