import { ICurrentUser } from '@nice-people/nice-21day-shared';
import Taro, { useDidHide, useDidShow } from '@tarojs/taro';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import isLeapYear from 'dayjs/plugin/isLeapYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';
import relativeTime from 'dayjs/plugin/relativeTime';
import React, { ReactNode, useEffect, useState } from 'react';

import './custom-theme.scss';
// eslint-disable-next-line import/first
import 'windi.css';
import './app.scss';

import { AppContext } from './appContext';
import { useSystemSettings } from './hooks';
import { getCurrentUser } from './service';

dayjs.extend(isoWeek);
dayjs.extend(isoWeeksInYear);
dayjs.extend(isLeapYear);
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

Object.assign(global, {
  Array: Array,
  Date: Date,
  Error: Error,
  Function: Function,
  Math: Math,
  Object: Object,
  RegExp: RegExp,
  String: String,
  TypeError: TypeError,
  setTimeout: setTimeout,
  clearTimeout: clearTimeout,
  setInterval: setInterval,
  clearInterval: clearInterval,
});

Taro.setInnerAudioOption({
  // 不遵循静音开关，即使是在静音模式下，也能播放声音
  obeyMuteSwitch: false,
});

interface IProps {
  children: ReactNode;
}
const App: React.FC = (props: IProps) => {
  const [currentUser, setCurrentUser] = useState<ICurrentUser>(
    {} as ICurrentUser,
  );
  const { systemSettings, querySystemSettingsLoading } = useSystemSettings();

  const refreshCurrentUser = () => {
    getCurrentUser()
      .then(({ data }) => {
        setCurrentUser(data);
      })
      .catch(() => {
        setCurrentUser({} as ICurrentUser);
      });
  };

  useEffect(() => {
    refreshCurrentUser();

    // Taro.loadFontFace({
    //   global: true,
    //   family: 'DINPro-Medium',
    //   source:
    //     'url("https://oss.yayujs.com/alioss/2023-12-24/bb59a6dc-9d50-4050-827d-c095de4ac201.otf")',
    //   success: console.log,
    // });

    // // Taro.loadFontFace({
    // //   global: true,
    // //   family: 'DINPro-Bold',
    // //   source:
    // //     'url("https://oss.yayujs.com/alioss/2023-12-20/d656674c-48b7-44f6-b552-4381e9827cc7.otf")',
    // //   success: console.log,
    // // });

    // // https://welai.github.io/glow-sans/
    // Taro.loadFontFace({
    //   global: true,
    //   family: 'Glow Sans SC',
    //   source:
    //     // GlowSansSC-Normal-Medium
    //     'url("https://oss.yayujs.com/alioss/2023-12-21/f6eb236f-3e51-4515-98ab-28bd910c31ab.otf")',
    //   // GlowSansSC-Normal-Regular
    //   // 'url("https://oss.yayujs.com/alioss/2023-12-21/80193670-c8fa-4f61-8dd0-17d547c11216.otf")',
    //   success: console.log,
    // });
  }, []);

  // 对应 onShow
  useDidShow(() => {
    const updateManager = Taro.getUpdateManager();
    updateManager.onCheckForUpdate(function ({ hasUpdate }) {
      // 请求完新版本信息的回调
      if (hasUpdate) {
        updateManager.onUpdateReady(function () {
          Taro.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: function ({ confirm }) {
              if (confirm) {
                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                updateManager.applyUpdate();
              }
            },
          });
        });
        updateManager.onUpdateFailed(function () {
          // 新的版本下载失败
          Taro.showModal({
            title: '已经有新版本了',
            content: '新版本已经上线，请您删除当前小程序，重新打开',
          });
        });
      }
    });
  });

  // 对应 onHide
  useDidHide(() => {});

  return (
    <AppContext.Provider
      value={{
        currentUser,
        refreshCurrentUser,
        systemSettings,
        querySystemSettingsLoading,
      }}
    >
      {props.children}
    </AppContext.Provider>
  );
};

export default App;
