.card_wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #fff;
  box-shadow: 0px 0px 38px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  position: relative;

  & > .progress-flag {
    position: absolute;
    left: 16px;
    top: 16px;
    color: #fff;
    font-size: 24px;
    padding: 2px 8px;
    background-color: var(--default-color);
  }

  &:first-child {
    margin-top: 16px;
  }

  .training_content {
    display: flex;
    justify-content: flex-start;
    width: 100%;

    .training_content_left {
      width: 168px;
      .cover {
        width: 100%;
        height: 192px;
      }
    }

    .training_content_right {
      display: flex;
      flex: 1 0 calc(100% - 220px);
      flex-direction: column;
      margin-left: 16px;
      justify-content: space-between;
      overflow: hidden;

      .training_date {
        font-family: serif;
        font-size: 28px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        // 把最后的分钟和秒数移除
        .at-countdown {
          .at-countdown__item:nth-last-of-type(-n + 2) {
            display: none;
          }
        }
      }

      .training_name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-weight: 700;
        font-size: 32px;
      }

      .training_descripton {
        position: relative;
        height: 96px;
      }

      .training_descripton_text {
        font-size: 28rpx;
        line-height: 32px;
        overflow: hidden;
        width: 100%;
        word-break: break-word;
        color: #999;
        font-size: 26px;
      }
    }
  }

  .training_footer {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
    margin-top: 16px;
    padding-top: 16px;

    .training_extrainfo {
      font-size: 28px;
    }

    .user-count {
      color: var(--default-color);
    }

    .button {
      width: 160px;
      height: 60px;
      line-height: 60px;
      background: var(--default-color);
      border-radius: 29px;
      border: none;
      font-size: 28px;
      color: #fff;
      padding-left: 0;
      padding-right: 0;

      &.disabled {
        opacity: 0.4;
      }
    }
  }

  .training_extra {
    // margin-top: 10px;
    width: 100%;
  }
}
