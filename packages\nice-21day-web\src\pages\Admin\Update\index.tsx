import { queryAdminDetail } from '@/services';
import { IAdmin } from '@nice-people/nice-21day-shared';
import { useParams } from '@umijs/max';
import { Result, Skeleton } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import AdminForm from '../components/AdminForm';

const UpdateAdmin: React.FC = () => {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [admin, setAdmin] = useState<IAdmin>();

  const queryAdminInfo = useCallback(() => {
    if (!params.id) {
      return;
    }
    setLoading(true);
    queryAdminDetail(params.id)
      .then((res) => {
        setAdmin(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.id]);

  useEffect(() => {
    queryAdminInfo();
  }, [queryAdminInfo]);

  if (loading) {
    return <Skeleton loading />;
  }

  if (!admin?.id) {
    return <Result status="error" title="管理员不存在或已被删除" />;
  }

  return <AdminForm admin={admin} />;
};

export default UpdateAdmin;
