import Taro, { CanvasContext } from '@tarojs/taro';

let isGetRatio = false;
let ratio = 1;

/**
 * 下载图片资源
 * @description 支持 cdn 图片资源和本地图片资源
 */
export function downImage(imageUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    if (/^http/.test(imageUrl)) {
      Taro.downloadFile({
        url: imageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath);
          } else {
            reject();
          }
        },
        fail(err) {
          reject(err);
        },
      });
    } else {
      // 支持本地地址
      resolve(imageUrl);
    }
  });
}

export function getImageInfo(
  imageUrl: string,
): Promise<Taro.getImageInfo.SuccessCallbackResult> {
  return new Promise((resolve, reject) => {
    Taro.getImageInfo({
      src: imageUrl,
      success: (res) => {
        resolve(res);
      },
      fail(err) {
        reject(err);
      },
    });
  });
}

/**
 * 圆角矩形
 * @param ctx canvas Context
 * @param x x 轴坐标
 * @param y y 轴坐标
 * @param w 宽度
 * @param h 高度
 * @param r 圆角
 */
export function drawRadiusRect(
  ctx: CanvasContext,
  x: number,
  y: number,
  w: number,
  h: number,
  r: number,
) {
  const br = r / 2;
  ctx.beginPath();
  ctx.moveTo(toPx(x + br), toPx(y)); // 移动到左上角的点
  ctx.lineTo(toPx(x + w - br), toPx(y));
  ctx.arc(
    toPx(x + w - br),
    toPx(y + br),
    toPx(br),
    2 * Math.PI * (3 / 4),
    2 * Math.PI * (4 / 4),
  );
  ctx.lineTo(toPx(x + w), toPx(y + h - br));
  ctx.arc(
    toPx(x + w - br),
    toPx(y + h - br),
    toPx(br),
    0,
    2 * Math.PI * (1 / 4),
  );
  ctx.lineTo(toPx(x + br), toPx(y + h));
  ctx.arc(
    toPx(x + br),
    toPx(y + h - br),
    toPx(br),
    2 * Math.PI * (1 / 4),
    2 * Math.PI * (2 / 4),
  );
  ctx.lineTo(toPx(x), toPx(y + br));
  ctx.arc(
    toPx(x + br),
    toPx(y + br),
    toPx(br),
    2 * Math.PI * (2 / 4),
    2 * Math.PI * (3 / 4),
  );

  ctx.clip();
}

interface IDrawSingleTextOptions {
  ctx: CanvasContext;
  x: number;
  y: number;
  text: string;
  fontSize?: number;
  color?: string;
  moveCenter?: boolean;
  // fontWeight?: string;
  // fontStyle?: string;
  // fontFamily?: string;
}
/**
 * 渲染一段文字
 */
export function drawSingleText({
  ctx,
  x,
  y,
  text,
  fontSize = 22,
  color = '#333',
  moveCenter = true,
}: // fontWeight = 'normal',
// fontStyle = 'normal',
// fontFamily = 'PingFangSC-Semibold',
IDrawSingleTextOptions) {
  // ctx.save();
  // ctx.beginPath();
  if (fontSize) {
    ctx.setFontSize(toPx(fontSize));
  }
  if (color) {
    ctx.setFillStyle(color);
  }
  if (moveCenter) {
    ctx.setTextAlign('center');
  } else {
    ctx.setTextAlign('left');
  }
  ctx.fillText(text, toPx(x), toPx(y));
  ctx.restore();
}

/**
 * rpx 转 px
 */
export function toPx(rpx: number) {
  // TODO: 这里不需要了。后面删除即可
  // if (!isGetRatio) {
  //   const sysInfo = Taro.getSystemInfoSync();
  //   const screenWidth = sysInfo.screenWidth;
  //   // 缩放比
  //   ratio = screenWidth / 750;
  //   isGetRatio = true;
  // }

  return rpx;
}
