import { queryTrainingMembers } from '@/services';
import {
  ArrowRightOutlined,
  BranchesOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import {
  getFileUrl,
  ITraining,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';
import { useModel } from '@umijs/max';
import { Avatar, Button, Card, Empty, Select, SelectProps, Space } from 'antd';
import { differenceBy } from 'lodash';
import { useEffect, useMemo, useState } from 'react';

interface IProps extends SelectProps<string> {
  allTrainings: ITraining[];
}
const TrainingSelect = ({ allTrainings, ...rest }: IProps) => (
  <Select
    {...rest}
    filterOption={(input, option) =>
      (option!.children as unknown as string)
        .toLowerCase()
        .includes(input.toLowerCase())
    }
  >
    {allTrainings.map((item) => (
      <Select.Option key={item.id} value={item.id}>
        {/* TODO: 这里可能要调整显示的名称 */}
        {/* 例如，展示完整的训练营名称 */}
        {item.name}
      </Select.Option>
    ))}
  </Select>
);

const TrainingMemberDiff = () => {
  const { allTrainings, queryAllTrainings } = useModel('trainingModel');

  const [sourceTrainingId, setSourceTrainingId] = useState<string>();
  const [targetTrainingId, setTargetTrainingId] = useState<string>();

  const [sourceTrainingMembers, setSourceTrainingMembers] = useState<
    ITrainingMember[]
  >([]);
  const [targetTrainingMembers, setTargetTrainingMembers] = useState<
    ITrainingMember[]
  >([]);

  useEffect(() => {
    queryAllTrainings();
  }, []);

  useEffect(() => {
    if (!sourceTrainingId || !targetTrainingId) {
      return;
    }
    if (sourceTrainingId === targetTrainingId) {
      return;
    }

    Promise.all([
      queryTrainingMembers({
        training_id: sourceTrainingId,
        page: 1,
        size: 500,
      }),
      queryTrainingMembers({
        training_id: targetTrainingId,
        page: 1,
        size: 500,
      }),
    ]).then(([sourceResult, targetResult]) => {
      setSourceTrainingMembers(sourceResult.rows);
      setTargetTrainingMembers(targetResult.rows);
    });
  }, [sourceTrainingId, targetTrainingId]);

  const renderResult = useMemo(() => {
    if (!sourceTrainingId || !targetTrainingId) {
      return <Empty description="请选择训练营" />;
    }
    if (sourceTrainingId === targetTrainingId) {
      return <Empty description="源版本与目标版本无差异，请重新选择" />;
    }

    // lodash 获取新增的数据
    const newMembers = differenceBy(
      targetTrainingMembers,
      sourceTrainingMembers,
      'user_id',
    );

    // lodash 获取删除的数据
    const deletedMembers = differenceBy(
      sourceTrainingMembers,
      targetTrainingMembers,
      'user_id',
    );

    // lodash 获取相同的数据
    const sameMembers = differenceBy(
      sourceTrainingMembers,
      deletedMembers,
      'user_id',
    );

    return (
      <div className="flex flex-col gap-2">
        <Card
          size="small"
          title={`新增的成员（${newMembers.length}）`}
          className="bg-green-100"
        >
          <Space size={[20, 10]} wrap>
            {newMembers.map((member) => (
              <div key={member.user_id}>
                <Avatar src={getFileUrl(member.user?.avatar_url)} />{' '}
                {member.user?.nick_name}
              </div>
            ))}
          </Space>
        </Card>

        <Card
          size="small"
          title={`退出的成员（${deletedMembers.length}）`}
          className="bg-red-100"
        >
          <Space size={[20, 10]} wrap>
            {deletedMembers.map((member) => (
              <div key={member.user_id}>
                <Avatar src={getFileUrl(member.user?.avatar_url)} />{' '}
                {member.user?.nick_name}
              </div>
            ))}
          </Space>
        </Card>

        <Card size="small" title={`相同的成员（${sameMembers.length}）`}>
          <Space size={[20, 10]} wrap>
            {sameMembers.map((member) => (
              <div key={member.user_id}>
                <Avatar src={getFileUrl(member.user?.avatar_url)} />{' '}
                {member.user?.nick_name}
              </div>
            ))}
          </Space>
        </Card>
      </div>
    );
  }, [
    sourceTrainingId,
    targetTrainingId,
    sourceTrainingMembers,
    targetTrainingMembers,
  ]);

  return (
    <>
      <div className="flex gap-2">
        <Space.Compact>
          <Button>
            源 <BranchesOutlined />
          </Button>
          <TrainingSelect
            className="w-[200px]"
            value={sourceTrainingId}
            allTrainings={allTrainings}
            onChange={setSourceTrainingId}
          />
        </Space.Compact>

        <ArrowRightOutlined />

        <Space.Compact>
          <Button>
            目标 <BranchesOutlined />
          </Button>
          <TrainingSelect
            className="w-[200px]"
            value={targetTrainingId}
            allTrainings={allTrainings}
            onChange={setTargetTrainingId}
          />
        </Space.Compact>

        <Button
          onClick={() => {
            const tempSourceTrainingId = sourceTrainingId;
            setSourceTrainingId(targetTrainingId);
            setTargetTrainingId(tempSourceTrainingId);
          }}
          disabled={
            !sourceTrainingId ||
            !targetTrainingId ||
            sourceTrainingId === targetTrainingId
          }
          icon={<SwapOutlined />}
        >
          交换
        </Button>
      </div>

      {/* 对比结果 */}
      <div className="mt-10">{renderResult}</div>
    </>
  );
};

export default TrainingMemberDiff;
