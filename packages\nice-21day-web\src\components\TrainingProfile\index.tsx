import {
  ITraining,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Descriptions } from 'antd';
import React from 'react';

interface ITrainingProfileProps {
  training: ITraining;
}
const TrainingProfile: React.FC<ITrainingProfileProps> = ({ training }) => {
  return (
    <Descriptions
      bordered
      size="small"
      column={3}
      styles={{
        label: { lineHeight: '32px' },
        content: { lineHeight: '32px' },
      }}
    >
      <Descriptions.Item label="进度">
        {TRAINING_PROGRESS_MAPPING[training!.progress]}
      </Descriptions.Item>
      <Descriptions.Item label="押金">
        {training?.fee ? `￥${training.fee}` : '无'}
      </Descriptions.Item>
      <Descriptions.Item label="参与人数">
        {training?.join_user_count || 0}
      </Descriptions.Item>
      <Descriptions.Item label="达标积分">
        {training?.standard_score || 0}
      </Descriptions.Item>

      <Descriptions.Item label="起止时间">
        {training?.start_time} ~ {training?.end_time}
      </Descriptions.Item>
    </Descriptions>
  );
};
export default TrainingProfile;
