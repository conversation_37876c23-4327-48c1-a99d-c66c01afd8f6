import { getSystemSettings } from '@/service';
import { ISystemSettingMap } from '@nice-people/nice-21day-shared';
import { useEffect, useState } from 'react';

export const useSystemSettings = () => {
  const [querySystemSettingsLoading, setQuerySystemSettingsLoading] =
    useState<boolean>(true);
  const [systemSettings, setSystemSettings] = useState<ISystemSettingMap>({});

  useEffect(() => {
    setQuerySystemSettingsLoading(true);
    getSystemSettings()
      .then(({ data }) => {
        if (data.length > 0) {
          setSystemSettings(
            data.reduce(
              (acc, cur) => ({
                ...acc,
                [cur.key]: cur.value,
              }),
              {},
            ),
          );
        }
      })
      .finally(() => {
        setQuerySystemSettingsLoading(false);
      });
  }, []);

  return {
    querySystemSettingsLoading,
    systemSettings,
  };
};
