import {
  AttendancesTimeDistributions,
  Avatar,
  Card,
  MetricGrid,
  TrainingLayout,
} from '@/components';
import { useRouterParams } from '@/hooks';
import { queryUserAttendanceCalendar } from '@/service';
import {
  EAttendanceState,
  IAttendanceCalendar,
} from '@nice-people/nice-21day-shared';
import { View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import Calendar from 'custom-calendar-taro';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { AtActionSheet, AtActionSheetItem, AtLoadMore, AtToast } from 'taro-ui';
import './index.scss';

const today = dayjs().format('YYYY-MM-DD');

interface IToastMsg {
  isOpen: boolean;
  msg: string;
  icon: string;
}

const UserAttendanceAnalysis = () => {
  const { training_id, user_id, from } = useRouterParams();

  const [loading, queryCalendarLoading] = useState(true);
  const [calendarData, setCalendarData] = useState<IAttendanceCalendar>();

  const [showToastMsg, setShowToastMsg] = useState<IToastMsg>({
    msg: '',
    isOpen: false,
    icon: 'error',
  });

  // 选择的日期
  const [selectedDate, setSelectedDate] = useState<string>();

  const selectedAttendance = useMemo(() => {
    if (!selectedDate || !calendarData?.attendanceLogs) {
      return null;
    }
    return (calendarData?.attendanceLogs ?? []).find(
      (el) => el.attendance_date === selectedDate,
    );
  }, [selectedDate, calendarData?.attendanceLogs]);

  useEffect(() => {
    queryCalendarLoading(true);
    queryUserAttendanceCalendar(training_id, user_id)
      .then(({ data }) => {
        setCalendarData(data);
      })
      .finally(() => {
        queryCalendarLoading(false);
      });
  }, [training_id, user_id]);

  useEffect(() => {
    if (!loading && calendarData?.user) {
      Taro.showShareMenu({});
    } else {
      Taro.hideShareMenu();
    }
  }, [loading, calendarData?.user]);

  useShareAppMessage(() => {
    return {
      title: `${calendarData?.user?.nick_name}的打卡统计`,
      path: `/pages/analytics/user-attendance-analysis/index?training_id=${training_id}&user_id=${user_id}`,
    };
  });

  const extraInfo = useMemo(() => {
    return (calendarData?.attendanceLogs || []).map((attendance) => {
      // 打卡
      if (attendance.attendance_state === EAttendanceState.Attendance) {
        // 判断是否是补卡
        const isReissue =
          dayjs(attendance.created_at).format('YYYY-MM-DD') ===
          attendance.attendance_date;
        return {
          value: attendance.attendance_date,
          text: isReissue ? '已打卡' : '已补卡',
          color: isReissue ? 'green' : '#ff9800',
        };
      }
      return {
        value: attendance.attendance_date,
        text: '已请假',
        color: 'red',
      };
    });
  }, [calendarData?.attendanceLogs]);

  const attendanceRate = useMemo(() => {
    if (!calendarData) {
      return 0;
    }
    const { training, attendanceLogs } = calendarData;
    // 打卡率根据当前时间计算
    const curTrainingDay = dayjs(Date()).isBefore(
      dayjs(training.end_time),
      'day',
    )
      ? dayjs(Date())
      : dayjs(training.end_time);
    const trainingDays =
      curTrainingDay.diff(training.start_time, 'day') + 1 || 1;
    return Math.trunc((attendanceLogs.length / trainingDays) * 100);
  }, [calendarData]);

  const handleDayClick = useCallback(
    (date: string) => {
      if (!calendarData?.training) {
        return;
      }
      const { start_time, end_time } = calendarData?.training;
      // 未来的日期不需要操作
      if (
        dayjs(date).isBefore(dayjs(start_time), 'day') ||
        dayjs(date).isAfter(dayjs(end_time), 'day')
      ) {
        return;
      }
      setSelectedDate(date);
    },
    [calendarData?.training],
  );

  /** 点击打卡详情 */
  const handleAttenceDesc = useCallback(() => {
    Taro.navigateTo({
      url: `/pages/attendance-detail/index?training_id=${training_id}&attendance_id=${selectedAttendance.id}`,
    });
  }, [training_id, selectedAttendance]);

  if (loading || !calendarData) {
    return <AtLoadMore status="loading" />;
  }

  const { training, user, trainingUser } = calendarData;

  return (
    <View className="anttendance-calendar-page">
      <AtToast
        isOpened={showToastMsg.isOpen}
        text={showToastMsg.msg}
        icon={showToastMsg.icon}
        onClose={() => {
          setShowToastMsg({
            msg: '',
            isOpen: false,
            icon: 'close',
          });
        }}
      />
      <TrainingLayout
        training={training}
        title={
          <>
            <View className="flex gap-2  items-center justify-center flex-col">
              <Avatar
                avatar={user?.avatar_url}
                userName={user?.nick_name}
                layout="horizontal"
                size="small"
                link
                onClick={() => {
                  const userId = user?.id;
                  if (userId) {
                    Taro.navigateTo({
                      url: `/pages/user-homepage/index?user_id=${userId}`,
                    });
                  }
                }}
              />
            </View>
          </>
        }
      >
        <Card
          title="打卡指标统计"
          extra={
            from === 'ranking' && (
              <View
                className="flex items-center"
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/attendance-timeline-user/index?training_id=${training_id}&user_id=${user_id}`,
                  });
                }}
              >
                打卡记录
                <View className="at-icon at-icon-chevron-right" />
              </View>
            )
          }
        >
          <MetricGrid
            columnNum={4}
            data={[
              { key: '积分', label: '积分', value: trainingUser.score },
              {
                key: '打卡天数',
                label: '打卡天数',
                value: trainingUser.total_attendance_count,
              },
              {
                key: '最长连续打卡',
                label: '最长连续打卡',
                value: trainingUser.max_consecutive_attendance_count,
              },
              {
                key: '打卡率',
                label: '打卡率',
                value: attendanceRate + '%',
              },
            ]}
          />
        </Card>

        {/* 打卡时间段统计 */}
        <Card title="打卡时间分布" styles={{ paddingBottom: 0 }}>
          <AttendancesTimeDistributions
            trainingId={training_id}
            userId={user_id}
          />
        </Card>

        <Card title="打卡日历">
          {/* @ts-ignore */}
          <Calendar
            isVertical
            selectedDate={today}
            minDate={calendarData?.training?.start_time}
            maxDate={calendarData?.training?.end_time}
            customStyleGenerator={() => ({
              extraInfoStyle: {
                textAlign: 'center',
                paddingLeft: '0',
                fontSize: 12,
              },
            })}
            extraInfo={extraInfo}
            selectedDateColor="#346fc2"
            onDayClick={(item) => handleDayClick(item.value)}
          />
        </Card>
      </TrainingLayout>

      <AtActionSheet
        cancelText="取消"
        onCancel={() => setSelectedDate(undefined)}
        onClose={() => setSelectedDate(undefined)}
        isOpened={!!(selectedDate && selectedAttendance)}
      >
        {selectedAttendance && (
          <AtActionSheetItem
            onClick={() => {
              handleAttenceDesc();
              setSelectedDate(undefined);
            }}
          >
            打卡详情
          </AtActionSheetItem>
        )}
      </AtActionSheet>
    </View>
  );
};

export default UserAttendanceAnalysis;
