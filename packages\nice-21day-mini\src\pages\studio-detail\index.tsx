import { AppContext } from '@/appContext';
import { AudioPlayer, Avatar, FabBox, Result } from '@/components';
import {
  usePageShowAgain,
  useRouterParams,
  useStudioDetail,
  useUserCall,
} from '@/hooks';
import { deleteStudio, updateStudio } from '@/service';
import {
  EStudioPublicScope,
  EStudioTag,
  getFileUrl,
  STUDIO_PUBLIC_SCOPE_MAPPING,
  STUDIO_TAG_MAPPING,
} from '@nice-people/nice-21day-shared';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import { useCallback, useContext, useEffect, useMemo } from 'react';
import { AtButton, AtLoadMore, AtTag } from 'taro-ui';
import './index.scss';

const CIRCLE_SIZE = 160;

// 随机图片
// @see https://picsum.photos/750/1300
const BACKGROUND_IMAGE_LIST = [
  '/alioss/2023-04-15/eb974d5d-0ff7-4364-817b-39f2d9d0cbb7.jpeg',
  '/alioss/2023-04-15/69591f35-e0f0-48d7-a478-22fbb89c221e.jpeg',
  '/alioss/2023-04-15/8ad1b68a-4dfa-4de7-9798-4e92207a1b01.jpeg',
  '/alioss/2023-04-15/c379de31-27d6-49ab-b274-cb1968f42da9.jpeg',
  '/alioss/2023-04-15/04cae2c3-0582-46ba-9329-3a62210bc774.jpeg',
  '/alioss/2023-04-15/514a4439-fa1e-43f3-bb35-473d16e4c118.jpeg',
  '/alioss/2023-04-15/ced5e9f2-c86d-4151-9b07-15cdd64337eb.jpeg',
  '/alioss/2023-04-15/5f44cea8-8250-4fb6-8d36-7d2525a0c462.jpeg',
  '/alioss/2023-04-15/5c1501b1-7d3d-4cdb-aaec-efecfb8c02a7.jpeg',
  '/alioss/2023-04-15/9102d9b5-bb3c-48d1-8da0-191e0887d390.jpeg',
  '/alioss/2023-04-15/07848a82-0ee3-4c1d-b615-19ba4b0e3323.jpeg',
  '/alioss/2023-04-15/c4294698-3046-48a1-b62b-51f8cf2a0d8c.jpeg',
  '/alioss/2023-04-15/7daac607-08f0-4eed-aaf6-312f1e32756b.jpeg',
  '/alioss/2023-04-15/cdfee4f1-ab7d-4df9-bb01-f18379073abe.jpeg',
];

/**
 * 随机获取图片
 */
const randomBackgroundImage = () => {
  return BACKGROUND_IMAGE_LIST[
    Math.floor(Math.random() * BACKGROUND_IMAGE_LIST.length)
  ];
};

const StudioDetail: React.FC = () => {
  const { currentUser, systemSettings } = useContext(AppContext);
  const { studio_id, from } = useRouterParams();

  const { loading, fetchStudioDetail, studioDetail } =
    useStudioDetail(studio_id);

  const { isMyself } = useUserCall(currentUser, studioDetail?.creator_id);

  usePageShowAgain(() => {
    fetchStudioDetail(false);
  });

  const bgImage = useMemo(() => {
    return randomBackgroundImage();
  }, []);

  useShareAppMessage(() => {
    return {
      title: `${studioDetail?.user?.nick_name}的声音创作`,
      path: `/pages/studio-detail/index?studio_id=${studio_id}&from=share`,
    };
  });

  useEffect(() => {
    if (studioDetail?.user?.nick_name) {
      Taro.setNavigationBarTitle({
        title: `${studioDetail?.user?.nick_name}的声音创作`,
      });
    }
  }, [studioDetail?.user?.nick_name]);

  const handleDelete = useCallback(() => {
    Taro.showModal({
      title: '确定删除吗？',
      success: function (res) {
        if (res.confirm) {
          deleteStudio(studio_id)
            .then(() => {
              Taro.showToast({
                title: '删除成功',
                icon: 'success',
                duration: 1000,
              });
              Taro.navigateBack();
            })
            .catch(() => {
              Taro.showToast({
                title: '删除失败',
                icon: 'error',
                duration: 1000,
              });
            });
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      },
    });
  }, [studio_id]);

  const handleChangePublicScope = useCallback(() => {
    if (!studioDetail) {
      return;
    }
    const curPublicScope = studioDetail.public_scope;
    const newPublicScope =
      curPublicScope === EStudioPublicScope.Public
        ? EStudioPublicScope.Private
        : EStudioPublicScope.Public;

    Taro.showModal({
      title: '可见范围修改',
      content: `当前可见范围是[${STUDIO_PUBLIC_SCOPE_MAPPING[curPublicScope]}]，确定要修改成[${STUDIO_PUBLIC_SCOPE_MAPPING[newPublicScope]}]吗？`,
      success: function (res) {
        if (res.confirm) {
          updateStudio(studioDetail.id, {
            public_scope: newPublicScope,
          })
            .then(() => {
              fetchStudioDetail();
              Taro.showToast({
                title: '修改成功',
                icon: 'success',
                duration: 2000,
              });
            })
            .catch(() => {
              Taro.showToast({
                title: '修改失败',
                icon: 'error',
                duration: 2000,
              });
            });
        }
      },
    });
  }, [studioDetail]);

  const tags = useMemo(() => {
    return studioDetail?.tags.split(',').filter((el) => el);
  }, [studioDetail?.tags]);

  // 兼容代码
  if (systemSettings['studio_enable'] === 'false') {
    return (
      <Result
        text={
          <View className="flex flex-col items-center gap-2">
            <View>个人版小程序暂不支持</View>
            <View>敬请期待</View>
          </View>
        }
      />
    );
  }

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  if (!studioDetail?.id) {
    return <Result text="内容不存在或已被删除" />;
  }

  return (
    <View className="studio-detail-page">
      {/* background */}
      <View
        className="studio-bg"
        style={{ backgroundImage: `url(${getFileUrl(bgImage)})` }}
      />
      <View className="studio-shade" />
      {/* 内容 */}
      <View className="studio-wrapper">
        <View className="my-4.5">
          <Avatar
            layout="horizontal"
            avatar={studioDetail.user?.avatar_url}
            userName={studioDetail.user?.nick_name}
            style={{ color: '#fff' }}
            link
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/user-homepage/index?user_id=${studioDetail.user?.id}`,
              });
            }}
          />
        </View>

        <View className="flex flex-col items-center justify-center mb-3">
          <AudioPlayer
            circle
            circleSize={CIRCLE_SIZE}
            circleType="full"
            circleImage={bgImage}
            src={getFileUrl(studioDetail.files)}
          />
          <View className="pt-2 px-4">
            <View className="mb-1 flex-row flex-wrap justify-center flex gap-2 h-10">
              {tags.map((tag: EStudioTag) => (
                <AtTag active key={tag} type="primary" circle size="small">
                  {STUDIO_TAG_MAPPING[tag]}
                </AtTag>
              ))}
              <AtTag
                active
                type="primary"
                circle
                size="small"
                onClick={() => {
                  if (isMyself) {
                    handleChangePublicScope();
                  }
                }}
              >
                {STUDIO_PUBLIC_SCOPE_MAPPING[studioDetail.public_scope]}
                {isMyself && <View className="at-icon at-icon-chevron-right" />}
              </AtTag>
            </View>
          </View>
        </View>
        <ScrollView
          className="text-light-600 mb-5"
          scrollY
          // - 头像高度 - 播放器高度 - 标签高度 - 下边距 - 底部菜单预留高度
          style={{
            height: `calc(100% - 108px - ${CIRCLE_SIZE}px - 32px - 20px - 100px)`,
          }}
        >
          <Text user-select className="block px-6 leading-relaxed">
            {studioDetail.content}
          </Text>
        </ScrollView>

        {/* 底部固定 */}
        <FabBox>
          <View className="flex flex-row gap-2">
            {
              // 如果是自己创作的，支持编辑/删除
              isMyself && (
                <>
                  <AtButton
                    type="primary"
                    className="flex-1 w-1/3 danger-button"
                    circle
                    onClick={handleDelete}
                  >
                    <View className="flex items-center gap-1">
                      <View className="at-icon at-icon-trash" />
                      删除
                    </View>
                  </AtButton>
                  <AtButton
                    type="primary"
                    className="flex-1 w-1/3"
                    circle
                    onClick={() => {
                      Taro.navigateTo({
                        url: `/pages/studio-update/index?studio_id=${studio_id}`,
                      });
                    }}
                  >
                    <View className="flex items-center gap-1">
                      <View className="at-icon at-icon-edit" />
                      编辑
                    </View>
                  </AtButton>
                </>
              )
            }
            <AtButton type="primary" className="flex-1" circle openType="share">
              <View className="flex items-center gap-1">
                <View className="at-icon at-icon-share-2" />
                分享
              </View>
            </AtButton>
          </View>
        </FabBox>
      </View>
    </View>
  );
};

export default StudioDetail;
