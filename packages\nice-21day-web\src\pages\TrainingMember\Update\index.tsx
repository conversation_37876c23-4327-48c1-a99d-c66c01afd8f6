import { queryTrainingMemberDetail } from '@/services';
import { ITrainingMember } from '@nice-people/nice-21day-shared';
import { useParams } from '@umijs/max';
import { Result, Skeleton } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import TrainingMemberForm from '../components/TrainingMemberForm';

const UpdateTrainingMemberMember: React.FC = () => {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [trainingMember, setTrainingMember] = useState<ITrainingMember>();

  const queryTrainingMemberInfo = useCallback(() => {
    if (!params.id) {
      return;
    }
    setLoading(true);
    queryTrainingMemberDetail(params.id)
      .then((res) => {
        setTrainingMember(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.id]);

  useEffect(() => {
    queryTrainingMemberInfo();
  }, [params.id]);

  if (loading) {
    return <Skeleton loading />;
  }

  if (!trainingMember?.id) {
    return <Result status="error" title="训练营成员不存在或已被删除" />;
  }

  return <TrainingMemberForm trainingMember={trainingMember} />;
};

export default UpdateTrainingMemberMember;
