.layout {
  :global {
    .ant-page-header-heading,
    .ant-page-header-footer {
      padding-block-start: 0 !important;
      margin-block-start: 0 !important;
    }

    .ant-page-header {
      padding-block-start: 0;
      padding-block-end: 0;
      padding-inline-start: 0;
      padding-inline-end: 0;
    }

    .ant-page-header-content {
      padding-block-start: 0 !important;
    }

    .ant-list-item {
      // padding-top: 0 !important;
    }
  }
}
