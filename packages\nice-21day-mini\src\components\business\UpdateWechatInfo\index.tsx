import { AppContext } from '@/appContext';
import { updateWechatProfile } from '@/service';
import {
  DEFAULT_AVATAR,
  DEFAULT_NICK_NAME,
  getFileUrl,
  taroUpload,
} from '@/utils';
import { ICurrentUser } from '@nice-people/nice-21day-shared';
import { Button, ButtonProps, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useContext, useEffect, useState } from 'react';
import { AtAvatar, AtButton, AtInput } from 'taro-ui';
import './index.scss';

interface IUpdateWechatInfoProps {
  currentUser: ICurrentUser;
  callback?: () => void;
}

const UpdateWechatInfo: React.FC<IUpdateWechatInfoProps> = ({
  currentUser,
  callback,
}) => {
  const { refreshCurrentUser } = useContext(AppContext);
  const [avatar, setAvatar] = useState('');
  const [nickName, setNickName] = useState('');

  useEffect(() => {
    if (!currentUser) {
      return;
    }

    setAvatar(currentUser.avatar || DEFAULT_AVATAR);
    setNickName(currentUser.nick_name || DEFAULT_NICK_NAME);
  }, [currentUser]);

  const onChooseAvatar: ButtonProps['onChooseAvatar'] = async (e) => {
    const { avatarUrl } = e.detail;

    taroUpload(avatarUrl).then(({ path }) => {
      setAvatar(path);
    });
  };

  const updateWechatInfo = useCallback(() => {
    const params = {
      avatar_url: avatar,
      nick_name: nickName,
    };
    if (!avatar || avatar === DEFAULT_AVATAR) {
      Taro.showToast({
        title: '请设置头像',
        icon: 'error',
        duration: 1000,
      });
      return;
    }
    if (!nickName || nickName === DEFAULT_NICK_NAME) {
      Taro.showToast({
        title: '请设置昵称',
        icon: 'error',
        duration: 1000,
      });
      return;
    }

    updateWechatProfile(params)
      .then(() => {
        callback?.();
      })
      .catch(() => {
        Taro.showToast({
          title: '出错了，请联系开发人员处理',
          icon: 'error',
          duration: 1000,
        });
      });
  }, [avatar, nickName]);

  return (
    <View className="wrapper">
      <View className="relative h-full">
        <View
          className="absolute left-half top-half w-full h-full"
          style={{ transform: 'translate(-50%,-50%)' }}
        >
          <Button
            open-type="chooseAvatar"
            className="bg-neutral-500 opacity-40 border-none rounded-none h-full"
            onChooseAvatar={onChooseAvatar}
          />
          <View
            className="at-icon at-icon-camera text-light-800 text-2xl absolute left-half top-half"
            style={{
              transform: 'translate(-50%,-50%)',
              color: '#fff',
              pointerEvents: 'none',
            }}
          />
        </View>
        <AtAvatar image={getFileUrl(avatar)} />
      </View>

      <AtInput
        title="昵称"
        // @ts-ignore
        type="nickname"
        placeholder="请输入昵称"
        border={false}
        value={nickName}
        // 真机下才可以
        // @ts-ignore
        onBlur={(val) => setNickName(val)}
      />
      <AtButton
        full
        type="primary"
        className="submit"
        onClick={updateWechatInfo}
      >
        保存
      </AtButton>
    </View>
  );
};

export default UpdateWechatInfo;
