import request from '@/utils/request';
import {
  IAttendanceLog,
  IPageFactory,
  IQueryAttendanceLogParams,
} from '@nice-people/nice-21day-shared';

/**
 * 获取我的打卡记录
 */
export const queryMyAttendanceDetail = async (
  training_id: string,
  attendance_date: string,
) => {
  return await request.get<IAttendanceLog>(
    `/my/trainings/${training_id}/attendances`,
    {
      training_id,
      attendance_date,
    },
  );
};

/**
 * 获取用户打卡记录
 */
export const queryUserAttendanceDetail = async (attendance_Id: string) => {
  return await request.get<IAttendanceLog>(
    `/user-attendance-logs/${attendance_Id}`,
  );
};

/**
 * 用户打卡
 */
export const userAttendance = async (
  trainingId: string,
  data: Omit<IAttendanceLog, 'id' | 'user_id'>,
) => {
  return await request.post<IAttendanceLog>(
    `/my/trainings/${trainingId}/attendances`,
    data,
  );
};

/**
 * 编辑用户打卡
 */
export const editUserAttendance = async (
  attendanceId: string,
  data: Omit<IAttendanceLog, 'id' | 'user_id'>,
) => {
  return await request.put<IAttendanceLog>(
    `/my/attendances/${attendanceId}`,
    data,
  );
};

interface IUserLevelData
  extends Pick<IAttendanceLog, 'attendance_date' | 'description'> {}
/**
 * 用户请假
 */
export const userLeave = async (trainingId: string, data: IUserLevelData) => {
  return await request.post<IAttendanceLog>(
    `/my/trainings/${trainingId}/leave`,
    data,
  );
};

/**
 * 获取打卡记录列表（分页）
 */
export const queryAttendanceLogs = async (params: IQueryAttendanceLogParams) =>
  await request.get<IPageFactory<IAttendanceLog>>(
    '/user-attendance-logs',
    params,
  );
