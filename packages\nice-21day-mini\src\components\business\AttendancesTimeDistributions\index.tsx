import { useTrainingTimeDistribution } from '@/hooks';
import { View } from '@tarojs/components';
import React, { CSSProperties } from 'react';
import { AtLoadMore } from 'taro-ui';
import AttenanceTimeChart from './AttenanceTimeChart';

function ceilNum(num: number) {
  const bite = Math.pow(10, String(num).length - 1 || 1);
  return Math.ceil(num / bite) * bite;
}

interface IAttendancesTimeDistributionsProps {
  trainingId: string;
  userId?: string;
  height?: number;
  style?: CSSProperties;
}
const AttendancesTimeDistributions: React.FC<
  IAttendancesTimeDistributionsProps
> = ({ trainingId, userId, height = 200 }) => {
  const { loading, timeDistributions } = useTrainingTimeDistribution(
    trainingId,
    userId,
  );

  return (
    <View style={{ width: '100%', height }}>
      {loading ? (
        <AtLoadMore status="loading" />
      ) : (
        <AttenanceTimeChart
          id="attendances-time-distributions"
          data={timeDistributions}
        />
      )}
    </View>
  );
};

export default AttendancesTimeDistributions;
