.audio-player-pannel {
  padding: 0 20px;
  background: linear-gradient(
    90deg,
    rgba(10, 205, 139, 1) 0%,
    rgba(2, 193, 96, 1) 100%
  );
}

.audio-player-circle {
  .circling {
    animation: circling 20s linear infinite;
    animation-play-state: paused;

    &.running {
      animation-play-state: running;
    }
  }

  @keyframes circling {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(1turn);
    }
  }
}
