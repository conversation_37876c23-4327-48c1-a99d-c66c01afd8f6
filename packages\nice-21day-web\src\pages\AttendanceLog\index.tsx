import { SelectTraining } from '@/components/SelectTraining';
import { SelectUser } from '@/components/SelectUser';
import { UserInfo } from '@/components/UserInfo';
import { PRO_TABLE_DEFAULT_CONFIG } from '@/constants';
import { queryAttendanceLogs } from '@/services/attendance-log';
import { generateProTableValueEnum } from '@/utils';
import type { FormInstance } from '@ant-design/pro-components';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';

import {
  ATTENDANCE_LOG_AUDIT_STATE_MAPPING,
  EAttendanceState,
  IAttendanceLog,
  parseArrayJson,
} from '@nice-people/nice-21day-shared';
import { history, useSearchParams } from '@umijs/max';
import { Button, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import AttendanceAudit from './components/Audit';

const AttendanceLog: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const formRef = React.useRef<FormInstance>();
  const [searchParams, setSearchParams] = useSearchParams();

  // 获取搜索默认值
  useEffect(() => {
    formRef.current?.setFieldsValue({
      training_id: searchParams.get('training_id') || undefined,
      user_id: searchParams.get('user_id') || undefined,
    });
  }, [JSON.stringify(searchParams)]);

  const columns: ProColumns<IAttendanceLog>[] = [
    {
      title: '成员',
      dataIndex: 'userName',
      key: 'userName',
      search: false,
      fixed: 'left',
      width: 180,
      render: (_, record) => (
        <UserInfo
          nick_name={record.user?.nick_name || record.user_id}
          avatar_url={record.user?.avatar_url}
        />
      ),
    },
    {
      title: '成员',
      dataIndex: 'user_id',
      hideInTable: true,
      valueType: 'select',
      renderFormItem: (_, props) => <SelectUser {...props} />,
    },
    {
      title: '训练营',
      dataIndex: 'training_id',
      valueType: 'select',
      width: 200,
      render: (_, record) => {
        return record.training?.name || record.training_id;
      },
      renderFormItem: (_, props) => <SelectTraining {...props} />,
    },
    {
      title: '操作类型',
      dataIndex: 'training_state',
      width: 160,
      search: false,
      render: (_, { attendance_date, attendance_state, created_at }) => {
        if (attendance_state === EAttendanceState.Leave) {
          return '请假';
        }
        if (attendance_date !== dayjs(created_at).format('YYYY-MM-DD')) {
          return `补卡：${attendance_date}`;
        }

        return '打卡';
      },
    },
    {
      title: '打卡日期',
      dataIndex: 'attendance_date',
      width: 160,
      search: false,
    },
    {
      title: '打卡内容',
      dataIndex: 'attendance_tasks',
      search: false,
      ellipsis: true,
      width: 120,
      render: (_, record) => {
        return (
          <a
            onClick={() =>
              history.push(`/admin/attendance-log/${record.id}/profile`)
            }
          >
            共 {parseArrayJson(record?.attendance_tasks)?.length} 项
          </a>
        );
      },
    },
    {
      title: '审核状态',
      dataIndex: 'audit_state',
      valueType: 'select',
      width: 100,
      valueEnum: generateProTableValueEnum(ATTENDANCE_LOG_AUDIT_STATE_MAPPING),
    },
    {
      title: '提交时间',
      dataIndex: 'created_at',
      valueType: 'dateTime',
      search: false,
      width: 180,
    },
    {
      title: '编辑时间',
      dataIndex: 'updated_at',
      valueType: 'dateTime',
      search: false,
      width: 180,
    },
    {
      title: '备注',
      dataIndex: 'description',
      search: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      width: 220,
      render: (_, record) => (
        <Space>
          <AttendanceAudit
            simple
            attendanceId={record.id}
            auditState={record.audit_state}
            onFinish={() => {
              actionRef.current?.reload();
            }}
          />
          <Button
            type="link"
            onClick={() =>
              history.push(`/admin/attendance-log/${record.id}/update`)
            }
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() =>
              history.push(
                `/admin/attendance-log/calendar?user_id=${record.user_id}&training_id=${record.training_id}`,
              )
            }
          >
            打卡记录
          </Button>
        </Space>
      ),
    },
  ];
  return (
    <>
      <ProTable<IAttendanceLog>
        headerTitle="打卡记录列表"
        rowKey="id"
        scroll={{ x: 1400 }}
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        onReset={() => setSearchParams({})}
        {...PRO_TABLE_DEFAULT_CONFIG}
        request={async ({ pageSize, current, ...rest }) => {
          const res = await queryAttendanceLogs({
            ...formRef.current?.getFieldsValue(),
            ...rest,
            size: pageSize!,
            page: current!,
          });
          return {
            data: res.rows,
            total: res.total,
            success: true,
          };
        }}
      />
    </>
  );
};

export default AttendanceLog;
