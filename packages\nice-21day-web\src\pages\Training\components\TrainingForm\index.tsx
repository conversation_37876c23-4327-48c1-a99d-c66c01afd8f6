import {
  createTraining,
  queryTrainingNextPeriod,
  updateTraining,
} from '@/services';
import { ProCard } from '@ant-design/pro-components';
import {
  EState,
  ETrainingType,
  getObjectKeys,
  ITraining,
  TRAINING_PROGRESS_MAPPING,
  TRAINING_TYPE_MAPPING,
} from '@nice-people/nice-21day-shared';
import { history } from '@umijs/max';
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Space,
  Switch,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState } from 'react';

const { Option } = Select;
const { TextArea } = Input;

interface IFormData extends ITraining {
  time_range: [Dayjs, Dayjs];
}

interface ITrainingFormProps {
  training?: ITraining;
}

const TrainingForm: React.FC<ITrainingFormProps> = ({
  training = {} as ITraining,
}) => {
  const [submiting, setSubmiting] = useState(false);
  const [form] = Form.useForm();

  const handleTypeChange = (type: ETrainingType) => {
    queryTrainingNextPeriod(type)
      .then(({ next_period }) => {
        form.setFieldsValue({
          period: next_period,
        });
      })
      .catch(() => {
        message.error('查询训练营期号失败，请手动填写');
      });
  };

  const handleFinish = (values: IFormData) => {
    Modal.confirm({
      title: '确定保存吗？',
      onOk: () => {
        setSubmiting(true);
        const time_range = values['time_range'];
        const data = {
          ...values,
          name: `${TRAINING_TYPE_MAPPING[values.type] || 'YYYY'} - 第${
            values.period || 'xxx'
          }期`,
          start_time: time_range[0].format('YYYY-MM-DD'),
          end_time: time_range[1].format('YYYY-MM-DD'),
          state: values.state ? EState.Enable : EState.Disable,
        };
        // @ts-ignore
        delete data.time_range;

        (training?.id ? updateTraining(data) : createTraining(data))
          .then(() => {
            message.success('保存成功');
            history.back();
          })
          .finally(() => {
            setSubmiting(false);
          });
      },
    });
  };

  return (
    <ProCard>
      <Form
        name="basic"
        onFinish={handleFinish}
        autoComplete="off"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 12 }}
        form={form}
        initialValues={{
          ...training,
          state: training?.id ? training.state === EState.Enable : true,
          time_range: training?.id
            ? [dayjs(training.start_time), dayjs(training.end_time)]
            : [],
        }}
      >
        <Form.Item label="id" name="id" hidden>
          <Input />
        </Form.Item>

        {/* <Form.Item
          label="名称"
          name="name"
          required
          extra="根据类型和第几期自动生成"
        >
          <Input readOnly maxLength={32} disabled placeholder="请填入" />
        </Form.Item> */}
        <Form.Item
          label="类型"
          name="type"
          required
          rules={[{ required: true, message: '请选择类型' }]}
        >
          <Select
            disabled={!!training.id}
            placeholder="请选择"
            onChange={handleTypeChange}
          >
            {getObjectKeys(TRAINING_TYPE_MAPPING).map((key) => (
              <Option key={key} value={key}>
                {TRAINING_TYPE_MAPPING[key]}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="第几期"
          name="period"
          required
          rules={[{ required: true, message: '请输入第几期' }]}
          extra="选择类型后会自动填充下一期序号"
        >
          <InputNumber
            disabled={!!training.id}
            min={1}
            max={2000}
            precision={0}
            placeholder="请填入"
            style={{ width: '300px' }}
          />
        </Form.Item>
        <Form.Item
          label="进度"
          name="progress"
          required
          rules={[{ required: true, message: '请选择进度' }]}
        >
          <Select placeholder="请选择">
            {getObjectKeys(TRAINING_PROGRESS_MAPPING).map((key) => (
              <Option key={key} value={key}>
                {TRAINING_PROGRESS_MAPPING[key]}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="达标积分"
          name="standard_score"
          required
          rules={[{ required: true, message: '请输入达标积分' }]}
        >
          <InputNumber
            min={1}
            max={2000}
            precision={0}
            placeholder="请填入"
            style={{ width: '300px' }}
          />
        </Form.Item>
        <Form.Item
          label="起止时间"
          name="time_range"
          required
          rules={[{ required: true, message: '请选择起止时间' }]}
        >
          <DatePicker.RangePicker style={{ width: '300px' }} />
        </Form.Item>
        <Form.Item
          label="押金费用"
          name="fee"
          help="不需要押金时填充 0 即可"
          required
          rules={[{ required: true, message: '请输入押金费用' }]}
        >
          <InputNumber
            min={0}
            max={2000}
            precision={0}
            placeholder="请填入"
            style={{ width: '300px' }}
          />
        </Form.Item>
        <Form.Item
          label="启用状态"
          name="state"
          valuePropName="checked"
          required
        >
          <Switch />
        </Form.Item>
        <Form.Item label="备注信息" name="description">
          <TextArea rows={4} maxLength={256} placeholder="请填入" />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4 }}>
          <Space>
            <Button type="primary" htmlType="submit" loading={submiting}>
              保存
            </Button>
            <Button loading={submiting} onClick={() => history.back()}>
              返回
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default TrainingForm;
