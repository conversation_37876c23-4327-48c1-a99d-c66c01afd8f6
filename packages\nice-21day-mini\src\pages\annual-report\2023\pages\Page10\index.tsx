import { CoverView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { EMPTY_KEYWORD, REPORT_2023_KEYWORD_LOCAL_KEY } from '../../contants';
import { IPageProps } from '../../type';
import Canvas, { CANVAS_ID } from '../Page10_canvas';
import styles from './index.module.scss';

const BACKGROUND_WIDTH = 750;
const BACKGROUND_HEIGHT = 1334;

export default (props: IPageProps) => {
  const { user, switchSwiper, reportData } = props;
  const { statistics } = reportData;
  const system = Taro.getSystemInfoSync();
  const displayWidth = Math.min(system.windowWidth, BACKGROUND_WIDTH / 2);
  const displayHeight = (BACKGROUND_HEIGHT * displayWidth) / BACKGROUND_WIDTH;

  // 放缩比例
  const radio = displayWidth / (BACKGROUND_WIDTH / 2);

  const saveImage = () => {
    Taro.canvasToTempFilePath({
      canvasId: CANVAS_ID,
      success: function (res) {
        Taro.previewImage({
          current: res.tempFilePath,
          urls: [res.tempFilePath],
        });
        Taro.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
        });
      },
    });
  };

  const textViewStyle = {
    height: 53 * radio + 'px',
    lineHeight: 53 * radio + 'px',
  };

  return (
    <View className={`${styles.container} flex items-center flex-col`}>
      <Canvas {...props}></Canvas>
      <CoverView
        className={styles.cover}
        style={{ width: displayWidth, height: displayHeight }}
      >
        {/* 飞机 */}
        {/* <View className="absolute top-28px right-40px">
        <Image className="h-232px w-218px" src={require('./assets/fly.svg')} />
      </View> */}
        <View className={styles.keyword} style={{ top: 160 * radio + 'px' }}>
          {Taro.getStorageSync(REPORT_2023_KEYWORD_LOCAL_KEY) || EMPTY_KEYWORD}
        </View>
        <View className={styles.content} style={{ top: 292 * radio + 'px' }}>
          <View
            className={styles.contentText}
            style={{ ...textViewStyle, fontWeight: 'bold' }}
          >
            @{user.nick_name}
          </View>
          <View className={styles.contentText} style={textViewStyle}>
            参加训练营：
            <View className={styles.contentNumber}>
              {statistics.training_joined_count}
            </View>{' '}
            个
          </View>
          <View className={styles.contentText} style={textViewStyle}>
            制定任务：
            <View className={styles.contentNumber}>
              {statistics.task_total_count}
            </View>{' '}
            个
          </View>
          <View className={styles.contentText} style={textViewStyle}>
            累计打卡：
            <View className={styles.contentNumber}>
              {statistics.attendance_total_count}
            </View>{' '}
            次
          </View>
          <View className={styles.contentText} style={textViewStyle}>
            输出文字：
            <View className={styles.contentNumber}>
              {statistics.training_summary_words_total_count +
                statistics.attendance_words_count}
            </View>{' '}
            字
          </View>
          <View
            style={textViewStyle}
            className="text-[28px] font-normal pr-[30px] text-right"
          >
            21天微习惯训练营
          </View>
        </View>
      </CoverView>

      <View className={styles.actions}>
        <View
          className={`${styles.btn} ${styles.viewAgain}`}
          onClick={() => switchSwiper(0)}
        >
          <View className="at-icon at-icon-reload" />
          重温报告
        </View>
        <View className={`${styles.btn} ${styles.share}`} onClick={saveImage}>
          <View className="at-icon at-icon-share" />
          晒晒数据
        </View>
      </View>
    </View>
  );
};
