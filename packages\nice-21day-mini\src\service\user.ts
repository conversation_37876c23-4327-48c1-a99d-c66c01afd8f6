import request from '@/utils/request';
import {
  IPageFactory,
  IQueryUserParams,
  IUser,
} from '@nice-people/nice-21day-shared';

/**
 * 获取社团用户分页列表
 */
export const getUsers = async (params: IQueryUserParams) => {
  return await request.get<IPageFactory<IUser>>(`/users`, params);
};

/**
 * 获取用户个人信息
 */
export const getUserProfile = async (user_id: string) => {
  return await request.get<IUser>(`/users/${user_id}`);
};
