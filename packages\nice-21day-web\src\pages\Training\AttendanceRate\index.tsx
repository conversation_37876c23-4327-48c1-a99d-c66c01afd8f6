import { queryTrainingAttendanceRate } from '@/services';
import { StatisticCard } from '@ant-design/pro-components';
import { ITrainingAttendanceRate } from '@nice-people/nice-21day-shared';
import { useParams } from '@umijs/max';
import { Col, DatePicker, Row, Skeleton } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

const AttendanceRate = () => {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [rate, setRate] = useState<ITrainingAttendanceRate>();
  const [attendanceDate, setAttendanceDate] = useState<string | undefined>();

  useEffect(() => {
    setLoading(true);
    queryTrainingAttendanceRate(params.id!, attendanceDate)
      .then((res) => {
        setRate(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params.id, attendanceDate]);

  return (
    <Skeleton loading={loading}>
      <DatePicker
        format="YYYY-MM-DD"
        value={attendanceDate ? dayjs(attendanceDate) : undefined}
        onChange={(_, val) => setAttendanceDate(val as string)}
        style={{ margin: '20px 0', width: 200 }}
      />

      <div style={{ marginBottom: 4 }}>训练营统计数据</div>
      <Row gutter={10}>
        <Col span={4}>
          <StatisticCard
            bordered={false}
            statistic={{
              title: '报名总人数',
              value: rate?.join_user_count,
            }}
          />
        </Col>
        <Col span={4}>
          <StatisticCard
            statistic={{
              title: '总打卡数',
              value: rate?.total_attendance_logs_count,
            }}
          />
        </Col>
        <Col span={4}>
          <StatisticCard
            statistic={{
              title: '正常打卡数',
              value: rate?.total_attendance_count,
            }}
          />
        </Col>
        <Col span={4}>
          <StatisticCard
            statistic={{
              title: '请假数',
              value: rate?.total_level_count,
            }}
          />
        </Col>
        <Col span={4}>
          <StatisticCard
            statistic={{
              title: '打卡率',
              value: rate?.total_attendance_rate + '%',
            }}
            // chart={
            //   <Progress
            //     width={80}
            //     strokeWidth={10}
            //     type="circle"
            //     percent={percent}
            //   />
            // }
            // chartPlacement="left"
          />
        </Col>
        <Col span={4}>
          <StatisticCard
            statistic={{
              title: '积分达标率',
              value: rate?.total_storard_rate + '%',
            }}
          />
        </Col>
      </Row>
      {attendanceDate && (
        <>
          <div style={{ marginBottom: 4, marginTop: 10 }}>
            {attendanceDate} 统计数据
          </div>

          <Row gutter={10}>
            <Col span={4}>
              <StatisticCard
                statistic={{
                  title: '总打卡数',
                  value: attendanceDate
                    ? rate?.attendance_logs_count
                    : rate?.total_attendance_logs_count,
                }}
              />
            </Col>
            <Col span={4}>
              <StatisticCard
                statistic={{
                  title: '正常打卡数',
                  value: rate?.attendance_count,
                }}
              />
            </Col>
            <Col span={4}>
              <StatisticCard
                statistic={{
                  title: '请假数',
                  value: rate?.level_count,
                }}
              />
            </Col>
            <Col span={4}>
              <StatisticCard
                statistic={{
                  title: '打卡率',
                  value: rate?.attendance_rate + '%',
                }}
                // chart={
                //   <Progress
                //     width={80}
                //     strokeWidth={10}
                //     type="circle"
                //     percent={percent}
                //   />
                // }
                // chartPlacement="left"
              />
            </Col>
          </Row>
        </>
      )}
    </Skeleton>
  );
};

export default AttendanceRate;
