import {
  ETrainingProgress,
  IAttendanceLog,
  IPageFactory,
  IPageParams,
  ITraining,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';
import { request } from '@umijs/max';

export interface IQueryMyTrainingParams extends IPageParams {
  progress?: ETrainingProgress;
  /** 多个训练营进度 */
  progresses?: string;
  attendance_date?: string;
  user_id?: string;
}

/**
 * 获取用户详情
 */
export const getMyTrainingList = async (params: IQueryMyTrainingParams) => {
  return await request<IPageFactory<ITraining>>(`/my/trainings`, { params });
};

/**
 * 用户打卡
 */
export const createMyAttendance = async (
  trainingId: string,
  data: Omit<IAttendanceLog, 'id' | 'user_id'>,
) => {
  return await request<IAttendanceLog>(
    `/my/trainings/${trainingId}/attendances`,
    {
      method: 'post',
      data,
    },
  );
};

/**
 * 编辑用户打卡
 */
export const updateMyrAttendance = async (
  attendanceId: string,
  data: Omit<IAttendanceLog, 'id' | 'user_id'>,
) => {
  return await request<IAttendanceLog>(`/my/attendances/${attendanceId}`, {
    method: 'put',
    data,
  });
};

/**
 * 更新我在某个训练营下的积分
 */
export const refreshMyTrainingScore = async (trainingId: string) => {
  return await request<ITrainingMember>(
    `/my/trainings/${trainingId}/refresh-scores`,
    {
      method: 'POST',
    },
  );
};
