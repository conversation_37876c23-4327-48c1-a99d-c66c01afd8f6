export function drawCircle(
  ctx: CanvasRenderingContext2D,
  { percent, width, r }: { percent: number; width: number; r: number },
) {
  ctx.lineWidth = 2;
  ctx.strokeStyle = '#c4c4c4';
  ctx.lineCap = 'round';

  const totalAngle = Math.PI * 2;
  const size = (width / 2) | 0;
  const inlineR = r || size * 0.6;

  ctx.beginPath();
  ctx.arc(size, size, inlineR, 0, totalAngle);
  ctx.stroke();

  const startAngle = -Math.PI / 2;
  const endAngle = Math.PI * 2 * percent - Math.PI / 2;
  ctx.beginPath();
  ctx.arc(size, size, inlineR, startAngle, endAngle);
  ctx.strokeStyle = '#fff';
  ctx.stroke();
}
