import {
  CalendarOutlined,
  CheckCircleOutlined,
  RightOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import {
  ETrainingProgress,
  formatNumber,
  ITraining,
  trainingAttendanceProgress,
  TRAINING_PROGRESS_MAPPING,
} from '@nice-people/nice-21day-shared';
import { useNavigate } from '@umijs/max';
import { Skeleton } from 'antd';
import { useMemo } from 'react';
import AttendanceGuideButton from '../AttendanceGuideButton';

interface ITrainingProps {
  training: ITraining;
}

const getProgressColor = (progress: ETrainingProgress) => {
  switch (progress) {
    case ETrainingProgress.Finished:
      return 'bg-green-100 text-green-600';
    case ETrainingProgress.Processing:
      return 'bg-blue-100 text-blue-600';
    case ETrainingProgress.Registering:
      return 'bg-blue-100 text-blue-600';
    case ETrainingProgress.Summary:
      return 'bg-orange-100 text-orange-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

export const TrainingSkeleton = () => (
  <div className="bg-white rounded-2xl shadow-sm p-3 cursor-pointer hover:shadow-md transition-shadow">
    <Skeleton.Input
      active
      size="small"
      block
      style={{ marginBottom: 8, width: '50%' }}
    />
    <Skeleton.Input active size="small" block />

    <div className="grid grid-cols-3 gap-4 mt-2 mb-3 w-full">
      {[1, 2, 3].map((item) => (
        <div key={item} className="text-center overflow-hidden">
          <Skeleton.Input active size="small" block />
        </div>
      ))}
    </div>
    <div>
      <Skeleton.Input active size="small" block style={{ width: '70%' }} />
    </div>
  </div>
);

const Training = ({ training }: ITrainingProps) => {
  const navigate = useNavigate();

  // 计算当前打卡进度
  const attendanceProgress = useMemo(() => {
    return trainingAttendanceProgress(training.start_time, training.end_time);
  }, [training.start_time, training.end_time]);

  return (
    <div
      key={training.id}
      onClick={() => navigate(`/training/${training.id}`)}
      className="relative bg-white rounded-2xl shadow-sm p-3 pb-6 cursor-pointer hover:shadow-md transition-shadow overflow-hidden"
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex-1">
          <h4 className="font-bold text-gray-800 mb-1">{training.name}</h4>
          <p className="text-sm text-gray-600">{training.description}</p>
        </div>
        <RightOutlined className="w-5 h-5 text-gray-400" />
      </div>

      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium ${getProgressColor(
              training.progress,
            )}`}
          >
            {TRAINING_PROGRESS_MAPPING[training.progress]}
          </span>
          <div className="flex items-center text-sm text-gray-600">
            <UsergroupAddOutlined className="w-4 h-4 mr-1 text-lg" />
            {formatNumber(training.join_user_count || 0)}人
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <CheckCircleOutlined className="w-4 h-4 mr-1 text-lg" />
            {formatNumber(training.attendance_user_count || 0)}次打卡
          </div>
        </div>
      </div>

      {/* {training.progress === ETrainingProgress.Processing && (
        <div>
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>训练进度</span>
            <span>12/21天</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all"
              // style={{ width: `${(training.progress / 21) * 100}%` }}
            ></div>
          </div>
        </div>
      )} */}

      <div className="flex items-center text-sm text-gray-500">
        <CalendarOutlined className="w-4 h-4 mr-1" />
        {training.start_time} 至 {training.end_time}
      </div>

      {/* 去打卡 */}
      <AttendanceGuideButton training={training} />

      {/* 进度条 */}
      <div className="absolute bottom-0 left-0 w-full overflow-hidden">
        <div className="relative h-2 bg-gray-200">
          {/* 进度条主体 */}
          <div
            className="absolute left-0 top-0 h-full bg-gradient-to-r from-blue-400 to-green-500 transition-all duration-500 ease-out"
            style={{ width: `${attendanceProgress}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default Training;
