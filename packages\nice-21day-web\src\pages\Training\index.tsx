import { PRO_TABLE_DEFAULT_CONFIG } from '@/constants';
import { changeStatus, delTraining, queryTrainings } from '@/services';
import { generateProTableValueEnum } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  ITraining,
  STATE_MAPPING,
  TRAINING_PROGRESS_MAPPING,
  TRAINING_TYPE_MAPPING,
} from '@nice-people/nice-21day-shared';
import { history, Link } from '@umijs/max';
import { Button, Popconfirm, Space } from 'antd';
import { useRef } from 'react';

const Training: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const changeType = (id: string, state: string) => {
    if (state === 'delete') {
      return delTraining(id).then(() => {
        actionRef.current?.reload();
      });
    }
    changeStatus(id, state).then(() => {
      actionRef.current?.reload();
    });
  };
  const renderAction = (text: string, id: string, state: string) => (
    <Popconfirm
      key={`popconfirm_${text}`}
      title={`确认${text}吗?`}
      okText="是"
      cancelText="否"
      onConfirm={() => {
        changeType(id, state);
      }}
    >
      <Button type="link"> {text} </Button>
    </Popconfirm>
  );
  const columns: ProColumns<ITraining>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      valueEnum: generateProTableValueEnum(TRAINING_TYPE_MAPPING),
    },
    {
      title: '第几期',
      dataIndex: 'period',
      key: 'period',
      search: false,
      width: 120,
      renderText: (text) => `第 ${text} 期`,
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      valueEnum: generateProTableValueEnum(TRAINING_PROGRESS_MAPPING),
    },
    {
      title: '起止时间',
      key: 'time',
      dataIndex: 'time-range',
      search: false,
      width: 240,
      renderText: (_, record) => (
        <>
          <div>
            {record.start_time} - {record.end_time}
          </div>
        </>
      ),
    },
    {
      title: '押金',
      key: 'fee',
      dataIndex: 'fee',
      search: false,
      width: 120,
      renderText: (fee) => (fee ? `￥${fee}` : '无'),
    },
    {
      title: '达标积分',
      key: 'standard_score',
      dataIndex: 'standard_score',
      search: false,
      width: 120,
    },
    {
      title: '参与人数',
      key: 'join_user_count',
      dataIndex: 'join_user_count',
      search: false,
      width: 120,
      renderText: (_, record) => {
        return (
          <Link to={`/admin/training/${record.id}/member`}>
            {record.join_user_count}
          </Link>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 120,
      valueEnum: generateProTableValueEnum(STATE_MAPPING),
    },
    {
      title: '备注信息',
      key: 'description',
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      search: false,
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        let state =
          record.state === 'enable'
            ? renderAction('禁用', record.id, 'disable')
            : renderAction('启用', record.id, 'enable');
        let del = renderAction('删除', record.id, 'delete');
        return (
          <Space>
            {state}
            <Button
              type="link"
              onClick={() =>
                history.push(`/admin/training/${record.id}/update`)
              }
            >
              编辑
            </Button>
            {del}
            <Button
              type="link"
              onClick={() =>
                history.push(`/admin/training/${record.id}/member`)
              }
            >
              成员
            </Button>
            <Button
              type="link"
              onClick={() =>
                history.push(`/admin/training/${record.id}/attendance-rate`)
              }
            >
              统计
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable<ITraining>
      headerTitle="训练营列表"
      columns={columns}
      actionRef={actionRef}
      {...PRO_TABLE_DEFAULT_CONFIG}
      request={async ({ pageSize, current, ...rest }) => {
        const res = await queryTrainings({
          ...rest,
          size: pageSize!,
          page: current!,
        });
        return {
          data: res?.rows,
          total: res?.total,
          success: true,
        };
      }}
      rowKey="id"
      scroll={{ x: 'max-content' }}
      toolBarRender={() => [
        <Button
          key="button"
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => history.push('/admin/training/create')}
        >
          新建
        </Button>,
      ]}
    />
  );
};
export default Training;
