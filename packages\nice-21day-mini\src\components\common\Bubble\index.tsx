import { getRandomIntInclusive } from '@/utils';
import { View } from '@tarojs/components';
import React, { memo } from 'react';
import styles from './index.module.scss';

interface IBubbleProps {
  count?: number;
}
const Bubble: React.FC<IBubbleProps> = ({ count = 20 }) => {
  return (
    <View className="absolute w-full h-full">
      {Array.from({ length: count }).map((_, index) => (
        <View
          key={index}
          className={styles.bubble}
          style={{
            borderWidth: `${getRandomIntInclusive(2, 6)}px`,
            left: `${getRandomIntInclusive(-10, 80)}%`,
            animationDelay: `${getRandomIntInclusive(1, 5)}s`,
            animationDuration: `${getRandomIntInclusive(3, 10)}s`,
          }}
        />
      ))}
    </View>
  );
};

export default memo(Bubble);
