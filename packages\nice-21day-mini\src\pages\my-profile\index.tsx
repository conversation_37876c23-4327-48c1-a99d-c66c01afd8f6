import { AppContext } from '@/appContext';
import { Avatar, FabBox, FormField, Title } from '@/components';
import { useUserProfile } from '@/hooks';
import { updateMyProfile } from '@/service';
import {
  EBooleanString,
  IUser,
  USER_GENDER_MAPPING,
  USER_LOVE_STATE_MAPPING,
} from '@nice-people/nice-21day-shared';
import { Picker, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useContext } from 'react';
import {
  AtButton,
  AtInput,
  AtList,
  AtListItem,
  AtLoadMore,
  AtSwitch,
  AtTextarea,
} from 'taro-ui';
import './index.scss';

const MyProfile: React.FC = () => {
  const { currentUser } = useContext(AppContext);

  const { userProfile, setUserProfile, loading } = useUserProfile(
    currentUser.id,
  );

  const changeField = (field: keyof IUser, value: any) => {
    setUserProfile((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const onSubmit = useCallback(() => {
    console.log(userProfile);
    Taro.showModal({
      title: '确定保存吗？',
      success: async (res) => {
        if (res.confirm) {
          // 保存
          await updateMyProfile(userProfile)
            .then(() => {
              Taro.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000,
              });
              Taro.navigateBack();
            })
            .catch(() => {
              Taro.showModal({
                title: '保存失败',
                showCancel: false,
                confirmText: '我知道了',
              });
            });
        }
      },
    });
  }, [userProfile]);

  if (loading) {
    return <AtLoadMore status="loading" />;
  }

  return (
    <View className="my-profile-page">
      <Avatar
        editable
        avatar={userProfile.avatar_url}
        userName={userProfile.nick_name}
      />

      <>
        {/* === 个人信息 === */}
        <Title title="我的资料" />
        <AtList>
          {/* 昵称先不支持修改吧 */}
          {/* <AtInput
            name="nick_name"
            title="昵称"
            type="text"
            value={userProfile.nick_name}
            onChange={(val) => changeField('nick_name', val)}
          /> */}
          <Picker
            mode="selector"
            range={Object.values(USER_GENDER_MAPPING)}
            value={Object.values(USER_GENDER_MAPPING).findIndex(
              (state) => state === userProfile.gender,
            )}
            onChange={(e) => {
              const index = e.detail.value as number;
              changeField('gender', Object.keys(USER_GENDER_MAPPING)[index]);
            }}
          >
            <AtListItem
              title="性别"
              extraText={USER_GENDER_MAPPING[userProfile.gender]}
            />
          </Picker>

          <AtInput
            name="location"
            title="居住地"
            type="text"
            value={userProfile.location}
            placeholder="省份-城市"
            onChange={(val) => changeField('location', val)}
          />

          <Picker
            mode="date"
            value={userProfile.birthday}
            onChange={(e) => changeField('birthday', e.detail.value)}
          >
            <AtListItem
              title="生日"
              note="对外展示时会屏蔽年份"
              extraText={userProfile.birthday}
            />
          </Picker>

          <FormField label="个人爱好">
            <AtTextarea
              value={userProfile.hobby}
              onChange={(val) => changeField('hobby', val)}
              maxLength={100}
              placeholder="请输入个人爱好"
            />
          </FormField>

          <FormField label="个人介绍">
            <AtTextarea
              value={userProfile.about_me}
              onChange={(val) => changeField('about_me', val)}
              maxLength={500}
              placeholder="请输入个人介绍"
            />
          </FormField>
        </AtList>

        {/* === 公司信息 === */}
        <Title title="我的公司" />
        <AtList>
          <FormField label="公司">
            <AtTextarea
              value={userProfile.company}
              onChange={(val) => changeField('company', val)}
              maxLength={50}
              placeholder="请输入公司"
            />
          </FormField>

          <FormField label="职称">
            <AtTextarea
              value={userProfile.job_title}
              onChange={(val) => changeField('job_title', val)}
              maxLength={50}
              placeholder="请输入职称"
            />
          </FormField>

          {/* <View>技能标签</View>
        <AtTextarea
          value={userProfile.skill_tags}
          onChange={(val) => changeField('skill_tags', val)}
          maxLength={50}
          placeholder="请输入技能标签"
        /> */}
        </AtList>

        {/* === 内推 === */}
        <Title title="我的内推" />
        <AtList>
          <AtSwitch
            title="提供内推"
            checked={userProfile.internal_referral_state === EBooleanString.YES}
            onChange={(checked) => {
              changeField(
                'internal_referral_state',
                checked ? EBooleanString.YES : EBooleanString.NO,
              );
            }}
          />

          {userProfile.internal_referral_state === EBooleanString.YES && (
            <FormField label="内推说明">
              <AtTextarea
                value={userProfile.internal_referral_description}
                onChange={(val) =>
                  changeField('internal_referral_description', val)
                }
                maxLength={500}
                placeholder="请输入内推说明"
              />
            </FormField>
          )}
        </AtList>

        {/* === 社交 === */}
        <Title title="我的社交" />
        <AtList>
          <FormField label="个人网站">
            <AtTextarea
              value={userProfile.websites}
              onChange={(val) => changeField('websites', val)}
              maxLength={200}
              placeholder="请输入个人网站"
            />
          </FormField>

          <AtInput
            name="mp_weixin_name"
            title="微信公众号"
            type="text"
            value={userProfile.mp_weixin_name}
            placeholder="请输入微信公众号名称"
            onChange={(val) => changeField('mp_weixin_name', val)}
          />

          <FormField label="Github">
            <AtTextarea
              value={userProfile.github_address}
              onChange={(val) => changeField('github_address', val)}
              maxLength={200}
              placeholder="请输入Github地址"
            />
          </FormField>

          <FormField label="掘金">
            <AtTextarea
              value={userProfile.juejin_address}
              onChange={(val) => changeField('juejin_address', val)}
              maxLength={200}
              placeholder="请输入掘金个人主页地址"
            />
          </FormField>

          <FormField label="知乎">
            <AtTextarea
              value={userProfile.zhihu_address}
              onChange={(val) => changeField('zhihu_address', val)}
              maxLength={200}
              placeholder="请输入知乎个人主页地址"
            />
          </FormField>

          <FormField label="思否">
            <AtTextarea
              value={userProfile.segmentfault_address}
              onChange={(val) => changeField('segmentfault_address', val)}
              maxLength={200}
              placeholder="请输入思否个人主页地址"
            />
          </FormField>
        </AtList>

        {/* 恋爱信息 */}
        <Title title="我的爱情" />
        <AtList>
          <Picker
            mode="selector"
            range={Object.values(USER_LOVE_STATE_MAPPING)}
            value={Object.values(USER_LOVE_STATE_MAPPING).findIndex(
              (state) => state === userProfile.love_state,
            )}
            onChange={(e) => {
              const index = e.detail.value as number;
              changeField(
                'love_state',
                Object.keys(USER_LOVE_STATE_MAPPING)[index],
              );
            }}
          >
            <AtListItem
              title="恋爱状况"
              extraText={USER_LOVE_STATE_MAPPING[userProfile.love_state]}
            />
          </Picker>

          <FormField label="恋爱宣言">
            <AtTextarea
              value={userProfile.love_expectation}
              onChange={(val) => changeField('love_expectation', val)}
              maxLength={500}
              placeholder="请输入恋爱宣言"
            />
          </FormField>
        </AtList>

        <FabBox>
          <AtButton type="primary" circle onClick={onSubmit}>
            保存
          </AtButton>
        </FabBox>
      </>
    </View>
  );
};

export default MyProfile;
