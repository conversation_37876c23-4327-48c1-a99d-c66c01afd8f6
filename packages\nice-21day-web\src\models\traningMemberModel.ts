import { queryTrainingMembers } from '@/services';
import {
  IQueryTrainingMemberParams,
  ITrainingMember,
} from '@nice-people/nice-21day-shared';
import { useCallback, useState } from 'react';

export default () => {
  const [loading, setLoading] = useState(false);
  const [trainingMembers, setTrainingMembers] = useState<ITrainingMember[]>([]);

  const queryAllTrainingMembers = useCallback(
    (params: Omit<IQueryTrainingMemberParams, 'page' | 'size'>) => {
      setLoading(true);

      // TODO: 拉取全部的人员，不分页了
      queryTrainingMembers({ ...params, page: 1, size: 1000 })
        .then((res) => {
          setTrainingMembers(res?.rows ?? []);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [],
  );

  return {
    queryAllTrainingMembers,
    loading,
    trainingMembers,
  };
};
